/**
 * 易代账产品介绍页面
 * 支持左右滑动切换页面、上下滑动查看详情
 */
const navService = require('../../utils/navigator.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品海报信息
    posterTitle: '易代账',
    posterSubtitle: '代账行业专用智能财税平台',
    posterSlogan: '让管理更轻松，代账更简单',
    
    // 触摸相关数据
    touchStartY: 0,
    touchMoveY: 0,
    touchStartX: 0,
    touchMoveX: 0,
    isAnimating: false,
    lastSwipeTime: 0,
    swipeThreshold: 50,
    
    // 页面配置
    activeTab: 1,
    productKey: 'ydz',
    isAtFirstScreen: true,
    
    // 当前活跃的工作流节点
    activeWorkflowNode: 0,
    // 当前应该滚动到的节点ID
    scrollToNodeId: 'node-0',
    // 翻转卡片的索引，-1表示没有卡片被翻转
    flippedCardIndex: -1,
    // 下一个要翻转的卡片索引
    nextCardIndex: 0,
    // 定时器标识
    featureTimer: null,
    // 区域可见性状态
    isFeatureSectionVisible: false,
    featureAnimationTriggered: false,
    testimonialAnimationTriggered: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({ activeTab: 1 });
    
    // 启动各种动画
    this.startWorkflowAnimation();
    this.startFeatureAnimation();
    
    // 创建区域可见性监测器
    this.createIntersectionObservers();
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll: function(e) {
    const scrollTop = e.scrollTop;
    // 如果滚动位置超过一定值（如100px），认为已经离开首屏
    const isAtFirstScreen = scrollTop < 100;
    
    // 只有当状态需要变化时才更新，减少不必要的setData
    if (isAtFirstScreen !== this.data.isAtFirstScreen) {
      this.setData({
        isAtFirstScreen: isAtFirstScreen
      });
    }
  },

  /**
   * 创建区域可见性监测器
   */
  createIntersectionObservers: function() {
    // 创建功能区域监测器
    this.featureObserver = wx.createIntersectionObserver(this, {
      // 启用nativeMode以提高性能
      observeAll: true,
      thresholds: [0, 0.5, 1],
      initialRatio: 0,
      nativeMode: true
    });
    
    this.featureObserver
      .relativeToViewport()
      .observe('.feature-showcase', (res) => {
        const isVisible = res.intersectionRatio > 0;
        
        // 如果可见性状态改变
        if (isVisible !== this.data.isFeatureSectionVisible) {
          this.setData({
            isFeatureSectionVisible: isVisible
          });
          
          // 根据可见性状态调整动画
          if (isVisible) {
            this.startFeatureAnimation();
          } else if (this.featureTimer) {
            clearInterval(this.featureTimer);
            this.featureTimer = null;
            
            // 如果离开区域，恢复所有卡片状态
            if (this.data.flippedCardIndex !== -1) {
              this.setData({
                flippedCardIndex: -1
              });
            }
          }
        }
      });
  },

  /**
   * 启动工作流节点动画
   */
  startWorkflowAnimation: function() {
    // 每2秒切换一次活跃节点
    this.workflowTimer = setInterval(() => {
      let nextNode = (this.data.activeWorkflowNode + 1) % 6;
      this.setData({
        activeWorkflowNode: nextNode,
        scrollToNodeId: 'node-' + nextNode
      });
    }, 2000);
  },

  /**
   * 启动功能卡片自动翻转
   */
  startFeatureAnimation: function() {
    // 如果已经有定时器或区域不可见，不重复创建
    if (this.featureTimer || !this.data.isFeatureSectionVisible) {
      return;
    }
    
    // 定义翻转状态的函数
    const flipCard = () => {
      let currentIndex = this.data.flippedCardIndex;
      
      if (currentIndex === -1) {
        // 如果当前没有卡片翻转，则翻转下一张
        let nextIndex = this.data.nextCardIndex;
        
        this.setData({
          flippedCardIndex: nextIndex,
          nextCardIndex: (nextIndex + 1) % 6  // 循环0-5
        });
        
        // 翻转到文字面后，停留4秒再翻回
        this.featureTimer = setTimeout(() => {
          this.setData({
            flippedCardIndex: -1
          });
          
          // 翻回正面后，2秒后再翻下一张
          this.featureTimer = setTimeout(flipCard, 2000);
        }, 4000); // 文字面停留4秒
      } else {
        // 这个分支通常不会执行，因为我们在上面已经安排了翻转回正面
        // 但保留它以防逻辑需要
        this.setData({
          flippedCardIndex: -1
        });
        
        // 2秒后继续
        this.featureTimer = setTimeout(flipCard, 2000);
      }
    };
    
    // 开始翻转循环
    this.featureTimer = setTimeout(flipCard, 1000);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 如果定时器不存在，且对应区域可见，重新启动
    if (!this.workflowTimer) {
      this.startWorkflowAnimation();
    }
    
    if (this.data.isFeatureSectionVisible && !this.featureTimer) {
      this.startFeatureAnimation();
    }
    
    // 如果观察器不存在，重新创建
    if (!this.featureObserver) {
      this.createIntersectionObservers();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 页面隐藏时清除定时器
    if (this.workflowTimer) {
      clearInterval(this.workflowTimer);
      this.workflowTimer = null;
    }
    if (this.featureTimer) {
      clearInterval(this.featureTimer);
      this.featureTimer = null;
    }
    
    // 断开观察器连接
    this.disconnectObservers();
  },
  
  /**
   * 断开观察器连接
   */
  disconnectObservers: function() {
    if (this.featureObserver) {
      this.featureObserver.disconnect();
      this.featureObserver = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除定时器
    if (this.workflowTimer) {
      clearInterval(this.workflowTimer);
      this.workflowTimer = null;
    }
    if (this.featureTimer) {
      clearInterval(this.featureTimer);
      this.featureTimer = null;
    }
    
    // 断开观察器连接
    this.disconnectObservers();
  },

  /**
   * 处理顶部导航切换
   */
  handleTabChange: function(e) {
    navService.handleNavBarTabChange(e);
  },
  
  /**
   * 处理参与促销活动
   */
  handleJoinPromo: function(e) {
    const { productKey } = e.detail;
    console.log(`[pdydz page] 接收到 joinpromo 事件, productKey: ${productKey}`);
    if (productKey) {
      navService.navigateToVersionPage(productKey);
    }
  },

  /**
   * 触摸开始事件处理
   */
  onTouchStart: function(e) {
    this.setData({
      touchStartY: e.changedTouches[0].clientY,
      touchStartX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY,
      touchMoveX: e.changedTouches[0].clientX
    });
  },

  /**
   * 触摸移动事件处理
   */
  onTouchMove: function(e) {
    if (this.data.isAnimating) return;
    
    this.setData({
      touchMoveX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY
    });
  },

  /**
   * 触摸结束事件处理
   */
  onTouchEnd: function(e) {
    const { 
      touchStartX, 
      touchMoveX,
      touchStartY,
      touchMoveY,
      isAnimating,
      swipeThreshold,
      lastSwipeTime,
      productKey,
      isAtFirstScreen
    } = this.data;
    
    if (isAnimating) return;
    
    const moveX = touchStartX - touchMoveX;
    const moveY = touchStartY - touchMoveY;
    const now = Date.now();
    
    if (now - lastSwipeTime < 300) return;
    this.setData({ lastSwipeTime: now });
    
    // 只有在首屏且为水平滑动时才允许切换页面
    if (isAtFirstScreen && Math.abs(moveX) > Math.abs(moveY) && Math.abs(moveX) > swipeThreshold) {
      const direction = moveX > 0 ? 'left' : 'right';
      this.setData({ isAnimating: true });
      
      // 使用导航服务切换产品页面
      navService.switchProductPage(productKey, direction);
      
      // 动画结束后重置状态
      setTimeout(() => {
        this.setData({ isAnimating: false });
      }, 350);
    }
  },

  /**
   * 导航到更多页面
   */
  navigateToMore: function() {
    navService.navigateToProductService(this.data.productKey, 'consult');
  },

  navigateToVersionhkj: function(e) {
    navService.navigateToVersionPage('ydz');
  },

  /**
   * 打电话
   */
  makePhoneCall: function() {
    navService.makePhoneCall();
  },

  /**
   * 处理功能卡片翻转
   */
  toggleCardFlip: function(e) {
    const cardIndex = e.currentTarget.dataset.index;
    const currentFlipped = this.data.flippedCardIndex;
    
    // 清除现有的翻转定时器
    if (this.featureTimer) {
      clearTimeout(this.featureTimer);
      this.featureTimer = null;
    }
    
    // 如果点击的是当前已翻转的卡片，则恢复原状
    if(currentFlipped === cardIndex) {
      this.setData({
        flippedCardIndex: -1
      });
    } else {
      // 否则翻转新点击的卡片，并更新下一个要翻转的卡片为当前点击卡片的下一个
      this.setData({
        flippedCardIndex: cardIndex,
        nextCardIndex: (cardIndex + 1) % 6
      });
    }
    
    // 延迟重启自动翻转
    this.featureTimer = setTimeout(() => {
      this.featureTimer = null;
      this.startFeatureAnimation();
    }, 4000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '易代账 - 专业代账软件，提80%代账效率',
      path: '/pages/pdydz/pdydz',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '易代账 - 专业代账软件，提80%代账效率',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  }
}); 