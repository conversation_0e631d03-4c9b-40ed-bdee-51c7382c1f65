Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetDetail-BYBTooWW.js:1  Uncaught (in promise) xt
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetDetail-BYBTooWW.js:1  Uncaught (in promise) xt
s2 @ index-D82_sypt.js:20
b @ index-D82_sypt.js:20
ProductOrderForm-DMXrA0rN.js:1 监听到价格字段变化: Object
ProductOrderForm-DMXrA0rN.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ProductOrderForm-DMXrA0rN.js:1 初始化新产品订单
OrderStatusBadge-DPzPtkW-.js:1 订单编码生成成功: PO20250731WIE
OrderStatusBadge-DPzPtkW-.js:1 OrderHeader updateParent 被调用，当前数据: Object
ProductOrderForm-DMXrA0rN.js:1 表头数据更新: Object
ProductOrderForm-DMXrA0rN.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DMXrA0rN.js:1 更新后的订单金额: Object
ProductOrderForm-DMXrA0rN.js:1 监听到价格字段变化: Object
ProductOrderForm-DMXrA0rN.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ServiceOrderForm-C9pBN4jm.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-C9pBN4jm.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-C9pBN4jm.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-C9pBN4jm.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-C9pBN4jm.js:1 初始化新服务订单
OrderStatusBadge-DPzPtkW-.js:1 订单编码生成成功: SO20250731AUK
OrderStatusBadge-DPzPtkW-.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-C9pBN4jm.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-C9pBN4jm.js:1 服务明细数据: Proxy(Array)
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetDetail-BYBTooWW.js:1  Uncaught (in promise) xt
s2 @ index-D82_sypt.js:20
b @ index-D82_sypt.js:20
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetChangeCreate-Bh8TyY00.js:1  加载资产数据失败: xt
B @ AssetChangeCreate-Bh8TyY00.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetForm-BY_joV3G.js:1  加载资产数据失败: xt
G @ AssetForm-BY_joV3G.js:1
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetDetail-BYBTooWW.js:1  Uncaught (in promise) xt
service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取所有资产变更记录失败
(匿名) @ index-D82_sypt.js:59
AssetChangeList-DapEsdZu.js:1  获取变更列表失败: xt
i @ AssetChangeList-DapEsdZu.js:1
service.bogoo.net/api/assets/1:1   Failed to load resource: the server responded with a status of 500 (Internal Server Error)
index-D82_sypt.js:59  API Error:  获取资产详情失败
(匿名) @ index-D82_sypt.js:59
AssetChangeCreate-Bh8TyY00.js:1  加载资产数据失败: xt
B @ AssetChangeCreate-Bh8TyY00.js:1
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息


4|customer | 2025-07-31 16:00:23 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 16:00:23 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 16:00:24 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 16:00:24 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 16:10:29 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 16:10:29 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 16:10:30 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 16:10:30 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 16:20:07 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 16:20:07 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 16:20:08 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 16:20:08 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 19:24:16 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 19:24:16 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 19:24:17 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 19:24:17 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 19:49:02 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 19:49:02 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 19:49:03 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 19:49:03 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 20:05:15 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 20:05:15 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 20:05:16 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 20:05:16 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-31 20:15:03 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-31 20:15:03 +08:00: 数据库连接已关闭
4|customer | 2025-07-31 20:15:03 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-31 20:15:03 +08:00: 服务器正在端口 3002 上运行.

4|customer-backend  | 2025-07-31 20:15:21 +08:00: 获取资产详情时出错: Error
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     at async AssetChangeLog.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     at async exports.getAssetById (/var/www/customer_system/backend/src/controllers/asset.controller.js:209:28) {
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:21 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:21 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:21 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:15:21 +08:00: }
4|customer-backend  | 2025-07-31 20:15:22 +08:00: 获取所有资产变更记录时出错: Error
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at async AssetChangeLog.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at async Promise.all (index 1)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at async AssetChangeLog.findAndCountAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1322:27)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     at async exports.getAllAssetChanges (/var/www/customer_system/backend/src/controllers/asset.controller.js:427:29) {
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:22 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   sql: 'SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`name` AS `creator.name` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` ORDER BY `AssetChangeLog`.`createdAt` DESC LIMIT 0, 20;',
4|customer-backend  | 2025-07-31 20:15:22 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:15:22 +08:00: }
4|customer-backend  | 2025-07-31 20:15:28 +08:00: 获取资产详情时出错: Error
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     at Query.run (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:315:28
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     at async MySQLQueryInterface.select (/var/www/customer_system/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:407:12)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     at async AssetChangeLog.findAll (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1140:21)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     at async exports.getAssetById (/var/www/customer_system/backend/src/controllers/asset.controller.js:209:28) {
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   name: 'SequelizeDatabaseError',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   parent: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   original: Error: Out of sort memory, consider increasing server sort buffer size
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Query.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Socket.emit (node:events:524:28)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at addChunk (node:internal/streams/readable:561:12)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
4|customer-backend  | 2025-07-31 20:15:28 +08:00:       at Readable.push (node:internal/streams/readable:392:5) {
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     code: 'ER_OUT_OF_SORTMEMORY',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     errno: 1038,
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sqlState: 'HY001',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sqlMessage: 'Out of sort memory, consider increasing server sort buffer size',
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:28 +08:00:     parameters: undefined
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   },
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   sql: "SELECT `AssetChangeLog`.`id`, `AssetChangeLog`.`asset_change_id`, `AssetChangeLog`.`change_date`, `AssetChangeLog`.`asset_id`, `AssetChangeLog`.`snapshot_before`, `AssetChangeLog`.`snapshot_after`, `AssetChangeLog`.`remark`, `AssetChangeLog`.`related_order_ids`, `AssetChangeLog`.`creator_id`, `AssetChangeLog`.`createdAt`, `AssetChangeLog`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset_change_log` AS `AssetChangeLog` LEFT OUTER JOIN `employee` AS `creator` ON `AssetChangeLog`.`creator_id` = `creator`.`id` WHERE `AssetChangeLog`.`asset_id` = '1' ORDER BY `AssetChangeLog`.`change_date` DESC LIMIT 50;",
4|customer-backend  | 2025-07-31 20:15:28 +08:00:   parameters: {}
4|customer-backend  | 2025-07-31 20:15:28 +08:00: }
