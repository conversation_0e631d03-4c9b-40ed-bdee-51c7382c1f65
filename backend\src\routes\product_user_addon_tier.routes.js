// 引入 Express 框架，我们需要用它的 Router 来定义路由
const express = require('express');
// 创建一个新的路由实例
const router = express.Router();

// 引入对应的控制器文件
const tierController = require('../controllers/product_user_addon_tier.controller');
// 引入认证中间件
const { employee } = require('../middleware/auth');

/**
 * @description 定义与产品用户数增购阶梯价格相关的 API 路由
 */

// --- 路由定义 ---

// 路由: GET /api/products/:productId/tiers
// 作用: 获取指定产品的所有阶梯价格
// 备注: 此接口通常不需要权限，因为价格信息可能需要公开展示
router.get('/products/:productId/tiers', tierController.getTiersByProductId);

// 路由: POST /api/products/:productId/tiers
// 作用: 为指定产品批量创建或更新阶梯价格
router.post(
  '/products/:productId/tiers',
  employee.verifyEmployee,
  tierController.bulkCreateOrUpdateTiers
);

// --- 导出路由 ---
// 将定义好的路由导出，以便在主应用文件 (index.js) 中使用
module.exports = router; 