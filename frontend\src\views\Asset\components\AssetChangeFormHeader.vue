<template>
  <div class="asset-change-form-header" v-if="formData">
    <!-- 资产变更表单表头组件 - 处理资产变更ID、变更时间、制单人、制单时间、变更备注 -->
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px" :disabled="readonly" class="responsive-form">
      <div class="change-header-grid">
        <!-- 资产变更ID -->
        <el-form-item label="资产变更ID" prop="asset_change_id" class="field-change-id">
          <el-input
            v-model="formData.asset_change_id"
            placeholder="自动生成"
            :readonly="readonly"
          />
        </el-form-item>

        <!-- 变更日期 -->
        <el-form-item label="变更日期" prop="change_date" class="field-change-date">
          <el-date-picker
            v-model="formData.change_date"
            type="date"
            placeholder="选择变更日期"
            :disabled="readonly"
          />
        </el-form-item>

        <!-- 制单人 -->
        <el-form-item label="制单人" prop="creator_name" class="field-creator">
          <el-input
            v-model="creatorName"
            placeholder="当前登录员工"
            readonly
          />
        </el-form-item>

        <!-- 制单时间 -->
        <el-form-item label="制单时间" prop="createdAt" class="field-create-time">
          <el-input
            v-model="formattedCreateTime"
            placeholder="创建时自动填充"
            readonly
          />
        </el-form-item>
      </div>

      <!-- 变更备注 -->
      <div class="remark-section">
        <el-form-item label="变更备注" prop="remark" class="field-remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入变更备注，系统会自动生成变更字段信息"
            :readonly="readonly"
            maxlength="500"
            show-word-limit
          />

          <!-- 自动生成变更信息的提示 -->
          <div v-if="!readonly" class="remark-tips">
            <el-alert
              title="备注说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul class="tips-list">
                  <li>系统会自动检测变更的字段并生成变更信息</li>
                  <li>您可以在自动生成的基础上添加额外的备注说明</li>
                  <li>变更信息格式：字段名：原值 → 新值</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </el-form-item>
      </div>


    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useAuth } from '@/store/auth.js'
import { formatDateTime } from '@/utils/format.js'

// Props定义
const props = defineProps({
  // 表单数据对象
  formData: {
    type: Object,
    default: null
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  // 原始资产数据（用于生成变更备注）
  originalData: {
    type: Object,
    default: null
  },
  // 变更后的资产数据（用于生成变更备注）
  changedData: {
    type: Object,
    default: null
  }
})

// 事件定义
const emit = defineEmits(['data-change'])

// 获取认证信息
const { state: authState } = useAuth()

// 表单引用
const formRef = ref(null)

// 表单验证规则
const formRules = {
  asset_change_id: [
    { required: true, message: '资产变更ID不能为空', trigger: 'blur' }
  ],
  change_date: [
    { required: true, message: '请选择变更日期', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '变更备注不能为空', trigger: 'blur' }
  ]
}

// 计算属性：制单人姓名
const creatorName = computed(() => {
  if (props.formData.creator) {
    return props.formData.creator.name || props.formData.creator.employee_name || '未知'
  }
  if (authState.user) {
    return authState.user.name || authState.user.employee_name || '当前用户'
  }
  return '未知'
})

// 计算属性：格式化的创建时间
const formattedCreateTime = computed(() => {
  if (props.formData.createdAt) {
    return formatDateTime(props.formData.createdAt)
  }
  return formatDateTime(new Date())
})

// 生成变更备注信息
const generateChangeRemark = () => {
  if (!props.originalData || !props.changedData) {
    return
  }

  const changes = []
  const fieldLabels = {
    enterprise_id: '关联企业',
    user_id: '关联用户',
    product_id: '产品',
    user_count: '使用人数',
    account_count: '账套数',
    duration_months: '购买时长',
    selected_features: '产品功能',
    purchase_date: '购买日期',
    product_expiry_date: '产品到期日',
    sps_expiry_date: 'SPS到期日',
    after_sales_expiry_date: '服务到期日',
    product_standard_price: '产品标准价',
    sps_annual_fee: 'SPS年费',
    after_sales_service_fee: '售后服务费用',
    implementation_fee: '实施费用',
    activation_code: '激活码',
    activation_phone: '激活手机号',
    activation_password: '激活密码',
    status: '资产状态'
  }

  // 检测变更的字段
  Object.keys(fieldLabels).forEach(field => {
    const oldValue = props.originalData[field]
    const newValue = props.changedData[field]
    
    // 特殊处理不同类型的字段
    if (field === 'features') {
      // 数组类型字段的比较
      const oldFeatures = Array.isArray(oldValue) ? oldValue : []
      const newFeatures = Array.isArray(newValue) ? newValue : []
      if (JSON.stringify(oldFeatures.sort()) !== JSON.stringify(newFeatures.sort())) {
        changes.push(`${fieldLabels[field]}：${oldFeatures.length}项 → ${newFeatures.length}项`)
      }
    } else if (field.includes('date')) {
      // 日期类型字段的比较
      const oldDate = oldValue ? new Date(oldValue).toLocaleDateString() : '未设置'
      const newDate = newValue ? new Date(newValue).toLocaleDateString() : '未设置'
      if (oldDate !== newDate) {
        changes.push(`${fieldLabels[field]}：${oldDate} → ${newDate}`)
      }
    } else if (field.includes('price') || field.includes('fee')) {
      // 价格类型字段的比较
      const oldPrice = oldValue ? `¥${Number(oldValue).toFixed(2)}` : '¥0.00'
      const newPrice = newValue ? `¥${Number(newValue).toFixed(2)}` : '¥0.00'
      if (oldPrice !== newPrice) {
        changes.push(`${fieldLabels[field]}：${oldPrice} → ${newPrice}`)
      }
    } else {
      // 普通字段的比较
      if (oldValue !== newValue) {
        const oldStr = oldValue || '未设置'
        const newStr = newValue || '未设置'
        changes.push(`${fieldLabels[field]}：${oldStr} → ${newStr}`)
      }
    }
  })

  // 生成变更备注
  if (changes.length > 0 && props.formData) {
    // 横向排列变更记录，用空格和分号分隔
    const autoRemark = `本次变更涉及以下字段：${changes.join('  ； ')}`

    // 获取用户手动输入的备注部分（排除自动生成的部分）
    let userRemark = ''
    if (props.formData.remark) {
      const autoRemarkIndex = props.formData.remark.indexOf('本次变更涉及以下字段：')
      if (autoRemarkIndex > 0) {
        userRemark = props.formData.remark.substring(0, autoRemarkIndex).trim()
      } else if (autoRemarkIndex === -1) {
        userRemark = props.formData.remark
      }
    }

    // 重新组合备注：用户备注 + 自动生成的变更信息
    if (userRemark) {
      props.formData.remark = `${userRemark}\n\n${autoRemark}`
    } else {
      props.formData.remark = autoRemark
    }

    // 注意：不在这里触发emit，避免递归
    // emit('data-change')
  }
}

// 防止无限递归的标志
const isGeneratingRemark = ref(false)

// 监听数据变化，自动生成变更备注
watch([() => props.originalData, () => props.changedData], () => {
  if (!props.readonly && props.originalData && props.changedData && props.formData && !isGeneratingRemark.value) {
    // 使用nextTick确保数据完全更新后再生成备注
    nextTick(() => {
      isGeneratingRemark.value = true
      try {
        generateChangeRemark()
      } finally {
        isGeneratingRemark.value = false
      }
    })
  }
}, { deep: true, immediate: false })

// 监听表单数据变化（排除备注字段的自动变更）
watch(() => props.formData, (newData, oldData) => {
  if (props.formData && !isGeneratingRemark.value) {
    emit('data-change')
  }
}, { deep: true })

// 表单验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单验证
const resetValidation = () => {
  formRef.value?.resetFields()
}

// 初始化制单人信息
onMounted(() => {
  if (props.formData) {
    if (!props.formData.creator && authState.user) {
      props.formData.creator = authState.user
    }

    if (!props.formData.createdAt) {
      props.formData.createdAt = new Date()
    }
  }
})

// 暴露方法给父组件
defineExpose({
  validate,
  resetValidation,
  generateChangeRemark
})
</script>

<style scoped>
.asset-change-form-header {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #e1f5fe;
}

.el-form-item {
  margin-bottom: 16px;
}

.remark-tips {
  margin-top: 10px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.tips-list li {
  margin-bottom: 6px;
  line-height: 1.4;
}



/* 响应式Grid布局 */
.asset-change-form-header {
  container-type: inline-size;
}

/* 变更表头Grid - 允许换行 */
.change-header-grid {
  display: grid;
  gap: 16px 20px;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.field-change-id { min-width: 200px; } /* 变更ID增加宽度 */
.field-change-date { min-width: 220px; } /* 变更日期增加宽度 */
.field-creator { min-width: 180px; } /* 制单人增加宽度 */
.field-create-time { min-width: 220px; } /* 制单时间增加宽度 */

/* 备注区域 */
.remark-section {
  margin-top: 16px;
}

.field-remark {
  margin-bottom: 0;
}

/* 输入框宽度优化 - 设置具体最小宽度 */
.responsive-form .el-input,
.responsive-form .el-date-picker,
.responsive-form .el-textarea {
  width: 100%;
  min-width: 180px; /* 基础输入框最小宽度 */
}

/* 特殊字段的输入框宽度 */
.field-change-id .el-input {
  min-width: 200px;
}

.field-change-date .el-date-picker {
  min-width: 220px; /* 日期选择器需要更多空间 */
}

.field-creator .el-input {
  min-width: 180px;
}

.field-create-time .el-input {
  min-width: 220px; /* 时间显示需要更多空间 */
}

/* 窄屏适配 */
@media (max-width: 1200px) {
  .change-header-grid {
    grid-template-columns: repeat(2, minmax(160px, 1fr));
    grid-template-areas:
      "change-id change-date"
      "creator create-time";
  }
}

@media (max-width: 600px) {
  .change-header-grid {
    grid-template-columns: 1fr;
    grid-template-areas:
      "change-id"
      "change-date"
      "creator"
      "create-time";
  }
}

/* 只读模式样式调整 */
.el-form--disabled .el-input__inner,
.el-form--disabled .el-select .el-input__inner,
.el-form--disabled .el-textarea__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

/* 变更表头特殊样式 */
.asset-change-form-header .el-form-item__label {
  color: #1976d2;
  font-weight: 600;
}
</style>
