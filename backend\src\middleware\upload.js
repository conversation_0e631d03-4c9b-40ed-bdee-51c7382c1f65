const multer = require('multer');
const path = require('path');

// 设置 multer 的存储引擎
const storage = multer.diskStorage({
  // 定义文件存储的目的地
  destination: function (req, file, cb) {
    // __dirname 是当前文件所在的目录，'../../uploads/' 表示上两级目录下的 uploads 文件夹
    // 确保这个文件夹存在，如果不存在需要手动创建
    cb(null, path.join(__dirname, '../../uploads/'));
  },
  // 定义存储的文件名
  filename: function (req, file, cb) {
    // 关键修复：正确解码中文文件名
    // multer 默认以 latin1 编码接收文件名，我们需要将其转回 utf8
    const decodedFilename = Buffer.from(file.originalname, 'latin1').toString('utf8');

    // 为了避免文件名冲突，仍然保留时间戳前缀
    const uniquePrefix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const finalFilename = uniquePrefix + '-' + decodedFilename;

    // 将解码后的干净文件名附加到 req 对象上，以便控制器层可以轻松获取
    req.attachment_filename = decodedFilename;
    // 将最终在服务器上存储的、带前缀的文件名附加到 req 对象上
    req.attachment_path = `uploads/${finalFilename}`;

    cb(null, finalFilename);
  }
});

// 创建 multer 实例
const upload = multer({ storage: storage });

module.exports = upload; 