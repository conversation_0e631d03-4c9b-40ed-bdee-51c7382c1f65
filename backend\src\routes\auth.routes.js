const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const { verifyToken } = require('../middleware/auth');

// 定义员工登录路由
// POST /api/auth/employee/login
router.post('/employee/login', authController.employeeLogin);

// [!] 新增：定义员工修改自己密码的路由
// PUT /api/auth/change-password
// 这个路由会先经过 verifyToken 中间件的校验，只有合法的、已登录的用户才能访问
router.put('/change-password', [verifyToken], authController.changePassword);

// [!] 新增：定义验证管理员密码的路由
// POST /api/auth/verify-password
router.post('/verify-password', [verifyToken], authController.verifyPassword);

// [新增] 获取当前用户信息
// GET /api/auth/me
router.get('/me', [verifyToken], authController.getCurrentUser);

// [重构] 2种合规登录方式 - 统一接口
// 1. 账号密码登录
// POST /api/auth/password-login
router.post('/password-login', authController.passwordLogin);

// 2. 微信手机号快速验证
// POST /api/auth/phone-login
router.post('/phone-login', authController.phoneLogin);

// [新增] 获取合伙人收益统计
// GET /api/auth/partner/earnings
router.get('/partner/earnings', [verifyToken], authController.getPartnerEarnings);

module.exports = router;