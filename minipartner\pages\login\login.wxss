/* 登录页面全局样式 */
.login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(135deg, #0FB9B1, #1ed4cb);
  overflow: hidden;
}

/* 波浪背景效果 */
.wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 40%;
}

.wave1 {
  bottom: -70%;
  left: -50%;
  background: rgba(255, 255, 255, 0.1);
  animation: wave-animation 10s infinite linear;
}

.wave2 {
  bottom: -80%;
  left: -50%;
  background: rgba(255, 255, 255, 0.15);
  animation: wave-animation 7s infinite linear;
}

@keyframes wave-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Logo样式 */
.logo-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 250rpx;
  z-index: 5;
}

.logo-image {
  width: 180rpx;
  height: 180rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 登录表单 */
.login-form {
  width: 100%;
  flex: 1;
  margin-top: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 5;
  padding: 0 50rpx;
  box-sizing: border-box;
}

.form-header {
  width: 100%;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 15rpx;
}

.form-subtitle {
  font-size: 32rpx;
  color: rgb(255, 255, 255);
}

/* 输入框容器 */
.input-container {
  width: 100%;
  margin-bottom: 30rpx;
}

.input-wrapper {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  padding: 16rpx 10rpx 16rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
}

.input-wrapper:hover, 
.input-wrapper:focus-within {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.input-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

/* 使用CSS绘制简单图标 */
.icon-phone {
  width: 40rpx;
  height: 40rpx;
  border: 2px solid #fff;
  border-radius: 8rpx;
  position: relative;
}

.icon-phone:after {
  content: '';
  position: absolute;
  width: 15rpx;
  height: 15rpx;
  background: #fff;
  border-radius: 50%;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
}

.icon-lock {
  width: 38rpx;
  height: 38rpx;
  border: 2px solid #fff;
  border-radius: 8rpx;
  position: relative;
}

.icon-lock:before {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2px solid #fff;
  border-radius: 50%;
  top: -15rpx;
  left: 50%;
  transform: translateX(-50%);
}

.input-field {
  flex: 1;
  height: 70rpx;
  font-size: 30rpx;
  color: #fff;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 94rpx;
  background: #fff;
  color: #0FB9B1;
  font-size: 34rpx;
  font-weight: bold;
  border-radius: 47rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 50rpx;
  position: relative;
  overflow: hidden;
}

.login-btn:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
}

.login-btn:active:after {
  transform: translateX(100%);
  transition: transform 0.5s ease;
}

/* 微信登录按钮样式 */
.wechat-login-btn {
  width: 100%;
  height: 94rpx;
  background: linear-gradient(135deg, #07c160 0%, #00d976 100%);
  color: white;
  border: none;
  border-radius: 47rpx;
  font-size: 34rpx;
  font-weight: 600;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(7, 193, 96, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.wechat-login-btn:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.4);
}

.wechat-login-btn:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
}

.wechat-login-btn:active:after {
  transform: translateX(100%);
  transition: transform 0.5s ease;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wechat {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 34rpx;
  font-weight: 600;
}

/* 底部链接 */
.bottom-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.action-link {
  font-size: 28rpx;
  color: rgb(255, 255, 255);
  padding: 10rpx;
}



/* 错误提示 */
.error-tip {
  width: 100%;
  display: flex;
  align-items: center;
  background: rgba(255, 77, 79, 0.1);
  border-left: 6rpx solid #ff4d4f;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.error-tip icon {
  margin-right: 10rpx;
  flex-shrink: 0;
}

.error-tip text {
  color: #ff4d4f;
  font-size: 26rpx;
}

/* 占位符样式 */
.input-field::placeholder {
  color: rgba(255, 255, 255, 0.6);
} 