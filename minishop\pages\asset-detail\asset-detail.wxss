/* pages/asset-detail/asset-detail.wxss */

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left, .navbar-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 36rpx;
}

.nav-icon {
  font-size: 32rpx;
  font-weight: 600;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 内容容器 */
.content-container {
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx) + 32rpx);
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.info-card.main-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 32rpx;
}

.info-card.main-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.product-info-main {
  flex: 1;
}

.product-name {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.asset-id {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.asset-status {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  background: #52c41a;
  animation: pulse 2s infinite;
}

.asset-status.expired .status-dot {
  background: #ff4d4f;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 产品详情 */
.product-details {
  position: relative;
  z-index: 1;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 16rpx;
}

.detail-icon .iconfont {
  font-size: 24rpx;
  color: white;
}

.detail-icon-text {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.detail-value {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-title .iconfont {
  margin-right: 12rpx;
  font-size: 32rpx;
  color: #667eea;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 产品信息 */
.product-info {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.info-row .value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 使用信息网格 */
.usage-grid {
  display: flex;
  justify-content: space-around;
  gap: 24rpx;
}

.usage-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #e8f0ff;
  position: relative;
  overflow: hidden;
}

.usage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.usage-number {
  font-size: 52rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.usage-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 购买信息 */
.purchase-info {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.info-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin-right: 16rpx;
  font-size: 24rpx;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

/* 产品功能列表 */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #667eea;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: 600;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.feature-price {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 600;
}

/* 无功能信息提示 */
.no-features {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #999;
}

.no-features-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.no-features-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.no-features-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 到期信息列表 */
.expiry-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.expiry-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #fafbfc;
  border-radius: 16rpx;
  border-left: 6rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.expiry-item:hover {
  transform: translateX(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.expiry-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  width: 200rpx;
  display: flex;
  align-items: center;
}

.expiry-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.expiry-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  font-weight: 500;
}

.expiry-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  white-space: nowrap;
}

.expiry-status.normal {
  background: linear-gradient(135deg, #e8f5e8 0%, #d9f7be 100%);
  color: #52c41a;
  border-left-color: #52c41a;
}

.expiry-status.warning {
  background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);
  color: #fa8c16;
  border-left-color: #fa8c16;
}

.expiry-status.expired {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  color: #ff4d4f;
  border-left-color: #ff4d4f;
}

.expiry-item.normal {
  border-left-color: #52c41a;
}

.expiry-item.warning {
  border-left-color: #fa8c16;
}

.expiry-item.expired {
  border-left-color: #ff4d4f;
}

/* 价格信息列表 */
.price-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #fff8f0 0%, #fff2e8 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #ff6b35;
  position: relative;
  overflow: hidden;
}

.price-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 107, 53, 0.1) 100%);
}

.price-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.price-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #ff6b35;
  position: relative;
  z-index: 1;
}

/* 价格汇总 */
.price-summary {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  border-radius: 16rpx;
  color: white;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-label {
  font-size: 30rpx;
  font-weight: 600;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 700;
}

/* 激活信息列表 */
.activation-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.activation-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-radius: 16rpx;
  border: 2rpx solid #e6f3ff;
  position: relative;
  overflow: hidden;
}

.activation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
}

.activation-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.activation-label::after {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #667eea;
  margin-left: 8rpx;
}

.activation-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.8);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  margin: 0 16rpx;
  letter-spacing: 2rpx;
}

.copy-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.copy-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.copy-icon {
  font-size: 24rpx;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
  padding: 32rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #667eea;
  position: relative;
  font-style: italic;
}

.remark-content::before {
  content: '"';
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  font-size: 48rpx;
  color: #667eea;
  opacity: 0.3;
  font-family: serif;
}

.remark-content::after {
  content: '"';
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 48rpx;
  color: #667eea;
  opacity: 0.3;
  font-family: serif;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
  padding: 0 32rpx;
}

.action-btn {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:active::before {
  left: 100%;
}

.action-btn .iconfont {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: white;
  color: #667eea;
  border: 3rpx solid #667eea;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.1);
}

.action-btn.secondary:active {
  background: #f8f9ff;
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 12rpx rgba(102, 126, 234, 0.2);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .usage-grid {
    flex-wrap: wrap;
    gap: 24rpx;
  }

  .usage-item {
    width: 30%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}
