// utils/error-handler.js

/**
 * 全局错误处理工具
 */

/**
 * 显示错误提示
 * @param {string|Error} error - 错误信息或错误对象
 * @param {object} options - 配置选项
 */
function showError(error, options = {}) {
  const defaultOptions = {
    duration: 3000,
    icon: 'none',
    mask: false
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  let message = '';
  if (typeof error === 'string') {
    message = error;
  } else if (error instanceof Error) {
    message = error.message;
  } else if (error && error.message) {
    message = error.message;
  } else {
    message = '发生未知错误';
  }
  
  wx.showToast({
    title: message,
    icon: finalOptions.icon,
    duration: finalOptions.duration,
    mask: finalOptions.mask
  });
}

/**
 * 显示网络错误提示
 * @param {Error} error - 网络错误对象
 */
function showNetworkError(error) {
  let message = '网络连接失败';
  
  if (error && error.message) {
    if (error.message.includes('timeout')) {
      message = '请求超时，请检查网络连接';
    } else if (error.message.includes('fail')) {
      message = '网络连接失败，请检查网络设置';
    } else {
      message = error.message;
    }
  }
  
  showError(message, { duration: 4000 });
}

/**
 * 显示加载错误提示
 * @param {string} resource - 资源名称
 * @param {Error} error - 错误对象
 */
function showLoadError(resource, error) {
  const message = `加载${resource}失败，请稍后重试`;
  showError(message);
  console.error(`加载${resource}失败:`, error);
}

/**
 * 显示操作错误提示
 * @param {string} action - 操作名称
 * @param {Error} error - 错误对象
 */
function showActionError(action, error) {
  const message = `${action}失败，请稍后重试`;
  showError(message);
  console.error(`${action}失败:`, error);
}

/**
 * 处理API错误
 * @param {Error} error - API错误对象
 * @param {string} context - 错误上下文
 */
function handleApiError(error, context = '') {
  console.error(`API错误${context ? ` (${context})` : ''}:`, error);
  
  if (error.message && error.message.includes('登录已过期')) {
    // 登录过期，跳转到登录页面
    wx.showModal({
      title: '登录过期',
      content: '您的登录已过期，请重新登录',
      showCancel: false,
      success: () => {
        // 这里可以跳转到登录页面或者刷新当前页面
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          if (currentPage.checkLoginStatus) {
            currentPage.checkLoginStatus();
          }
        }
      }
    });
  } else {
    showError(error);
  }
}

/**
 * 捕获并处理Promise错误
 * @param {Promise} promise - Promise对象
 * @param {string} context - 错误上下文
 */
function catchError(promise, context = '') {
  return promise.catch(error => {
    handleApiError(error, context);
    throw error; // 重新抛出错误，让调用者可以继续处理
  });
}

/**
 * 安全执行异步函数
 * @param {Function} asyncFn - 异步函数
 * @param {string} context - 错误上下文
 * @param {object} options - 配置选项
 */
async function safeExecute(asyncFn, context = '', options = {}) {
  const { showLoading = false, loadingText = '处理中...' } = options;
  
  try {
    if (showLoading) {
      wx.showLoading({ title: loadingText });
    }
    
    const result = await asyncFn();
    
    if (showLoading) {
      wx.hideLoading();
    }
    
    return result;
  } catch (error) {
    if (showLoading) {
      wx.hideLoading();
    }
    
    handleApiError(error, context);
    throw error;
  }
}

/**
 * 重试机制
 * @param {Function} fn - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 */
async function retry(fn, maxRetries = 3, delay = 1000) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        break;
      }
      
      // 如果是网络错误，进行重试
      if (error.message && (
        error.message.includes('timeout') ||
        error.message.includes('fail') ||
        error.message.includes('网络')
      )) {
        console.log(`第${i + 1}次重试失败，${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // 非网络错误，不重试
        break;
      }
    }
  }
  
  throw lastError;
}

module.exports = {
  showError,
  showNetworkError,
  showLoadError,
  showActionError,
  handleApiError,
  catchError,
  safeExecute,
  retry
};
