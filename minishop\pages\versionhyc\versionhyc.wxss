/**
 * 好业财版本页面样式 - 仅包含套餐详情相关样式
 */
/* 引入iconfont图标库 */
@import '/static/fonts/iconfont.wxss';

/* 全局变量 */
page {
  --primary-color: #FF3333;
  --secondary-color: #FF8C00;
  --light-color: #FFFFFF;
  --bg-color: #F6F6F6;
  --text-color: #1a1a1a;
  --text-secondary: #383838;
  --border-radius: 12rpx;
  background-color: #FF3333; /* 添加页面背景色与头部一致 */
}

/* 页面容器 */
.container {
  background-color: #FFEDED;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  padding: 0 !important;
  width: 100% !important;
  display: block !important;
  align-items: unset !important;
}

/* 促销气泡装饰 */
.container::before,
.container::after {
  content: '';
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  opacity: 0.1;
  z-index: 0;
}

.container::before {
  background: radial-gradient(circle, #FF6A6A, #FF2B22);
  top: -50rpx;
  right: -50rpx;
}

.container::after {
  background: radial-gradient(circle, #FFD700, #FF8C00);
  bottom: 20%;
  left: -100rpx;
}







/* 标签页 - 修改样式增加间距 */
.tabs {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 50rpx;
  margin: 0 30rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  position: relative;
  z-index: 1;
}

.tab-container {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 25rpx;
}

.tab-item {
  font-size: 32rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
  text-align: center;
  width: 100%;
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(to right, #FF3333, #FF6A6A);
  border-radius: 3rpx;
}

/* 标签内容区域 */
.tab-content {
  padding-bottom: 120rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* 套餐列表 */
.package-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.package-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  border: 2rpx solid transparent;
  transition: all 0.3s;
  width: 92%;
  margin: 0 auto 30rpx;
}

.package-item.popular {
  border-color: var(--primary-color);
}

.package-item.selected {
  border-color: var(--primary-color);
  background-color: rgba(255, 43, 34, 0.05);
}

/* 年限选择单选框 */
.duration-selector {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
  gap: 10rpx;
}

.duration-option {
  flex: 1;
  max-width: 120rpx;
  text-align: center;
  padding: 12rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.duration-option.selected {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 产品版本选择器 */
.type-selector {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
  gap: 10rpx;
}

.type-option {
  flex: 1;
  text-align: center;
  padding: 12rpx 16rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
  max-width: 140rpx;
}

.type-option.selected {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.popular-tag {
  position: absolute;
  top: 0;
  right: 20rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 0 0 16rpx 16rpx;
}

.package-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.package-price-container {
  margin-bottom: 16rpx;
}

.package-original-price {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 6rpx;
}

.package-discount-price {
  display: flex;
  align-items: baseline;
}

.price-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.price-value {
  font-size: 38rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.package-gift {
  background-color: rgba(255, 215, 0, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  display: inline-flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.gift-label {
  font-size: 26rpx;
  color: #FF8C00;
  margin-right: 10rpx;
}

.gift-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #FF8C00;
}

.feature-list {
  margin: 20rpx 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
  width: 48%;
}

.feature-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.select-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #F5F5F5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.select-btn.selected {
  background-color: var(--primary-color);
  color: #fff;
}

/* 赠品卡片 */
.gift-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 100rpx;  /* 为底部按钮留出空间 */
}



.gift-list {
  display: flex;
  flex-direction: column;
}

.gift-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.gift-item:last-child {
  border-bottom: none;
}

.gift-content {
  flex: 1;
}

.gift-threshold {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 6rpx;
}

.gift-name {
  font-size: 26rpx;
  color: #666;
}

.gift-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 40rpx;
}




/* 整合卡片样式 */
.ydz-integrated-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx 30rpx;
  margin: 0 30rpx 30rpx;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* 618标签 */
.ydz-integrated-card::before {
  content: '618';
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  font-size: 120rpx;
  font-weight: 900;
  color: rgba(255, 51, 51, 0.05);
  line-height: 1;
  z-index: 0;
}

/* 易代账头部 */
.ydz-header {
  margin-bottom: 30rpx;
  margin-top: 50rpx;
  padding-bottom: 25rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.ydz-title {
  font-size: 50rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  display: inline-block;
}

.ydz-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60%;
  height: 8rpx;
  background: linear-gradient(to right, #FF3333, transparent);
  border-radius: 4rpx;
}

.ydz-desc {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

/* 易代账特点 */
.ydz-features {
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
  padding: 25rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  background: linear-gradient(to bottom, #fff, #fcfcfc);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.ydz-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, rgba(255, 51, 51, 0.7), rgba(255, 106, 106, 0.3));
  border-radius: 3rpx;
  opacity: 0.8;
}

.ydz-feature-item {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
  padding: 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  border-left: 4rpx solid rgba(255, 51, 51, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
}

.ydz-feature-item:nth-child(odd) {
  background: rgba(255, 250, 250, 0.7);
}

.ydz-feature-item:last-child {
  margin-bottom: 10rpx;
}

.ydz-feature-item:hover {
  background: rgba(255, 245, 245, 0.5);
  border-left: 4rpx solid rgba(255, 51, 51, 0.5);
}

.ydz-feature-item .feature-icon {
  margin-right: 20rpx;
  flex-shrink: 0;
  padding-top: 6rpx;
  line-height: 1;
  display: flex;
}

.ydz-feature-item .feature-icon .iconfont {
  font-size: 36rpx;
  color: #FF3333;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 51, 51, 0.3));
  line-height: 1;
}

.ydz-feature-item text {
  font-size: 28rpx;
  color: #444;
  line-height: 1.6;
  flex: 1;
  position: relative;
  padding-bottom: 2rpx;
  word-break: break-all;
  word-wrap: break-word;
}

/* 选择器部分 */
.ydz-selector-section {
  margin-bottom: 40rpx;
  position: relative;
}

.ydz-selector-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.ydz-selector-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #FF3333, #FF6A6A);
  margin-right: 12rpx;
  border-radius: 4rpx;
}

.ydz-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.ydz-option {
  padding: 20rpx 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
  font-size: 30rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.ydz-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 106, 106, 0.8), rgba(255, 43, 34, 0.8));
  opacity: 0;
  transition: opacity 0.3s;
  z-index: -1;
}

.ydz-option.selected {
  color: #fff;
  border-color: transparent;
  box-shadow: 0 4rpx 12rpx rgba(255, 43, 34, 0.3);
  transform: translateY(-2rpx);
}

.ydz-option.selected::before {
  opacity: 1;
}

/* 时长选择器 */
.ydz-duration-selector {
  display: flex;
  gap: 20rpx;
}

.ydz-duration-option {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 30rpx;
  color: #666;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.ydz-duration-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6A6A, #FF2B22);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: -1;
}

.ydz-duration-option.selected {
  color: #fff;
  border-color: transparent;
  box-shadow: 0 4rpx 12rpx rgba(255, 43, 34, 0.3);
}

.ydz-duration-option.selected::before {
  opacity: 1;
}

.ydz-duration-option .discount-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FF8C00;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 0 0 0 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 140, 0, 0.3);
}

.ydz-duration-option .hot-tag {
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  background-color: #FF2B22;
  color: white;
  font-size: 20rpx;
  padding: 20rpx 16rpx 2rpx 16rpx;
  transform: rotate(-45deg);
  box-shadow: 0 2rpx 6rpx rgba(255, 43, 34, 0.3);
}

/* 价格区域 */
.ydz-price-section {
  background: linear-gradient(135deg, #FFF9F9, #FFECEC);
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(255, 51, 51, 0.1);
}

.ydz-price-section::after {
  content: '特惠';
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(255, 43, 34, 0.8);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  transform: rotate(15deg);
  box-shadow: 0 2rpx 6rpx rgba(255, 43, 34, 0.3);
}

.ydz-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}



.price-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
  width: 200rpx;
  flex-shrink: 0;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
  position: relative;
}

.discount-price {
  font-size: 42rpx;
  color: #FF3333;
  font-weight: bold;
  text-shadow: 0 2rpx 3rpx rgba(255, 51, 51, 0.2);
  position: relative;
}

.discount-price::before {
  content: '¥';
  font-size: 28rpx;
  position: relative;
  top: -8rpx;
  left: -4rpx;
}

.daily-price {
  font-size: 32rpx;
  color: #FF6600;
  font-weight: bold;
  position: relative;
}

.daily-price::after {
  content: '/天';
  font-size: 24rpx;
  margin-left: 4rpx;
}

/* 价格注释样式 */
.ydz-price-note-container {
  margin: 14rpx 0 6rpx;
  padding: 12rpx 16rpx 8rpx;
  border-top: 1px dashed rgba(255, 102, 102, 0.3);
  background: linear-gradient(to right, rgba(255, 248, 248, 0.5), rgba(255, 236, 236, 0.5));
  border-radius: 0 0 16rpx 16rpx;
}

.ydz-price-note {
  font-size: 24rpx;
  color: #777;
  display: flex;
  align-items: center;
  justify-content: center;
}

.note-icon {
  color: #FF6600;
  font-weight: bold;
  margin-right: 6rpx;
  font-size: 26rpx;
}

/* 赠品部分 */
.ydz-gift-section {
  background: linear-gradient(135deg, #FFF5E6, #FFE0B2);
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 0, 0.1);
}

.ydz-gift-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 120rpx;
  height: 120rpx;
  background: radial-gradient(circle, rgba(255, 140, 0, 0.2), transparent);
  border-radius: 50%;
}

.ydz-gift-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #FF8C00;
  margin-bottom: 20rpx;
  position: relative;
}

.gift-icon {
  margin-right: 10rpx;
  display: inline-block;
  transform-origin: center;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.ydz-gift-info {
  padding-left: 10rpx;
  position: relative;
}

.ydz-gift-product {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  padding: 10rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 10rpx;
  display: inline-block;
}

.no-gift {
  font-size: 26rpx;
  color: #999;
  font-style: italic;
  padding: 10rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 10rpx;
  display: inline-block;
}

/* 按钮组 */
.buttons-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 购买按钮 */
.ydz-buy-button {
  background: linear-gradient(135deg, #FF3333, #FF6A6A);
  color: #fff;
  font-size: 34rpx;
  font-weight: bold;
  flex: 3;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 51, 51, 0.25);
  margin-top: 0;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.ydz-buy-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 60%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.6s, opacity 0.6s;
}

.ydz-buy-button:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 10rpx rgba(255, 51, 51, 0.2);
}

.ydz-buy-button:active::after {
  transform: scale(1);
  opacity: 1;
}





/* 活动说明 */
.activity-notes {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}

.activity-notes text {
  margin-bottom: 6rpx;
}



/* 限时特惠标签 */
.promo-tag {
  position: absolute;
  top: 20rpx;
  left: -10rpx;
  background: linear-gradient(135deg, #FF3333, #FF6A6A);
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 20rpx;
  border-radius: 0 8rpx 8rpx 0;
  box-shadow: 0 4rpx 8rpx rgba(255, 51, 51, 0.3);
  z-index: 2;
}

.promo-tag::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -10rpx;
  border-top: 10rpx solid #CC0000;
  border-left: 10rpx solid transparent;
}

/* 折扣徽章 */
.discount-badge {
  background: linear-gradient(135deg, #FF8C00, #FFBB00);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(255, 140, 0, 0.3);
  transform: rotate(-5deg);
  position: relative;
}

.discount-badge::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

/* 满减进度指示器 */
.gift-progress {
  margin-top: 20rpx;
}

.gift-progress-bar {
  height: 16rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  position: relative;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(to right, #FF6A6A, #FF2B22);
  border-radius: 8rpx;
  transition: width 0.5s;
}

.gift-progress-marks {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #888;
}

.gift-progress-marks .reached {
  color: #FF3333;
  font-weight: bold;
}

/* 按钮发光效果 */
.button-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 150%;
  height: 100%;
  background: linear-gradient(
    to right, 
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.3) 50%,
    rgba(255,255,255,0) 100%
  );
  transform: skewX(-20deg) translateX(-150%);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { transform: skewX(-20deg) translateX(-150%); }
  40%, 100% { transform: skewX(-20deg) translateX(300%); }
}

/* 让账套选择的热门标签与时长选择保持一致 */
.ydz-option {
  position: relative;
  overflow: hidden;
}

.ydz-option .hot-tag {
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  background-color: #FF2B22;
  color: white;
  font-size: 20rpx;
  padding: 20rpx 16rpx 2rpx 16rpx;
  transform: rotate(-45deg);
  box-shadow: 0 2rpx 6rpx rgba(255, 43, 34, 0.3);
}



/* 产品详情按钮 */
.detail-button {
  background: linear-gradient(135deg, #FF9900, #FFC107);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 153, 0, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.detail-button:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 10rpx rgba(255, 153, 0, 0.2);
}