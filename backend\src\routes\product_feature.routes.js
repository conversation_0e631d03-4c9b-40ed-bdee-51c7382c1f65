const express = require('express');
const router = express.Router();
const { employee } = require('../middleware/auth');

const featureController = require('../controllers/product_feature.controller.js');

// 定义产品功能相关的路由

// GET /api/features - 获取所有功能列表
router.get('/', employee.verifyEmployee, featureController.getAllFeatures);

// [新增] 获取下一个可用ID
router.get('/next-id', employee.verifyAdmin, featureController.getNextFeatureId);

// POST /api/features - 创建一个新功能
router.post('/', employee.verifyAdmin, featureController.createFeature);

// PUT /api/features/:id - 更新一个现有功能
router.put('/:id', employee.verifyAdmin, featureController.updateFeature);

// DELETE /api/features/:id - 删除一个功能
router.delete('/:id', employee.verifyAdmin, featureController.deleteFeature);

module.exports = router; 