<view class="container">
  <!-- 页面头部组件 -->
  <page-header
    product-name="{{productName}}"
    promotion-info="{{promotionInfo}}"
    activity-time-range="{{activityTimeRange}}" />

  <!-- 标签页选项卡 -->
  <view class="tabs">
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 0 ? 'active' : ''}}"
        data-index="0"
        bindtap="switchTab">
        套餐详情
      </view>
    </view>
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 1 ? 'active' : ''}}"
        data-index="1"
        bindtap="switchTab">
        活动规则
      </view>
    </view>
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 2 ? 'active' : ''}}"
        data-index="2"
        bindtap="switchTab">
        常见问题
      </view>
    </view>
  </view>

  <!-- 套餐详情 -->
  <view class="tab-content" hidden="{{activeTab !== 0}}">
    <view class="ydz-integrated-card">
      <view class="promo-tag">限时特惠</view>

      <view class="ydz-header">
        <view class="ydz-title">{{productName}}</view>
        <view class="ydz-desc">
          <view>针对不同的企业规模、业务场景</view>
          <view>不同版本的好会计灵活满足您的需求</view>
        </view>
      </view>

      <!-- 产品特点 -->
      <view class="ydz-features">
        <view class="ydz-feature-item" wx:for="{{hkjFeatures}}" wx:key="index">
          <view class="feature-icon"><text class="iconfont icon-xuanzhong"></text></view>
          <text>{{item}}</text>
        </view>
      </view>

      <!-- 账套数选择 -->
      <view class="ydz-selector-section">
        <view class="ydz-selector-title">请选择账套数：</view>
        <view class="ydz-selector">
          <view wx:for="{{packages}}" wx:key="item"
                class="ydz-option {{selectedPackage === item ? 'selected' : ''}}"
                bindtap="selectPackage" data-package="{{item}}">
                <text>{{item}}</text>
                <text wx:if="{{item === '标准版'}}" class="hot-tag">HOT</text>
          </view>
        </view>
      </view>

      <!-- 时长选择 -->
      <view class="ydz-selector-section">
        <view class="ydz-selector-title">请选择时长：</view>
        <view class="ydz-duration-selector">
          <view wx:for="{{promotionInfo.rules}}" wx:key="index"
                class="ydz-duration-option {{selectedDuration === item.period ? 'selected' : ''}}"
                bindtap="selectDuration" data-duration="{{item.period}}">
            <text>{{item.period}}年</text>
            <text wx:if="{{item.period === 3}}" class="hot-tag">HOT</text>
            <text class="discount-tag">{{item.discountText}}折</text>
          </view>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="ydz-price-section">
        <view class="ydz-price-row">
          <text class="price-label">原价:</text>
          <text class="original-price">¥{{originalPrice}}</text>
        </view>
        <view class="ydz-price-row">
          <text class="price-label">特惠价:</text>
          <text class="discount-price">{{discountPrice}}</text>
          <view class="discount-badge">
            <text wx:if="{{selectedDiscount}}">{{selectedDuration}}年{{selectedDiscountText}}折</text>
          </view>
        </view>
        <view class="ydz-price-row">
          <text class="price-label">每天低至:</text>
          <text class="daily-price">{{dailyPrice}}</text>
        </view>
      </view>

      <!-- 赠送礼品 -->
      <view class="ydz-gift-section" wx:if="{{promotionInfo.gifts && promotionInfo.gifts.length}}">
        <view class="ydz-gift-title">
          <text class="gift-icon">🎁</text>
          <text>满额赠送</text>
        </view>
        <view class="ydz-gift-info">
          <view class="ydz-gift-product" wx:if="{{giftProduct}}">{{giftProduct}}</view>
          <view wx:else class="no-gift">暂未满足赠品条件</view>
        </view>

        <view class="gift-progress">
          <view class="gift-progress-bar">
            <view class="progress-inner" style="width: {{progressPercent}}"></view>
          </view>
          <view class="gift-progress-marks">
            <text wx:for="{{promotionInfo.gifts}}" wx:key="index"
                  class="{{discountPrice >= item.threshold ? 'reached' : ''}}">满{{item.threshold}}</text>
          </view>
        </view>

        <!-- 满减指示器 -->
        <view class="gift-progress">
          <view class="gift-progress-bar">
            <view class="progress-inner" style="width: {{progressPercent}}"></view>
          </view>
          <view class="gift-progress-marks">
            <text wx:for="{{promotionInfo.gifts}}" wx:key="index" 
                  class="{{discountPrice >= item.threshold ? 'reached' : ''}}">满{{item.threshold}}</text>
          </view>
        </view> 
      </view>

      <!-- 立即购买按钮 -->
      <view class="buttons-group">
        <view class="detail-button" bindtap="showProductDetail">产品详情</view>
        <button class="ydz-buy-button" open-type="contact">
          购买咨询
          <view class="button-shine"></view>
        </button>
      </view>

      <view class="activity-notes">
        <text>活动时间：{{activityTimeRange}}</text>
        <text>*活动解释权归贝克信息所有</text>
      </view>
    </view>
  </view>

  <!-- 活动规则 -->
  <view class="tab-content" hidden="{{activeTab !== 1}}">
    <activity-rules
      promotion-info="{{promotionInfo}}"
      activity-time-range="{{activityTimeRange}}" />
  </view>

  <!-- 常见问题 -->
  <view class="tab-content" hidden="{{activeTab !== 2}}">
    <common-faq />
  </view>
</view>