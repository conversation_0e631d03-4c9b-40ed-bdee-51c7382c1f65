<!--pages/me.wxml-->
<wxs module="utils">
  // 计算剩余天数
  function calculateDaysLeft(expiryDateStr) {
    if (!expiryDateStr) return 0;
    var today = getDate();
    var expiryDate = getDate(expiryDateStr);
    var timeDiff = expiryDate.getTime() - today.getTime();
    return Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
  }

  // 获取到期状态
  function getExpiryStatus(expiryDateStr) {
    if (!expiryDateStr) {
      return { class: 'normal', text: '无限期' };
    }

    var daysLeft = calculateDaysLeft(expiryDateStr);

    if (daysLeft <= 0) {
      return { class: 'expired', text: '已过期' };
    } else if (daysLeft <= 30) {
      return { class: 'warning', text: daysLeft + '天后到期' };
    } else {
      return { class: 'normal', text: daysLeft + '天后到期' };
    }
  }

  module.exports = {
    getExpiryStatus: getExpiryStatus,
    calculateDaysLeft: calculateDaysLeft
  };
</wxs>

<view class="page-container">
  <!-- 顶部头部 -->
  <view class="header-section" animation="{{headerAnimation}}">
    <view class="header-bg"></view>
    <view class="user-info-container" bindtap="handleUserInfoTap">
      <view class="avatar-wrapper">
        <text class="iconfont icon-user avatar-icon"></text>
      </view>
      <view class="info-text">
        <view class="user-nickname-container">
          <text class="user-nickname">{{userInfo.nickName}}</text>
          <text wx:if="{{userInfo.isLoggedIn}}" class="edit-icon" catchtap="editUserInfo">✏️</text>
        </view>
        <view class="user-id" wx:if="{{userInfo.isLoggedIn}}">
          <text>用户ID：{{userInfo.displayUserId || '未设置'}}</text>
          <text class="copy-icon" catchtap="copyUserId" wx:if="{{userInfo.displayUserId}}">📋</text>
        </view>
        <view class="user-id" wx:else>登录后享更多精彩服务</view>
      </view>
      <view class="settings-icon" bindtap="navigateToSettings" catchtap="navigateToSettings">
        <text class="iconfont icon-shezhi"></text>
      </view>
    </view>
  </view>

  <!-- 企业切换区域 -->
  <view class="enterprise-selector card" animation="{{cardAnimation[0]}}">
    <view class="selector-content" bindtap="showEnterpriseSelector">
      <view class="selector-left">
        <text class="selector-icon">🏢</text>
        <view class="selector-info">
          <view class="selector-label">当前企业</view>
          <view class="current-enterprise">{{currentEnterprise.name || '请选择企业'}}</view>
        </view>
      </view>
      <view class="selector-arrow">
        <text class="iconfont icon-youjiantou"></text>
      </view>
    </view>
  </view>

  <!-- 页签切换 -->
  <view class="tab-container card" animation="{{cardAnimation[1]}}">
    <view class="tab-header">
      <view class="tab-item {{activeTab === 'assets' ? 'active' : ''}}" bindtap="switchTab" data-tab="assets">
        <text class="tab-icon">📦</text>
        <text class="tab-text">我的资产</text>
        <view class="tab-badge" wx:if="{{assets.length > 0}}">{{assets.length}}</view>
      </view>
      <view class="tab-item {{activeTab === 'orders' ? 'active' : ''}}" bindtap="switchTab" data-tab="orders">
        <text class="tab-icon">📋</text>
        <text class="tab-text">我的订单</text>
        <view class="tab-badge" wx:if="{{orders.length > 0}}">{{orders.length}}</view>
      </view>
    </view>
  </view>

  <!-- 资产页签内容 -->
  <view class="assets-section card" wx:if="{{activeTab === 'assets'}}" animation="{{cardAnimation[2]}}">
    <view class="card-header">
      <view class="card-title">资产列表</view>
      <view class="card-more" bindtap="viewAllAssets">查看全部 <text class="iconfont icon-youjiantou"></text></view>
    </view>

    <!-- 资产状态筛选 -->
    <view class="asset-filter">
      <view class="filter-item {{assetFilter === 'all' ? 'active' : ''}}" bindtap="switchAssetFilter" data-filter="all">
        <text class="filter-text">全部</text>
        <view class="filter-badge" wx:if="{{assets.length > 0}}">{{assets.length}}</view>
      </view>
      <view class="filter-item {{assetFilter === 'active' ? 'active' : ''}}" bindtap="switchAssetFilter" data-filter="active">
        <text class="filter-text">有效</text>
        <view class="filter-badge" wx:if="{{activeAssets.length > 0}}">{{activeAssets.length}}</view>
      </view>
      <view class="filter-item {{assetFilter === 'expired' ? 'active' : ''}}" bindtap="switchAssetFilter" data-filter="expired">
        <text class="filter-text">已过期</text>
        <view class="filter-badge" wx:if="{{expiredAssets.length > 0}}">{{expiredAssets.length}}</view>
      </view>
    </view>

    <!-- 资产列表 -->
    <view wx:if="{{filteredAssets.length > 0}}" class="assets-list">
      <view wx:for="{{filteredAssets}}" wx:key="id" class="asset-item" bindtap="viewAssetDetail" data-id="{{item.id}}">
        <view class="asset-main">
          <view class="asset-header">
            <view class="asset-name">{{item.product ? item.product.product_name : '未知产品'}}</view>
            <view class="asset-status {{item.status === '过期' ? 'expired' : 'active'}}">{{item.status}}</view>
          </view>
          <view class="asset-info">
            <view class="asset-id">资产编号：{{item.asset_id}}</view>
            <view class="asset-version" wx:if="{{item.product && item.product.version_name}}">版本：{{item.product.version_name}}</view>
          </view>
          <view class="asset-details">
            <view class="detail-item">
              <text class="detail-label">用户数：</text>
              <text class="detail-value">{{item.user_count}}人</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">账套数：</text>
              <text class="detail-value">{{item.account_count}}套</text>
            </view>
          </view>
          <view class="asset-dates">
            <view class="date-item" wx:if="{{item.product_expiry_date}}">
              <text class="date-label">产品到期：</text>
              <text class="date-value {{utils.getExpiryStatus(item.product_expiry_date).class}}">{{item.product_expiry_date}}</text>
            </view>
            <view class="date-item" wx:if="{{item.sps_expiry_date}}">
              <text class="date-label">SPS到期：</text>
              <text class="date-value {{utils.getExpiryStatus(item.sps_expiry_date).class}}">{{item.sps_expiry_date}}</text>
            </view>
          </view>
        </view>
        <view class="asset-actions">
          <text class="iconfont icon-youjiantou"></text>
        </view>
      </view>
    </view>

    <!-- 空资产状态 -->
    <view wx:else class="empty-assets">
      <view class="empty-icon-wrapper">
        <text class="empty-icon">📦</text>
      </view>
      <view class="empty-text">暂无资产信息</view>
      <view class="empty-subtext">请联系管理员添加资产</view>
    </view>
  </view>

  <!-- 订单页签内容 -->
  <view class="order-section card" wx:if="{{activeTab === 'orders'}}" animation="{{cardAnimation[2]}}">
    <view class="card-header">
      <view class="card-title">订单列表</view>
      <view class="card-more" bindtap="viewAllOrders">查看全部 <text class="iconfont icon-youjiantou"></text></view>
    </view>

    <!-- 订单状态筛选 -->
    <view class="order-filter">
      <view class="filter-item {{orderFilter === 'all' ? 'active' : ''}}" bindtap="switchOrderFilter" data-filter="all">
        <text class="filter-text">全部</text>
        <view class="filter-badge" wx:if="{{orders.length > 0}}">{{orders.length}}</view>
      </view>
      <view class="filter-item {{orderFilter === 'pending' ? 'active' : ''}}" bindtap="switchOrderFilter" data-filter="pending">
        <text class="filter-text">待付款</text>
        <view class="filter-badge" wx:if="{{pendingOrders.length > 0}}">{{pendingOrders.length}}</view>
      </view>
      <view class="filter-item {{orderFilter === 'paid' ? 'active' : ''}}" bindtap="switchOrderFilter" data-filter="paid">
        <text class="filter-text">已完成</text>
        <view class="filter-badge" wx:if="{{paidOrders.length > 0}}">{{paidOrders.length}}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:if="{{filteredOrders.length > 0}}">
      <view wx:for="{{filteredOrders}}" wx:key="id" class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <view class="order-number">订单号：{{item.order_number}}</view>
          <view class="order-status {{item.status}}">{{item.status_text}}</view>
        </view>
        <view class="order-content">
          <view class="order-product">{{item.product_name}}</view>
          <view class="order-amount">¥{{item.total_amount}}</view>
        </view>
        <view class="order-footer">
          <view class="order-date">{{item.created_at}}</view>
          <view class="order-actions">
            <text class="iconfont icon-youjiantou"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空订单状态 -->
    <view wx:else class="empty-orders">
      <view class="empty-icon-wrapper">
        <text class="empty-icon">📋</text>
      </view>
      <view class="empty-text">暂无订单信息</view>
      <view class="empty-subtext">当前企业下还没有订单</view>
    </view>
  </view>

</view>

<!-- 企业选择弹窗 -->
<view class="enterprise-modal" wx:if="{{showEnterpriseModal}}" bindtap="hideEnterpriseModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">选择企业</view>
      <view class="modal-close" bindtap="hideEnterpriseModal">
        <text class="iconfont icon-guanbi"></text>
      </view>
    </view>
    <view class="enterprise-list">
      <view wx:for="{{enterprises}}" wx:key="id" class="enterprise-option {{currentEnterprise.id === item.id ? 'selected' : ''}}" bindtap="selectEnterprise" data-enterprise="{{item}}">
        <view class="enterprise-info">
          <view class="enterprise-name">{{item.name}}</view>
          <view class="enterprise-desc" wx:if="{{item.contact_person}}">联系人：{{item.contact_person}}</view>
        </view>
        <view class="enterprise-check" wx:if="{{currentEnterprise.id === item.id}}">
          <text class="iconfont icon-chenggong"></text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 用户信息编辑弹窗 -->
<view class="modal-overlay" wx:if="{{showUserEditModal}}" bindtap="hideUserEditModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑个人信息</text>
      <text class="modal-close" bindtap="hideUserEditModal">×</text>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">用户昵称</text>
        <input
          class="form-input"
          type="text"
          placeholder="请输入昵称"
          value="{{editForm.nickname}}"
          data-field="nickname"
          bindinput="handleFormInput"
          maxlength="20"
        />
      </view>
      <view class="form-group">
        <text class="form-label">邮箱地址</text>
        <input
          class="form-input"
          type="text"
          placeholder="请输入邮箱地址"
          value="{{editForm.email}}"
          data-field="email"
          bindinput="handleFormInput"
          maxlength="50"
        />
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn-cancel" bindtap="hideUserEditModal">取消</button>
      <button class="btn-confirm" bindtap="saveUserInfo">保存</button>
    </view>
  </view>
</view>



<!-- 底部导航栏 -->
<bottom-nav currentTab="me" bindbottomnav="onBottomNavEvent"></bottom-nav>

<!-- 浮动咨询组件 -->
<float-consult />