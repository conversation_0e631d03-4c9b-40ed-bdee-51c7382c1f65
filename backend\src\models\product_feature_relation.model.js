const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 ProductFeatureRelation (产品功能关联) 模型/表
 * 这是 Product 和 ProductFeature 之间的联结表 (Join Table)
 */
const ProductFeatureRelation = sequelize.define(
  'ProductFeatureRelation',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '自增主键',
    },
    // product_id 和 feature_id 是外键，Sequelize 会在定义关联时自动处理，
    // 但在这里明确写出可以增加代码可读性。
    // 注意：数据库中的 feature_id 列已被 product_feature_id (INT) 替代
    product_feature_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联product_feature.id',
    },
    feature_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '功能价格',
    },
    remark: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '备注',
    },
  },
  {
    tableName: 'product_feature_relation',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
  }
);

module.exports = ProductFeatureRelation; 