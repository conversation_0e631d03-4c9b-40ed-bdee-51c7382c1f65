import service from '@/utils/request_extra.js';

const API_PATH = '/dashboard';

/**
 * 获取仪表板统计数据
 * 包含企业、用户、合伙人、资产、订单、佣金等统计信息
 */
export const getDashboardStats = () => {
  return service.get(`${API_PATH}/stats`);
};

/**
 * 获取用户增长趋势数据
 * @param {string} period - 时间周期 '7days', '30days', '3months'
 */
export const getUserGrowthTrend = (period = '30days') => {
  return service.get(`${API_PATH}/user-growth-trend`, { params: { period } });
};

/**
 * 获取订单状态分布数据
 */
export const getOrderStatusDistribution = () => {
  return service.get(`${API_PATH}/order-status-distribution`);
};

/**
 * 获取最近活动记录
 * @param {number} limit - 返回记录数量，默认5条
 */
export const getRecentActivities = (limit = 5) => {
  return service.get(`${API_PATH}/recent-activities`, { params: { limit } });
};
