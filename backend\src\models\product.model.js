// 引入 Sequelize 库和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 Product 模型/表
 * @param {object} sequelize - Sequelize 实例
 * @param {object} DataTypes - Sequelize 的数据类型
 */
const Product = sequelize.define(
  'Product', // 模型名称，Sequelize 会自动将其映射为表名 'Products'
  {
    // 字段定义
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '自增主键ID',
    },
    product_id: {
      type: DataTypes.STRING(20), // 字符串类型，最大长度20
      unique: true, // 业务ID，确保唯一性
      allowNull: false, // 不允许为空
      comment: '产品ID（如HKJ01）', // 数据库字段注释
    },
    product_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '产品名称',
    },
    version_name: {
      type: DataTypes.STRING(50),
      allowNull: true, // 允许为空
      comment: '版本名称',
    },
    base_price: {
      type: DataTypes.DECIMAL(10, 2), // 精确的小数类型，总共10位，小数点后2位
      allowNull: false,
      comment: '基础价格',
    },
    base_account_count: {
      type: DataTypes.INTEGER, // 整数类型
      allowNull: false,
      comment: '基础账套数',
    },
    base_user_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '基础使用人数',
    },
    allow_user_addon: {
      type: DataTypes.BOOLEAN, // 布尔类型 (在MySQL中通常是TINYINT(1))
      allowNull: false,
      defaultValue: false, // 默认值为 false
      comment: '允许增用户',
    },
    allow_account_addon: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '允许增账套',
    },
    addons: {
      type: DataTypes.JSON, // JSON类型
      allowNull: true,
      comment: '增购规则',
    },
    remark: {
      type: DataTypes.TEXT, // 长文本类型
      allowNull: true,
      comment: '备注',
    },
  },
  {
    // 模型选项
    tableName: 'product', // 强制 Sequelize 使用我们指定的表名 'product'，而不是它自动生成的 'Products'
    timestamps: true, // [核心修复] 启用时间戳，让Sequelize自动管理createdAt和updatedAt
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
  }
);

// 导出模型
module.exports = Product; 