/* pages/change-password/change-password.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 50rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 90rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  background-color: #f9f9f9;
}

.tips {
  margin: 40rpx 0;
  padding: 20rpx;
  background-color: #f7fafc;
  border-radius: 8rpx;
  border-left: 4rpx solid #0FB9B1;
}

.tips text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background: #0FB9B1;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn[disabled] {
  opacity: 0.6;
  background: #0FB9B1;
  color: #fff;
} 