<script setup>
import { ref } from 'vue';
import {
  OfficeBuilding,
  User,
  Avatar,
  Goods,
  Collection,
  MessageBox,
  Tickets,
  Fold,
  Expand,
  ArrowDown,
  HomeFilled
} from '@element-plus/icons-vue'
import { useAuth } from './store/auth'; // 1. 引入认证store
import ChangePasswordDialog from '@/components/ChangePasswordDialog.vue'; // 1. 引入修改密码对话框组件
import TabsView from '@/components/TabsView.vue'; // 2. 引入标签页组件

const { state, logout } = useAuth(); // 2. 获取响应式状态和logout方法
const isCollapse = ref(false)
const changePasswordDialogRef = ref(null); // 2. 创建对话框的引用

const handleOpenChangePasswordDialog = () => { // 3. 定义打开对话框的方法
  if (changePasswordDialogRef.value) {
    changePasswordDialogRef.value.openDialog();
  }
};
</script>

<template>
  <div class="common-layout">
    <el-container>
      <!-- 左侧菜单栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="main-aside">
        <div class="logo">
           <span v-if="!isCollapse">贝克智软</span>
        </div>
        <el-menu
          active-text-color="#ffd04b"
          background-color="#545c64"
          class="el-menu-vertical-demo"
          text-color="#fff"
          router
          :collapse="isCollapse"
          :collapse-transition="false"
        >
          <!-- 首页 -->
          <el-menu-item index="/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-sub-menu index="/orders">
            <template #title>
              <el-icon><Tickets /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="/orders/product/form">新增产品订单</el-menu-item>
            <el-menu-item index="/orders/service/form">新增服务订单</el-menu-item>
            <el-menu-item index="/orders/review">订单审核</el-menu-item>
            <el-menu-item index="/orders/product">产品订单</el-menu-item>
            <el-menu-item index="/orders/service">服务订单</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="/assets">
            <template #title>
              <el-icon><MessageBox /></el-icon>
              <span>资产管理</span>
            </template>
            <el-menu-item index="/assets/form?mode=add">新增资产</el-menu-item>
            <el-menu-item index="/assets/list">资产列表</el-menu-item>
            <el-menu-item index="/assets/change/list">变更列表</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/users">
            <el-icon><Avatar /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/enterprises">
            <el-icon><OfficeBuilding /></el-icon>
            <span>企业管理</span>
          </el-menu-item>
          <el-menu-item index="/products">
            <el-icon><Goods /></el-icon>
            <span>产品管理</span>
          </el-menu-item>
          <el-menu-item index="/features">
            <el-icon><Collection /></el-icon>
            <span>功能管理</span>
          </el-menu-item>


          <el-menu-item v-if="state.user?.role === 'admin'" index="/employees">
            <el-icon><User /></el-icon>
            <span>员工管理</span>
          </el-menu-item>



        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部栏 -->
        <el-header class="main-header">
           <el-icon class="collapse-icon" @click="isCollapse = !isCollapse">
                <Fold v-if="!isCollapse" />
                <Expand v-else />
            </el-icon>

          <div class="header-right">
            <!-- 3. 改造欢迎语和登出功能 -->
            <el-dropdown v-if="state.isAuthenticated">
              <span class="el-dropdown-link">
                欢迎您, {{ state.user?.name || '员工' }}
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOpenChangePasswordDialog">修改密码</el-dropdown-item>
                  <el-dropdown-item @click="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <!-- 主内容区 -->
        <el-main>
          <!-- 使用标签页组件替换原有的router-view -->
          <TabsView />
        </el-main>
      </el-container>
    </el-container>

    <!-- 5. 将对话框组件放置在模板中 -->
    <ChangePasswordDialog ref="changePasswordDialogRef" />
  </div>
</template>

<style scoped>
.common-layout, .el-container {
  height: 100vh; /* 让布局占满整个屏幕高度 */
}

.el-main {
  padding: 0; /* 移除默认内边距 */
  overflow: hidden; /* 主容器不滚动，让TabsView内部处理滚动 */
}

.main-aside {
  background-color: #545c64;
  color: #fff;
  transition: width 0.3s; /* 平滑过渡动画 */
  overflow-x: hidden; /* 隐藏折叠时的滚动条 */
}

.el-menu {
    border-right: none;
}

.logo {
  text-align: center;
  font-size: 20px;
  line-height: 60px;
  height: 60px;
  font-weight: bold;
  white-space: nowrap; /* 防止文字换行 */
}

.main-header {
  background-color: #ffffff;
  color: #333;
  display: flex;
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
  border-bottom: 1px solid #e6e6e6;
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.collapse-icon {
  font-size: 22px;
  cursor: pointer;
}
</style>
