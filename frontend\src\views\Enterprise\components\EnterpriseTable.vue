<template>
  <div class="enterprise-table">
    <!-- 企业表格 -->
    <el-table 
      :data="enterprises" 
      :loading="loading"
      border 
      style="width: 100%"
      :empty-text="emptyText"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列（可选） -->
      <el-table-column 
        v-if="selectable" 
        type="selection" 
        width="55" 
      />
      
      <!-- 企业ID列 -->
      <el-table-column prop="enterprise_id" label="企业ID" width="180">
        <template #default="{ row }">
          <el-button 
            link 
            type="primary" 
            @click="viewEnterpriseDetail(row.id)"
          >
            {{ row.enterprise_id }}
          </el-button>
        </template>
      </el-table-column>
      
      <!-- 企业名称列 -->
      <el-table-column 
        prop="name" 
        label="企业名称" 
        width="250" 
        show-overflow-tooltip
      />

      <!-- 联系人列 -->
      <el-table-column 
        prop="contact_person" 
        label="联系人" 
        width="120" 
      />

      <!-- 联系电话列 -->
      <el-table-column 
        prop="contact_phone" 
        label="联系电话" 
        width="150" 
      />

      <!-- 负责人列 -->
      <el-table-column 
        prop="employee.name" 
        label="负责人" 
        width="120" 
      />

      <!-- 地址列 -->
      <el-table-column 
        prop="address" 
        label="地址" 
        width="200" 
        show-overflow-tooltip
      />
      
      <!-- 创建时间列 -->
      <el-table-column 
        prop="createdAt" 
        label="创建时间" 
        width="160"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      
      <!-- 备注列 -->
      <el-table-column 
        prop="remark" 
        label="备注" 
        show-overflow-tooltip 
      />
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="showActions" 
        label="操作" 
        width="120" 
        fixed="right"
      >
        <template #default="{ row }">
          <el-button 
            v-if="actionType === 'view'"
            size="small" 
            type="primary"
            @click="viewEnterpriseDetail(row.id)"
          >
            查看
          </el-button>
          <el-button 
            v-if="actionType === 'remove'"
            size="small" 
            type="danger"
            @click="handleRemoveEnterprise(row.id)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination" v-if="showPagination && total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/format.js'

// Props
const props = defineProps({
  enterprises: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  selectable: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: false
  },
  actionType: {
    type: String,
    default: 'view', // 'view' | 'remove'
    validator: (value) => ['view', 'remove'].includes(value)
  },
  showPagination: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['selection-change', 'enterprise-removed', 'size-change', 'current-change'])

const router = useRouter()

// 状态
const currentPage = ref(1)
const pageSize = ref(20)

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 查看企业详情
const viewEnterpriseDetail = (enterpriseId) => {
  router.push({ name: 'enterprise-detail', params: { id: enterpriseId } })
}

// 移除企业
const handleRemoveEnterprise = async (enterpriseId) => {
  try {
    await ElMessageBox.confirm('确定要移除这个企业吗？', '警告', {
      type: 'warning'
    })
    emit('enterprise-removed', enterpriseId)
  } catch (error) {
    // 用户取消
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('current-change', page)
}
</script>

<style scoped>
.enterprise-table {
  width: 100%;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
