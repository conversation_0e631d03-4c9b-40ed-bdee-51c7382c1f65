// 引入数据库模型
const db = require('../models');
const Product = db.Product;
const ProductFeature = db.ProductFeature;
const ProductFeatureRelation = db.ProductFeatureRelation;
const ProductUserAddonTier = db.ProductUserAddonTier; // [新增] 1. 引入阶梯价格模型

/**
 * @description 计算产品订单项的价格（适配新的产品订单表体）
 * @param {object} itemData - 包含订单项信息的对象
 * @param {number} itemData.product_id - 产品的主键ID
 * @param {number} itemData.user_count - 使用人数
 * @param {number} itemData.account_count - 账套数
 * @param {number[]} [itemData.selected_features=[]] - 选中的产品功能ID列表
 * @param {number} [itemData.duration_months=12] - 购买的时长（月）
 * @returns {Promise<number>} 返回计算出的标准价格
 * @throws {Error} 如果产品未找到或输入无效，则抛出错误
 */
exports.calculateProductOrderPrice = async ({
  product_id,
  user_count,
  account_count,
  selected_features = [],
  duration_months = 12
}) => {
  if (!product_id || !user_count || !account_count) {
    throw new Error('计算价格时必须提供产品ID、使用人数和账套数。');
  }

  // 1. 获取产品基础信息
  const product = await Product.findByPk(product_id);
  if (!product) {
    throw new Error(`ID为 ${product_id} 的产品不存在。`);
  }

  let totalPrice = parseFloat(product.base_price); // 从基础价格开始计算

  // 2. 计算用户数增购费用
  const userAddons = Math.max(0, user_count - product.base_user_count);
  if (userAddons > 0) {
    // 查询该产品的所有阶梯价格规则，按起始人数升序排序
    const tiers = await ProductUserAddonTier.findAll({
      where: { product_id: product.id },
      order: [['min_users', 'ASC']]
    });

    let remainingUsers = userAddons; // 待计算的剩余增购用户数
    let userAddonTotalPrice = 0; // 增购用户数的总费用

    // 遍历所有阶梯进行分段计算
    for (const tier of tiers) {
      if (remainingUsers <= 0) break; // 如果已经计算完毕，提前退出循环

      // 将阶梯数据转换为纯数字，避免类型问题
      const min = parseInt(tier.min_users, 10);
      const max = tier.max_users ? parseInt(tier.max_users, 10) : Infinity;
      const price = parseFloat(tier.price_per_user);

      // 计算当前阶梯“能容纳”的用户数
      // 注意：min_users 是从1开始数的第n个增购用户，而不是与产品基础用户数的差值
      const usersInThisTier = Math.min(remainingUsers, max - min + 1);
      
      if (usersInThisTier > 0) {
        userAddonTotalPrice += usersInThisTier * price; // 累加当前阶梯的费用
        remainingUsers -= usersInThisTier; // 更新待计算的用户数
      }
    }
    
    // 如果遍历完所有阶梯后，仍有用户未被计算（通常是因为最后一个阶梯是“以上”），
    // 并且最后一个阶梯存在，则用最后一个阶梯的价格计算剩余用户
    if (remainingUsers > 0 && tiers.length > 0) {
      const lastTier = tiers[tiers.length - 1];
      if (!lastTier.max_users) { // 确认是“以上”的阶梯
         userAddonTotalPrice += remainingUsers * parseFloat(lastTier.price_per_user);
      }
    }

    totalPrice += userAddonTotalPrice; // 将计算出的用户增购总费用加到总价中
  }

  // 3. 计算账套数增购费用
  const accountAddons = Math.max(0, account_count - product.base_account_count);
  if (accountAddons > 0) {
    const addonsConfig = product.addons || {};
    const accountAddonPrice = parseFloat(addonsConfig.account_addon_price || 0);
    totalPrice += (accountAddons * accountAddonPrice);
  }

  // 4. 计算所选功能的总价
  if (selected_features && selected_features.length > 0) {
    // 查询所有选中功能与当前产品的关联关系，以获取它们的价格
    const featureRelations = await ProductFeatureRelation.findAll({
      where: {
        product_id: product.id, // 确保是当前产品的价格
        product_feature_id: selected_features // 筛选出选中的功能
      }
    });

    // 将查询到的功能价格累加到总价中
    const featuresPrice = featureRelations.reduce((sum, relation) => {
      return sum + parseFloat(relation.feature_price);
    }, 0);

    totalPrice += featuresPrice;
  }

  // 5. 根据购买时长（月）计算最终价格
  const durationYears = duration_months / 12;
  const finalPrice = totalPrice * durationYears;

  // 6. 返回最终计算的价格
  return finalPrice;
};

/**
 * @description 兼容旧版本的价格计算方法（保持向后兼容）
 * @deprecated 请使用 calculateProductOrderPrice 方法
 */
exports.calculateOrderItemPrice = exports.calculateProductOrderPrice;