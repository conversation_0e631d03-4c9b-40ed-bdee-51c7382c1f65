// 引入数据库模型。我们通过操作这些模型对象来与数据库交互。
const db = require('../models'); // 引入模型调度中心
const ProductUserAddonTier = db.ProductUserAddonTier;
const Product = db.Product;

/**
 * @description 获取指定产品的所有阶梯价格
 * @param {object} req - Express的请求对象，包含了客户端发来的信息
 * @param {object} res - Express的响应对象，用于向客户端发送响应
 */
exports.getTiersByProductId = async (req, res) => {
  try {
    // [修复] 显式地将URL参数转换为整数，并增加校验，提高代码健壮性
    const productId = parseInt(req.params.productId, 10);
    if (isNaN(productId)) {
      return res.status(400).json({ message: '无效的产品ID格式' });
    }

    // 查询该产品ID关联的所有阶梯价格记录
    const tiers = await ProductUserAddonTier.findAll({
      where: { product_id: productId },
      order: [['min_users', 'ASC']], // 按起始用户数升序排序，确保返回的阶梯是有序的
    });

    // 以JSON格式返回查询到的阶梯价格列表
    res.status(200).json(tiers);
  } catch (error) {
    // 如果发生错误，记录错误日志并向客户端返回500错误
    console.error('获取阶梯价格失败:', error);
    res.status(500).json({ message: '服务器内部错误', error: error.message });
  }
};

/**
 * @description 为指定产品批量创建或更新阶梯价格（覆盖式）
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.bulkCreateOrUpdateTiers = async (req, res) => {
  const productId = req.params.productId; // 从URL中获取产品ID
  const tiersData = req.body.tiers; // 从请求体中获取阶梯价格的数组

  // 校验数据是否存在
  if (!tiersData || !Array.isArray(tiersData)) {
    return res.status(400).json({ message: '请求失败，请提供阶梯价格数组(tiers)。' });
  }

  // 使用数据库事务来保证操作的原子性：所有操作要么全部成功，要么全部失败。
  const transaction = await db.sequelize.transaction();

  try {
    // 1. 在事务中，先删除该产品所有旧的阶梯价格记录
    await ProductUserAddonTier.destroy({
      where: { product_id: productId },
      transaction: transaction, // 确保此操作在事务中执行
    });

    // 2. 如果提交的数组不为空，则将新的阶梯价格数据批量插入
    if (tiersData.length > 0) {
      // 为每一条阶梯数据补充 product_id
      const newTiers = tiersData.map(tier => ({
        ...tier,
        product_id: productId,
      }));
      // 批量创建新的记录
      await ProductUserAddonTier.bulkCreate(newTiers, {
        transaction: transaction, // 确保此操作在事务中执行
      });
    }

    // 3. 如果以上所有操作都成功，提交事务
    await transaction.commit();

    // 向客户端返回成功信息
    res.status(200).json({ message: '阶梯价格已成功更新！' });

  } catch (error) {
    // 4. 如果中间任何一步发生错误，回滚事务，撤销所有已进行的操作
    await transaction.rollback();
    console.error('更新阶梯价格失败:', error);
    res.status(500).json({ message: '服务器内部错误', error: error.message });
  }
}; 