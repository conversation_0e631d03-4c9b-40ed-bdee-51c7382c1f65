# 资产管理模块重构说明

## 概述

本次重构严格按照设计文档要求，重新组织了资产管理模块的前端代码结构，实现了完整的资产CRUD功能和变更管理功能。

## 文件结构

```
frontend/src/views/Asset/
├── components/                    # 可复用组件
│   ├── AssetFormHeader.vue       # 资产表单表头组件
│   ├── AssetProductDetail.vue    # 产品详情组件
│   ├── AssetActivationInfo.vue   # 激活信息组件
│   ├── AssetRelatedOrders.vue    # 关联订单组件
│   ├── AssetChangeFormHeader.vue # 资产变更表单表头组件
│   ├── AssetChangeRecords.vue    # 变更记录组件
├── pages/                        # 页面组件
│   ├── AssetList.vue            # 资产列表页面
│   ├── AssetDetail.vue          # 资产详情页面
│   ├── AssetForm.vue            # 资产表单页面（新增/修改）
│   ├── AssetChangeCreate.vue    # 资产变更创建页面
│   ├── AssetChangeDetail.vue    # 资产变更详情页面
│   └── AssetChangeList.vue      # 资产变更列表页面
├── composables/                  # 数据管理
│   └── useAssetData.js          # 资产数据管理
├── test/                        # 测试文件
│   └── ComponentTest.vue        # 组件测试页面
└── README.md                    # 说明文档
```

## 组件说明

### 1. AssetFormHeader.vue
- **功能**: 处理资产ID、企业ID、用户ID、status字段
- **特性**: 支持只读和编辑模式，企业用户联动选择
- **Props**: formData, readonly, enterpriseOptions, userOptions
- **Events**: enterprise-change, user-change

### 2. AssetProductDetail.vue
- **功能**: 处理产品相关信息（产品版本、使用人数、账套数、购买时长、功能、日期、价格等）
- **特性**: 自动计算到期日期，功能多选，价格输入
- **Props**: formData, readonly, productOptions
- **Events**: product-change, data-change

### 3. AssetActivationInfo.vue
- **功能**: 处理激活码、激活手机号、激活密码
- **特性**: 支持自动生成激活信息，批量操作
- **Props**: formData, readonly
- **Events**: data-change

### 4. AssetRelatedOrders.vue
- **功能**: 处理关联订单列表，支持关联多个订单和联查功能
- **特性**: 新增模式支持选择订单，查看模式显示订单列表
- **Props**: mode, readonly, enterpriseId, relatedOrders, selectedOrderIds
- **Events**: orders-change

### 5. AssetChangeFormHeader.vue
- **功能**: 处理资产变更ID、变更时间、制单人、制单时间、变更备注
- **特性**: 自动生成变更信息，支持变更摘要显示
- **Props**: formData, readonly, changeSummary, originalData, changedData
- **Events**: data-change

### 6. AssetChangeRecords.vue
- **功能**: 处理资产变更记录列表，支持查看变更详情和回滚功能
- **特性**: 分页显示，变更摘要，回滚确认
- **Props**: assetId, readonly
- **Events**: rollback-success

### 7. AssetChangeCompare.vue
- **功能**: 支持只读和编辑模式的变更对比组件，引用其他子组件实现左右对比功能
- **特性**: 左右对比布局，差异高亮，响应式设计
- **Props**: originalData, changedData, editMode, enterpriseOptions, userOptions, productOptions
- **Events**: data-change

## 页面说明

### 1. AssetList.vue
- **功能**: 资产列表页面，包括表格展示、操作按钮、跳转逻辑
- **特性**: 搜索过滤、分页、排序、批量操作
- **路由**: `/assets/list`

### 2. AssetDetail.vue
- **功能**: 资产详情页面，使用标签页组织内容，集成各个子组件
- **特性**: 标签页布局、只读/编辑模式切换
- **路由**: `/assets/detail/:id`

### 3. AssetForm.vue
- **功能**: 资产新增和修改表单页面，支持不同模式，使用标签页组织内容
- **特性**: 新增/编辑模式、表单验证、自动生成ID
- **路由**: `/assets/form/:id?` (query参数mode=add表示新增)

### 4. AssetChangeCreate.vue
- **功能**: 左右分栏的资产变更页面，左侧显示原始数据，右侧支持编辑
- **特性**: 左右对比、实时差异检测、变更记录生成
- **路由**: `/assets/change/create/:id`

### 5. AssetChangeDetail.vue
- **功能**: 查看变更单详情的页面，左右对比显示变更前后数据
- **特性**: 只读对比、变更统计、回滚功能
- **路由**: `/assets/change/detail/:id`

### 6. AssetChangeList.vue
- **功能**: 展示所有资产变更单的列表页面
- **特性**: 搜索过滤、分页、变更摘要显示
- **路由**: `/assets/change/list`

## 数据流设计

### 1. 字段映射
严格按照数据库字段设计，确保前后端数据一致性：
- 资产表：assets
- 变更记录表：asset_changes
- 关联订单：通过中间表关联

### 2. 状态管理
使用 `useAssetData` composable 管理资产数据：
- 支持CRUD操作
- 统一错误处理
- 加载状态管理

### 3. 表单验证
每个组件都有独立的验证规则，支持：
- 必填字段验证
- 格式验证
- 业务逻辑验证

## 特色功能

### 1. 自动生成功能
- 资产ID和变更ID自动生成（支持修改）
- 激活码和密码自动生成
- 制单人自动获取当前登录用户

### 2. 智能联动
- 企业选择后自动过滤用户
- 产品选择后自动填充基础数据
- 购买日期变更后自动计算到期日期

### 3. 变更追溯
- 完整的变更记录
- 左右对比显示
- 支持回滚操作
- 变更摘要自动生成

### 4. 响应式设计
- 支持桌面和移动端
- 自适应布局
- 优化的交互体验

## 使用说明

### 1. 开发环境
确保已安装所有依赖：
```bash
npm install
```

### 2. 测试组件
访问测试页面查看组件效果：
```
/assets/test/components
```

### 3. 路由配置
所有路由已在 `router/index.js` 中配置完成。

### 4. API接口
相关API接口已在 `api/asset.js` 中定义，确保后端接口匹配。

## 注意事项

1. **字段映射**: 严格按照数据库字段命名，避免数据获取失败
2. **中文备注**: 所有代码都有详细的中文注释
3. **Vue最佳实践**: 使用Vue 3 Composition API和推荐的代码结构
4. **错误处理**: 完善的错误处理和用户提示
5. **性能优化**: 合理的组件拆分和数据管理

## 后续扩展

1. 可以根据业务需求添加更多字段
2. 支持批量操作功能
3. 添加数据导入导出功能
4. 集成更多的业务流程
