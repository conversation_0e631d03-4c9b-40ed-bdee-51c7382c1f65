/**
 * 合伙人认证信息页面
 * 用于查看和更新银行卡、身份证等认证信息
 */
const api = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户认证信息
    authInfo: {
      idCard: '',           // 身份证号
      bankCard: '',         // 银行卡号
      realName: '',         // 真实姓名
      bankName: '',         // 银行名称
      status: 'pending'     // 认证状态：pending-待审核, approved-已通过, rejected-已拒绝
    },
    
    // 编辑状态
    isEditing: false,
    submitting: false,
    
    // 表单数据
    formData: {
      idCard: '',
      bankCard: '',
      bankName: ''
    }
  },

  /**
   * 页面加载
   */
  onLoad() {
    // 检查登录状态
    if (!api.checkPartnerAuth()) {
      return;
    }
    
    wx.setNavigationBarTitle({
      title: '认证信息'
    });
    
    this.loadAuthInfo();
  },

  /**
   * 加载认证信息
   */
  async loadAuthInfo() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const result = await api.getAuthInfo();
      
      if (result.success) {
        const authInfo = result.data || {};
        this.setData({
          authInfo: {
            idCard: authInfo.id_card || '',
            bankCard: authInfo.bank_card || '',
            realName: authInfo.real_name || '',
            bankName: authInfo.bank_name || '',
            status: authInfo.status || 'pending'
          },
          formData: {
            idCard: authInfo.id_card || '',
            bankCard: authInfo.bank_card || '',
            bankName: authInfo.bank_name || ''
          }
        });
      }
      
    } catch (error) {
      console.error('加载认证信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 开始编辑
   */
  startEdit() {
    this.setData({
      isEditing: true,
      formData: {
        idCard: this.data.authInfo.idCard,
        bankCard: this.data.authInfo.bankCard,
        bankName: this.data.authInfo.bankName
      }
    });
  },

  /**
   * 取消编辑
   */
  cancelEdit() {
    this.setData({
      isEditing: false
    });
  },

  /**
   * 处理表单输入
   */
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.idCard.trim()) {
      wx.showToast({ title: '请输入身份证号', icon: 'none' });
      return false;
    }
    
    // 身份证号格式验证
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(formData.idCard)) {
      wx.showToast({ title: '请输入正确的身份证号', icon: 'none' });
      return false;
    }
    
    if (!formData.bankCard.trim()) {
      wx.showToast({ title: '请输入银行卡号', icon: 'none' });
      return false;
    }
    
    // 银行卡号格式验证
    const bankCardRegex = /^\d{16,19}$/;
    if (!bankCardRegex.test(formData.bankCard)) {
      wx.showToast({ title: '请输入正确的银行卡号', icon: 'none' });
      return false;
    }
    
    if (!formData.bankName.trim()) {
      wx.showToast({ title: '请输入银行名称', icon: 'none' });
      return false;
    }
    
    return true;
  },

  /**
   * 保存认证信息
   */
  async saveAuthInfo() {
    if (!this.validateForm()) {
      return;
    }
    
    if (this.data.submitting) {
      return;
    }
    
    this.setData({ submitting: true });
    
    try {
      const { formData } = this.data;
      
      const result = await api.updateAuthInfo({
        idCard: formData.idCard.trim(),
        bankCard: formData.bankCard.trim(),
        bankName: formData.bankName.trim()
      });
      
      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 更新本地数据
        this.setData({
          'authInfo.idCard': formData.idCard,
          'authInfo.bankCard': formData.bankCard,
          'authInfo.bankName': formData.bankName,
          'authInfo.status': 'pending', // 重新提交后状态变为待审核
          isEditing: false
        });
        
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('保存认证信息失败:', error);
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 复制信息到剪贴板
   */
  copyToClipboard(e) {
    const { text } = e.currentTarget.dataset;
    if (text) {
      wx.setClipboardData({
        data: text,
        success: () => {
          wx.showToast({
            title: '已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  /**
   * 获取状态显示文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取状态样式类名
   */
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'approved': 'status-approved',
      'rejected': 'status-rejected'
    };
    return classMap[status] || '';
  }
});
