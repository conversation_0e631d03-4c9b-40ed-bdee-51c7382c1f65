import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { useAuth } from './store/auth' // 1. 引入认证 store

// 引入 Element Plus 的样式和组件库
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 2. 在应用启动时，初始化认证状态
// 这会检查 localStorage 中是否有 token，并恢复登录状态
const { initAuthStore } = useAuth();
initAuthStore();

const app = createApp(App)

app.use(router)
app.use(ElementPlus) // 全局注册 Element Plus

app.mount('#app')
