图片上传到阿里云云服务器 ECSUbuntu 24.04 64位，域名：8.138.207.
已有项目目录：/var/www/BeconNetCode/、/root/partnerprogram/，还有数据库

存储隔离：图片存储在专属目录 /var/www/miniprogram_images
访问隔离：通过独立域名/IP端口访问
权限隔离：专属Nginx配置，不干扰现有项目

1. 检查sites-enabled目录（只保留有效配置）
sudo ls -l /etc/nginx/sites-enabled
# 应只有 beconnet 和 default 的软链接，和 miniprogram_images.conf

2. 创建专属Nginx配置（关键步骤）
sudo nano /etc/nginx/sites-available/miniprogram_images.conf
使用子域名访问（推荐生产环境）,记得将域名解析到服务器IP！
https://mshop.bogoo.net/hsy_feature5.png

3. 启用新配置（阿里云服务器端执行）
# 创建软链接
sudo ln -s /etc/nginx/sites-available/miniprogram_images.conf /etc/nginx/sites-enabled/
# 测试配置
sudo nginx -t
# 出现 successful 才能继续
# 重启Nginx
sudo systemctl restart nginx

4. 本地图片上传至阿里云服务器
第一步：准备本地图片
将小程序所有图片整理到一个文件夹（如 miniprogram_images）
确保图片命名规范（建议英文+数字，避免中文和特殊字符）
检查图片格式（推荐：JPG/PNG/WEBP）

第二步：上传图片到服务器
方法1：SCP命令（最推荐）
# 打开本地终端（Windows用PowerShell，Mac用Terminal）
# 进入图片所在目录
cd D:\miniprogram_images
# 执行上传（示例路径替换为你的实际路径）
scp -r ./* root@*************:/var/www/miniprogram_images/
# 输入服务器root密码后开始传输

第三步：设置权限（阿里云服务器端执行）
为什么需要设置权限？
Nginx默认以 www-data 用户运行
上传的文件默认属于 root 用户
权限错误会导致 403 Forbidden 错误
# 1. 登录服务器
ssh root@*************
# 2. 进入图片目录
cd /var/www/miniprogram_images
# 3. 递归修改所有者（让Nginx有读取权）
sudo chown -R www-data:www-data .
# 4. 设置目录权限（755 = 所有者可读写执行，其他人可读执行）
sudo find . -type d -exec chmod 755 {} \;
# 5. 设置文件权限（644 = 所有者可读写，其他人只读）
sudo find . -type f -exec chmod 644 {} \;
# 6. 验证权限
ls -l  # 应该看到类似：
# drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 17 images_dir
# -rw-r--r-- 1 <USER> <GROUP> 10240 Jun 17 example.jpg

第四步：验证图片可访问性
# 在服务器上测试（替换为你的实际图片名）
curl -I https://mshop.bogoo.net/hsy_feature5.png  # 使用你的端口
# 或在本地浏览器访问（根据你的Nginx配置）：
https://mshop.bogoo.net/hsy_feature5.png
期望结果：返回HTTP 200状态码并显示图片

安全加固措施
防火墙开放端口（仅方案一需要）：
sudo ufw allow 8080/tcp
防盗链设置（Nginx配置中添加）：
location ~ \.(jpg|png|gif)$ {
    valid_referers none blocked ************* *.yourdomain.com;
    if ($invalid_referer) {
        return 403;
    }
}

故障排查指南
问题1：上传后图片无法访问（403错误）
# 检查三步曲：
1. 所有者：ls -ld /var/www/miniprogram_images  # 应为www-data
2. 目录权限：ls -ld /var/www/miniprogram_images # 应为drwxr-xr-x
3. 文件权限：ls -l /var/www/miniprogram_images/test.jpg # 应为-rw-r--r--
# 修复命令：
sudo chown -R www-data:www-data /var/www/miniprogram_images
sudo chmod 755 /var/www/miniprogram_images
sudo find /var/www/miniprogram_images -type f -exec chmod 644 {} \;

问题2：图片上传中断
原因：网络不稳定或大文件超时
解决方案：
# 使用rsync支持断点续传
rsync -P -e ssh /本地/图片/* root@*************:/var/www/miniprogram_images
