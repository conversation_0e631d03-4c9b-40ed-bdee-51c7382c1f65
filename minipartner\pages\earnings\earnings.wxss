/* pages/earnings/earnings.wxss */
.container {
  padding: 30rpx 20rpx;
  background: #F8F8F8;
  min-height: 100vh;
}

.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 收益概览 */
.overview .amount-row {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.amount-item {
  text-align: center;
  flex: 1;
}

.amount-item .label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.amount-item .amount {
  font-size: 36rpx;
  color: #0FB9B1;
  font-weight: bold;
}

/* 月结说明 */
.settlement .info {
  font-size: 28rpx;
  color: #666;
}

/* 筛选器 - 新设计 */
.filters {
  margin-bottom: 20rpx;
}

/* 搜索栏和筛选容器 */
.search-filter-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  background: #F6F6F6;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  flex: 1;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  padding: 0 10rpx;
}

/* 筛选按钮包装器 */
.filter-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70rpx;
}

.filter-icon {
  width: 46rpx;
  height: 46rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.filter-icon image {
  width: 36rpx;
  height: 36rpx;
}

.filter-icon.active image {
  color: #0FB9B1;
}

.filter-text {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}

.icon-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #FF5757;
  border-radius: 50%;
}

/* 状态筛选标签 */
.status-filter {
  display: flex;
  flex-wrap: wrap;
  margin-top: 15rpx;
}

.filter-item {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 24rpx;
  margin-right: 15rpx;
  background-color: #F6F6F6;
}

.filter-item.active {
  background-color: #0FB9B1;
  color: #fff;
}

/* 筛选弹窗 */
.filter-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.filter-popup.show {
  visibility: visible;
  opacity: 1;
}

.filter-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.filter-popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-popup.show .filter-popup-content {
  transform: translateY(0);
}

.filter-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
  font-size: 30rpx;
  font-weight: bold;
}

.filter-popup-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.filter-section {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.filter-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.time-filter-options {
  display: flex;
  flex-wrap: wrap;
}

.time-option {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background: #F6F6F6;
  border-radius: 24rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
}

.time-option.active {
  background: #0FB9B1;
  color: #fff;
}

.custom-time-range {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.date-input {
  width: 48%;
  margin-bottom: 15rpx;
}

.date-input text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.date-picker-field {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333;
  background: #F6F6F6;
  border-radius: 6rpx;
}

.filter-actions {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
}

.filter-actions button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.reset-btn {
  background: #F6F6F6;
  color: #666;
}

.confirm-btn {
  background: #0FB9B1;
  color: #fff;
}

/* 订单列表 */
.order-item {
  border-bottom: 1rpx solid #F0F0F0;
  padding: 20rpx 0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.order-id {
  font-size: 26rpx;
  color: #333;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.product-info {
  margin-bottom: 15rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.product-name .version {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
  margin-left: 10rpx;
}

.company-name {
  font-size: 26rpx;
  color: #666;
}

.price-info .price-row {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.commission-row {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  align-items: center;
}

.commission {
  color: #FF9800;
  font-weight: bold;
  margin-right: auto;
}

.status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status.pending {
  background: #FFF3E0;
  color: #FF9800;
}

.status.paid {
  background: #E0F2F1;
  color: #0FB9B1;
}


/* 底部提示 */
.list-footer {
  text-align: center;
  padding: 20rpx 0;
}

.no-more {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #0FB9B1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主容器 */
.earnings-container {
  min-height: 100vh;
  background-color: #f7f8fc;
  padding-bottom: 40rpx;
  position: relative;
}

/* 加载状态 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0FB9B1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 顶部卡片区域 */
.header-section {
  padding: 30rpx 30rpx 10rpx;
}

.overview-card {
  background: linear-gradient(135deg, #0FB9B1, #38e7df);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(15, 185, 177, 0.2);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.overview-card:before {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.overview-card:after {
  content: '';
  position: absolute;
  bottom: -40rpx;
  left: -40rpx;
  width: 180rpx;
  height: 180rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  z-index: 0;
}

.overview-header {
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.overview-title {
  font-size: 36rpx;
  font-weight: 600;
}

.overview-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
  padding: 0 10rpx;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-divider {
  width: 2rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 15rpx;
}

.overview-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12rpx;
}

.overview-value {
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.overview-value.total {
  color: #FFD700;
  font-size: 52rpx;
}

.overview-footer {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  margin-top: 10rpx;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.settlement-info {
  display: flex;
  align-items: center;
}

.settlement-icon {
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  margin-right: 15rpx;
  position: relative;
}

.settlement-icon:before {
  content: '';
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.settlement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 订单区域 */
.orders-section {
  padding: 30rpx 30rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: 20rpx;
  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.05);
  min-height: 70vh;
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}

.section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: #0FB9B1;
  border-radius: 4rpx;
}

/* 筛选区域 */
.filter-area {
  margin-bottom: 30rpx;
}

.search-filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-wrapper {
  flex: 1;
  height: 80rpx;
  background: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') center no-repeat;
  margin-right: 10rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.filter-icon {
  width: 48rpx;
  height: 48rpx;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/></svg>') center no-repeat;
  position: relative;
}

.filter-icon.active {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230FB9B1"><path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/></svg>') center no-repeat;
}

.filter-dot {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff5757;
  border-radius: 50%;
}

.filter-text {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 状态选项卡 */
.status-tabs {
  display: flex;
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f7fa;
  margin-top: 20rpx;
}

.status-tab {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.status-tab.active {
  color: #fff;
  background: #0FB9B1;
  font-weight: 500;
}

/* 订单列表 */
.order-list {
  margin-top: 30rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.order-header {
  padding: 25rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: #fafafa;
}

.order-number {
  display: flex;
  align-items: center;
}

.order-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}

.order-id {
  font-size: 26rpx;
  color: #666;
  font-family: monospace;
}

.order-date {
  font-size: 24rpx;
  color: #999;
}

.order-content {
  padding: 25rpx 30rpx;
}

.product-info {
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 12rpx;
  background: #F8F9FA;
  border-radius: 6rpx;
  border-left: 3rpx solid #0FB9B1;
}

.service-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.service-price {
  font-size: 26rpx;
  color: #0FB9B1;
  font-weight: 500;
}

.product-version {
  font-size: 24rpx;
  color: #666;
  background: #f5f7fa;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  display: inline-block;
}

.company-name {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 5rpx;
}

.price-details {
  padding: 15rpx 0;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.price-value {
  font-size: 26rpx;
  color: #333;
}

.price-value.highlight {
  color: #0FB9B1;
  font-weight: bold;
}

.order-footer {
  padding: 20rpx 30rpx;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px dashed rgba(0, 0, 0, 0.06);
}

.commission-info {
  display: flex;
  align-items: center;
}

.commission-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.commission-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff9500;
}

.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.status-badge.pending {
  background: rgba(255, 149, 0, 0.1);
  color: #ff9500;
}

.status-badge.paid {
  background: rgba(15, 185, 177, 0.1);
  color: #0FB9B1;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/><path d="M12 12.5c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.8 2.5H8.2c-.7 0-1.2-.6-1.2-1.2 0-1.7 1.3-3 3-3h6c1.7 0 3 1.3 3 3 0 .6-.5 1.2-1.2 1.2z"/></svg>') center no-repeat;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 列表底部 */
.list-footer {
  text-align: center;
  padding: 30rpx 0;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.filter-modal.visible {
  visibility: visible;
  opacity: 1;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  overflow: hidden;
}

.filter-modal.visible .modal-container {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-button {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>') center no-repeat;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 30rpx;
}

.filter-group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.time-options {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}

.time-option {
  padding: 12rpx 30rpx;
  background: #f5f7fa;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  transition: all 0.3s;
}

.time-option.active {
  background: #0FB9B1;
  color: #fff;
}

.date-range {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.date-field {
  width: 48%;
}

.date-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.date-picker {
  background: #f5f7fa;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #999;
}

.date-picker.has-value {
  color: #333;
}

.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 50rpx;
  border-top: 1px solid #f0f0f0;
}

.reset-button, .confirm-button {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
}

.reset-button {
  background: #f5f7fa;
  color: #666;
  margin-right: 15rpx;
}

.confirm-button {
  background: #0FB9B1;
  color: #fff;
}
