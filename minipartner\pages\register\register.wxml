<!--合伙人注册页面-->
<view class="register-container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="title">申请成为合伙人</view>
    <view class="subtitle">填写以下信息，等待管理员审核</view>
  </view>

  <!-- 注册表单 -->
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <!-- 真实姓名 -->
      <view class="form-item">
        <view class="label">真实姓名 *</view>
        <input 
          class="input" 
          placeholder="请输入真实姓名"
          value="{{formData.name}}"
          data-field="name"
          bindinput="handleInput"
          maxlength="20"
        />
      </view>
      
      <!-- 手机号 -->
      <view class="form-item">
        <view class="label">手机号 *</view>
        <input 
          class="input" 
          placeholder="请输入手机号"
          value="{{formData.mobile}}"
          data-field="mobile"
          bindinput="handleInput"
          type="number"
          maxlength="11"
        />
      </view>
      
      <!-- 邮箱 -->
      <view class="form-item">
        <view class="label">邮箱</view>
        <input 
          class="input" 
          placeholder="请输入邮箱（选填）"
          value="{{formData.email}}"
          data-field="email"
          bindinput="handleInput"
          type="email"
        />
      </view>
    </view>

    <!-- 登录信息 -->
    <view class="form-section">
      <view class="section-title">登录信息</view>
      
      <!-- 密码 -->
      <view class="form-item">
        <view class="label">登录密码 *</view>
        <view class="password-input">
          <input 
            class="input" 
            placeholder="请输入登录密码（至少6位）"
            value="{{formData.password}}"
            data-field="password"
            bindinput="handleInput"
            password="{{!showPassword}}"
            maxlength="20"
          />
          <view 
            class="password-toggle"
            data-type="password"
            bindtap="togglePasswordVisibility"
          >
            {{showPassword ? '隐藏' : '显示'}}
          </view>
        </view>
      </view>
      
      <!-- 确认密码 -->
      <view class="form-item">
        <view class="label">确认密码 *</view>
        <view class="password-input">
          <input 
            class="input" 
            placeholder="请再次输入密码"
            value="{{formData.confirmPassword}}"
            data-field="confirmPassword"
            bindinput="handleInput"
            password="{{!showConfirmPassword}}"
            maxlength="20"
          />
          <view 
            class="password-toggle"
            data-type="confirm"
            bindtap="togglePasswordVisibility"
          >
            {{showConfirmPassword ? '隐藏' : '显示'}}
          </view>
        </view>
      </view>
    </view>

    <!-- 认证信息 -->
    <view class="form-section">
      <view class="section-title">认证信息</view>
      
      <!-- 身份证号 -->
      <view class="form-item">
        <view class="label">身份证号 *</view>
        <input 
          class="input" 
          placeholder="请输入身份证号"
          value="{{formData.idCard}}"
          data-field="idCard"
          bindinput="handleInput"
          maxlength="18"
        />
      </view>
      
      <!-- 银行卡号 -->
      <view class="form-item">
        <view class="label">银行卡号 *</view>
        <input 
          class="input" 
          placeholder="请输入银行卡号"
          value="{{formData.bankCard}}"
          data-field="bankCard"
          bindinput="handleInput"
          type="number"
          maxlength="19"
        />
      </view>
      
      <!-- 申请说明 -->
      <view class="form-item">
        <view class="label">申请说明</view>
        <textarea 
          class="textarea" 
          placeholder="请简要说明申请成为合伙人的原因（选填）"
          value="{{formData.remark}}"
          data-field="remark"
          bindinput="handleInput"
          maxlength="200"
          auto-height
        />
      </view>
    </view>

    <!-- 协议同意 -->
    <view class="protocol-section">
      <view class="protocol-item" bindtap="toggleProtocol">
        <view class="checkbox {{agreedProtocol ? 'checked' : ''}}">
          <view class="checkmark" wx:if="{{agreedProtocol}}">✓</view>
        </view>
        <view class="protocol-text">
          我已阅读并同意
          <text class="protocol-link" bindtap="viewProtocol">《合伙人协议》</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="button-section">
      <button 
        class="submit-btn {{submitting ? 'disabled' : ''}}"
        bindtap="handleSubmit"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交申请'}}
      </button>
      
      <view class="login-link" bindtap="goToLogin">
        已有账号？返回登录
      </view>
    </view>
  </view>
</view>
