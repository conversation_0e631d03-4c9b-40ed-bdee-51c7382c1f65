<view class="login-container">
  <!-- 背景波浪效果 -->
  <view class="wave-container">
    <view class="wave wave1"></view>
    <view class="wave wave2"></view>
  </view>
  
  <!-- Logo部分 -->
  <view class="logo-container">
    <image class="logo-image" src="/images/logo.png" mode="aspectFit"></image>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-header">
      <text class="form-title"></text>
      <text class="form-subtitle">欢迎回来，请登录您的账号</text>
    </view>
    
    <view class="input-container">
      <view class="input-wrapper">
        <view class="input-icon">
          <view class="icon-phone"></view>
        </view>
        <input 
          class="input-field"
          type="number" 
          placeholder="请输入手机号" 
          bindinput="handlePhoneInput"
          value="{{phone}}"
        />
      </view>
      
      <view class="input-wrapper">
        <view class="input-icon">
          <view class="icon-lock"></view>
        </view>
        <input 
          class="input-field"
          type="password" 
          placeholder="请输入密码" 
          bindinput="handlePasswordInput"
          value="{{password}}"
        />
      </view>
    </view>

    <!-- 错误提示区域 -->
    <view class="error-tip" wx:if="{{errorMessage}}">
      <icon type="warn" size="16"></icon>
      <text>{{errorMessage}}</text>
    </view>

    <!-- 登录按钮 -->
    <button class="login-btn" bindtap="handleLogin">登录</button>

    <!-- 微信手机快速验证登录 -->
    <button
      class="wechat-login-btn"
      open-type="getPhoneNumber"
      bindgetphonenumber="onGetPhoneNumber"
      phone-number-no-quota-toast="{{false}}"
    >
      <view class="btn-content">
        <text class="icon-wechat">📱</text>
        <text class="btn-text">微信手机快速验证</text>
      </view>
    </button>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <text class="action-link" bindtap="handleForgetPassword">忘记密码</text>
      <text class="action-link" bindtap="handleRegister">没账号？注册合伙人</text>
    </view>
  </view>
</view> 