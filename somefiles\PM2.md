# 1. 进入项目目录
cd /var/www/customer_system

# 2. 停止旧服务
pm2 delete customer-frontend customer-backend

# 3. 启动新服务
pm2 start ecosystem.config.js

# 4. 设置开机启动
pm2 save
pm2 startup
# 执行提示的命令

# 5. 查看服务状态
pm2 list

# 6. 检查端口
sudo lsof -i :5173
sudo lsof -i :3002

# 监控实时日志（Ctrl+C退出）
pm2 logs customer-backend --lines 100


# 验证端口监听
ss -tuln | grep 3002  # 确认后端端口监听



# 停止所有服务
pm2 stop all
# 删除所有服务
pm2 delete all
# 清除日志
pm2 flush
# 从配置文件启动
pm2 start ecosystem.config.js
# 保存状态
pm2 save
pm2 logs customer-backend --lines 20