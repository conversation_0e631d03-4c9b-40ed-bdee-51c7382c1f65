/* pages/order-detail/order-detail.wxss */

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(102, 126, 234, 0.95);
  backdrop-filter: blur(10rpx);
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.navbar-right {
  width: 80rpx;
}

/* 内容区域 */
.content-container {
  padding-top: calc(var(--status-bar-height, 44rpx) + 88rpx + 32rpx);
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-size: 28rpx;
  opacity: 0.8;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.retry-btn {
  margin-top: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 信息卡片 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.status-header {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.status-icon.pending {
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
}

.status-icon.paid {
  background: linear-gradient(135deg, #55efc4, #00b894);
}

.status-icon.completed {
  background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

.status-icon.cancelled {
  background: linear-gradient(135deg, #fd79a8, #e84393);
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-text.pending {
  color: #e17055;
}

.status-text.paid {
  color: #00b894;
}

.status-text.completed {
  color: #6c5ce7;
}

.status-text.cancelled {
  color: #e84393;
}

.status-desc {
  font-size: 24rpx;
  color: #666;
}

/* 订单信息列表 */
.order-info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  word-break: break-all;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.product-item {
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.product-info {
  flex: 1;
  margin-right: 24rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.product-spec, .product-quantity {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.product-price {
  text-align: right;
}

.unit-price {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.total-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #e17055;
}

/* 价格汇总 */
.price-summary {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.summary-item.total {
  border-top: 2rpx solid #e0e0e0;
  padding-top: 24rpx;
  margin-top: 16rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-item.total .summary-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #e17055;
}

.summary-item.total .summary-value {
  font-size: 36rpx;
  color: #e17055;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
}
