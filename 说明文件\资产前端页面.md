src/
├── views/
│   ├── Asset/
│   │   ├── pages/
│   │   │   ├── AssetList.vue      # 资产列表页面
│   │   │   ├── AssetDetail.vue    # 资产详情页面(查看资产详情)
│   │   │   ├── AssetForm.vue      # 新增资产和修改资产的表单页面
│   │   │   ├── AssetChangeCreate.vue    # 创建新的资产变更单
│   │   │   ├── AssetChangeDetail.vue   # 查看某次变更的详细信息
│   │   │   └── AssetChangeList.vue     # 资产变更列表页面
│   │   │
│   │   └── components/
│   │       ├── AssetFormHeader.vue        # 资产表单的表头部分     
│   │       ├── AssetProductDetail.vue    # 产品详情组件
│   │       ├── AssetActivationInfo.vue    # 激活信息组件
│   │       ├── AssetRelatedOrders.vue    # 关联订单组件
│   │       ├── AssetChangeFormHeader.vue  # 资产变更表单的表头
│   │       ├── AssetChangeRecords.vue    # 变更记录组件
│   │       └── AssetChangeCompare.vue      # 资产变更对比组件

# 组件内容
## AssetFormHeader.vue
处理：资产ID、企业ID、用户ID、status字段。

## AssetProductDetail.vue    # 产品详情组件
处理：产品ID-版本号、使用人数、账套数、购买时长、勾选产品功能、购买日期、到期日-产品到期日、到期日-sps到期日、到期日-服务到期日、价格信息-产品标准价、价格信息-sps年费、价格信息-售后服务费用、价格信息-实施费用。

## AssetActivationInfo.vue    # 激活信息组件
处理：激活码、激活手机号、激活密码。

## AssetRelatedOrders.vue    # 关联订单组件
处理：关联订单列表，可关联多个订单。

## AssetChangeFormHeader.vue
处理：资产变更ID、资产变更时间、制单人、制单时间、变更备注。

## AssetChangeRecords.vue    # 变更记录组件
处理：资产变更记录列表，可关联多个资产变更单。

## AssetChangeCompare.vue
- 结构：引用：AssetFormHeader.vue、AssetProductDetail.vue、AssetActivationInfo.vue组件！
- 支持两种模式：只读模式（用于查看）和编辑模式（用于创建变更单时的右侧编辑部分）。
- 在编辑模式下，当用户修改字段时，子组件会触发事件，AssetChangeCompare.vue会监听这些事件，更新本地数据，并通过一个事件（例如`change`）将整个变更后的数据对象传递给父组件（AssetChangeCreate.vue）





# 页面逻辑
## AssetList.vue
        - 展示资产列表表格，包含资产ID、企业ID、用户ID、资产状态等很多字段。
        - 提供“新增”按钮，跳转到新增资产页面。
        - 每一行资产有操作列：修改、变更、删除（删除需要二次确认）。
        - 点击资产ID跳转到资产详情页面。

## AssetDetail.vue
        - 通过路由参数获取资产ID，从Vuex或API获取该资产的详细信息。
            - 表头：使用AssetFormHeader.vue组件。
        - 使用标签页（Tabs）组织内容：产品详情、激活信息、关联订单、变更记录。
            - 产品详情：使用AssetProductDetail组件（只读模式）
            - 激活信息：使用AssetActivationInfo组件（只读模式）
            - 关联订单：使用AssetRelatedOrders组件（展示该资产关联的所有订单，可点击查看订单详情）
            - 变更记录：使用AssetChangeRecords组件（展示该资产的所有变更记录，可点击查看变更单详情AssetChangeDetail.vue，并提供“回滚”按钮）
        - 页面顶部有操作按钮：编辑（跳转到AssetForm编辑模式）、变更（跳转到AssetChangeCreate页面）、删除、返回。
        - 表尾：备注、制单人、制单时间。

## AssetForm.vue 
       - 通过props传入mode（'add'或'edit'）来决定是新增还是修改。
        - 使用AssetFormHeader组件（可编辑或只读取决于模式）。
        - 使用标签页（Tabs）组织内容：
           - 产品详情：使用AssetProductDetail组件
           - 激活信息：使用AssetActivationInfo组件
           - 关联订单：使用AssetRelatedOrders组件（新增时才有，修改时没有关联订单标签页，因为关联订单在详情页查看）
          - 注意：在新增资产时，关联订单是一个按钮，点击弹出订单列表（该企业下未绑定资产的订单），选择后关联。
        - 表尾部分：备注、制单人（自动填充当前登录员工）、制单时间（创建表单时填充当前时间，保存时确定）。
        - 保存时根据模式调用不同的API（新增或更新）。

## AssetChangeCreate.vue
        - 创建新的变更资产页面，左右分栏布局。
        - 数据流：加载原始资产数据 → 显示在左侧。右侧初始化与原始数据相同，但可编辑。
        - 表头：使用AssetChangeFormHeader.vue组件。资产变更ID、资产变更时间、制单人、制单时间、变更备注。
        - 表体：使用AssetChangeCompare.vue组件。（左右两侧都使用这个组件，但左侧传入只读模式，右侧传入编辑模式）
        - 先放还有一个标签页：关联变更订单（使用AssetRelatedOrders组件，但这里只用于手工选择变更订单）。
        - 保存时，生成资产变更记录，并更新资产信息（同时更新原资产的信息，并将变更记录保存到历史）。

## AssetChangeDetail.vue
    - 功能：查看已创建的变更单的详细信息（包括变更前后的数据）
    - UI特点：左右两栏对比，但都是只读模式，展示变更前和变更后的数据
    - 数据流：根据变更单ID，从后端获取变更单记录（包含变更前后的完整数据快照）
    - 表头：使用AssetChangeFormHeader.vue组件。资产变更ID、资产变更时间、制单人、制单时间、变更备注。
    - 表体组件：AssetChangeCompare（左右两侧都使用这个组件，且都传入只读模式）
    -还要显示关联变更订单的列表。

## AssetChangeList.vue
        - 展示所有资产变更单的列表，包含资产变更ID、变更时间、变更备注等字段。
        - 点击资产变更ID跳转到AssetChangeDetail页面。

注意事项：
    - 在新增资产时，关联订单是一个按钮，点击后弹出对话框（可在AssetRelatedOrders组件内实现），选择订单后，将订单ID与资产关联。同时，订单状态更新为已绑定资产。该订单的资产ID字段会被更新为当前新增的资产ID。
    - 变更资产时，关联变更订单同样是一个按钮，选择后关联到本次变更单（变更单与订单关联，同时资产与订单的关联关系在资产详情中的关联订单列表里也会体现）。
    - 资产变更记录在保存变更单时生成，记录变更的字段和变更前后的值（在变更单的备注中自动生成变更信息，同时变更记录表中保存详细变更数据）。可设置自动检测并高亮显示变更字段。
    - 回滚功能：在变更记录组件AssetChangeRecords.vue中，点击回滚按钮，弹出确认框，确认后调用回滚接口，将资产恢复到该变更前的状态。
    - 在进入编辑、变更等页面时，检查用户权限（如果系统有角色权限控制）。