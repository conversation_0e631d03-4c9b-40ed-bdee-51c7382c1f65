/* 组件容器 */
.promo-component-container {
  position: relative;
  width: 100%;
}

/* 普通卡片容器 */
.promo-card-container {
  padding: 0;
  box-sizing: border-box;
  max-height: 120rpx;
  overflow: visible;
  position: relative;
  z-index: 50;
  margin: 0;
}

/* 现代悬浮式促销卡片 */
.promo-card-float {
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 30rpx rgba(99, 102, 241, 0.3);
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  height: 120rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  
  /* 入场动画初始状态 */
  opacity: 0;
  transform: translateY(20rpx);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

/* 入场动画激活状态 */
.promo-card-float.enter {
  opacity: 1;
  transform: translateY(0);
}

.promo-card-float:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(99, 102, 241, 0.2);
}

/* 卡片装饰元素 */
.card-decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.2;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  z-index: 0;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -40rpx;
  left: -20rpx;
  animation: float-slow 8s infinite alternate;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: -20rpx;
  right: 100rpx;
  animation: float-slow 6s infinite alternate-reverse;
}

.card-decoration-dots {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  right: 10rpx;
  top: 10rpx;
  background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 10rpx 10rpx;
  z-index: 0;
  opacity: 0.5;
}

.card-decoration-line {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  z-index: 0;
}

.line-1 {
  width: 100rpx;
  height: 2rpx;
  bottom: 30rpx;
  left: 20rpx;
  transform: rotate(-30deg);
}

.line-2 {
  width: 60rpx;
  height: 2rpx;
  top: 40rpx;
  right: 40rpx;
  transform: rotate(45deg);
}

@keyframes float-slow {
  0% { transform: translateY(0) scale(1); }
  100% { transform: translateY(-10rpx) scale(1.1); }
}

/* 礼物图标容器 */
.gift-icon-wrapper {
  position: relative;
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.gift-icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  z-index: 0;
}

.gift-icon {
  font-size: 40rpx;
  z-index: 1;
}

/* 促销内容 */
.promo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 5rpx;
  z-index: 1;
}

.promo-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.promo-label {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-right: 12rpx;
  letter-spacing: 1rpx;
}

.highlight-text {
  font-size: 42rpx;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-flex;
  align-items: baseline;
}

.discount-unit {
  font-size: 28rpx;
  font-weight: 600;
  margin-left: 2rpx;
}

.promo-discount {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-original {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: line-through;
  font-size: 22rpx;
}

.price-now {
  color: #ffffff;
  font-weight: bold;
  font-size: 26rpx;
  position: relative;
}

/* 箭头指示器 */
.arrow-indicator {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  z-index: 1;
}

.arrow-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  animation: arrow-bounce 1.5s infinite;
}

@keyframes arrow-bounce {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5rpx); }
} 