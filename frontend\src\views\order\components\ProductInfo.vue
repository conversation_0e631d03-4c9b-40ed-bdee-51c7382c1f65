<template>
  <div class="product-info">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="80px">
      <div class="product-layout">
        <!-- 左侧配置区域 -->
        <div class="config-section">
          <!-- 产品选择 -->
          <el-form-item label="产品" prop="product_id">
            <el-select 
              v-model="formData.product_id" 
              :disabled="!isEditing"
              placeholder="请选择产品"
              style="width: 100%"
              @change="handleProductChange"
              filterable
            >
              <el-option
                v-for="product in products"
                :key="product.id"
                :label="`${product.product_name} - ${product.version_name || ''}`"
                :value="product.id"
              />
            </el-select>
          </el-form-item>

          <!-- 使用人数和账套数 -->
          <div class="config-row">
            <div class="config-item">
              <el-form-item label="使用人数" prop="user_count">
                <el-input-number
                  v-model="formData.user_count"
                  :disabled="!isEditing"
                  :min="selectedProduct?.base_user_count || 1"
                  controls-position="right"
                  @change="handleUserCountChange"
                />
              </el-form-item>
            </div>
            <div class="config-item">
              <el-form-item label="账套数" prop="account_count">
                <el-input-number
                  v-model="formData.account_count"
                  :disabled="!isEditing"
                  :min="selectedProduct?.base_account_count || 1"
                  controls-position="right"
                  @change="handleAccountCountChange"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 购买时长 -->
          <div class="config-row">
            <div class="config-item">
              <el-form-item label="购买时长" prop="duration_months">
                <el-input-number 
                  v-model="formData.duration_months"
                  :disabled="!isEditing"
                  :min="1"
                  :max="60"
                  controls-position="right"
                  @change="handleDurationChange"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 有效月份选择 -->
          <div class="config-row">
            <div class="config-item full-width">
              <el-form-item label="有效月份">
                <div class="month-selector">
                  <el-button 
                    v-for="month in [12, 24, 36]" 
                    :key="month"
                    :type="formData.duration_months === month ? 'primary' : 'default'"
                    size="small"
                    @click="selectMonth(month)"
                    :disabled="!isEditing"
                  >
                    {{ month }}月
                  </el-button>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 功能管理 -->
          <div class="config-row">
            <div class="config-item full-width">
              <el-form-item label="功能管理">
                <div class="feature-tags-container">
                  <!-- 已选功能 -->
                  <div v-if="selectedFeatures.length > 0" class="feature-section">
                    <span class="section-label">已选功能</span>
                    <div class="feature-tags">
                      <el-tag
                        v-for="feature in selectedFeatures"
                        :key="feature.id"
                        type="success"
                        :closable="isEditing && !feature.is_required"
                        @close="toggleFeature(feature)"
                        class="feature-tag-item"
                      >
                        {{ feature.feature_name }} (¥{{ parseFloat(feature.price || 0).toFixed(2) }})
                      </el-tag>
                    </div>
                  </div>

                  <!-- 可选功能 -->
                  <div v-if="unselectedFeatures.length > 0" class="feature-section">
                    <span class="section-label">可选功能</span>
                    <div class="feature-tags">
                      <el-tag
                        v-for="feature in unselectedFeatures"
                        :key="feature.id"
                        type="info"
                        :class="{ 'clickable': isEditing }"
                        @click="isEditing ? toggleFeature(feature) : null"
                        class="feature-tag-item"
                      >
                        {{ feature.feature_name }} (¥{{ parseFloat(feature.price || 0).toFixed(2) }})
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 右侧购买清单 -->
        <div class="summary-section">
          <div class="summary-header">购买清单</div>
          <div class="summary-content">
            <div class="summary-row">
              <span class="label">账套数:</span>
              <span class="value">{{ formData.account_count }} 套</span>
            </div>
            <div class="summary-row">
              <span class="label">用户数:</span>
              <span class="value">{{ formData.user_count }}</span>
            </div>
            <div class="summary-row">
              <span class="label">使用月份:</span>
              <span class="value">{{ formData.duration_months }}个月</span>
            </div>
            <div class="summary-row">
              <span class="label">服务模块:</span>
              <span class="value service-module">{{ selectedFeaturesText }}</span>
            </div>
            <!-- 可编辑的价格字段 -->
            <div class="summary-row price-row editable">
              <span class="label">产品原价:</span>
              <el-input-number
                v-if="isEditing"
                v-model="formData.standard_price"
                :precision="2"
                :min="0"
                :controls="false"
                size="small"
                style="width: 120px"
                @change="calculateActualPrice"
              />
              <span v-else class="value price">¥{{ (parseFloat(formData.standard_price) || 0).toFixed(2) }}</span>
            </div>

            <div class="summary-row price-row editable">
              <span class="label">优惠折扣:</span>
              <el-input-number
                v-if="isEditing"
                v-model="formData.discount_rate"
                :precision="4"
                :min="0"
                :max="1"
                :step="0.01"
                :controls="false"
                size="small"
                style="width: 120px"
                @change="calculateActualPrice"
              />
              <span v-else class="value discount">{{ discountText }}</span>
            </div>

            <div class="summary-row price-row editable">
              <span class="label">其他优惠:</span>
              <el-input-number
                v-if="isEditing"
                v-model="formData.other_discount"
                :precision="2"
                :min="0"
                :controls="false"
                size="small"
                style="width: 120px"
                @change="calculateActualPrice"
              />
              <span v-else class="value discount">¥{{ (parseFloat(formData.other_discount) || 0).toFixed(2) }}</span>
            </div>

            <div class="summary-row total-row">
              <span class="label">实付价格:</span>
              <span class="value total-price">¥{{ (parseFloat(formData.actual_price) || 0).toFixed(2) }}</span>
            </div>

            <!-- 重新计算按钮 -->
            <div v-if="isEditing" class="summary-actions">
              <el-button
                type="primary"
                size="small"
                @click="recalculateFromBackend"
                :loading="calculating"
              >
                重新计算
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getProducts } from '@/api/product';
import { getProductFeatures } from '@/api/product.js';
import { calculateProductPrice } from '@/api/pricing.js';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);

// 响应式数据
const formRef = ref(null);
const formData = ref({
  product_id: null,
  user_count: 3,
  account_count: 5,
  duration_months: 12,
  selected_features: [],
  standard_price: 0.00,
  discount_rate: 1.0000,
  other_discount: 0.00,
  actual_price: 0.00,
  activity_other: ''
});

const products = ref([]);
const availableFeatures = ref([]);
const calculating = ref(false);

// 计算属性：选中的产品
const selectedProduct = computed(() => {
  return products.value.find(p => p.id === formData.value.product_id);
});

// 计算属性：选中的功能文本
const selectedFeaturesText = computed(() => {
  const selected = availableFeatures.value.filter(f => f.selected);
  return selected.length > 0 ? selected.map(f => f.feature_name).join('、') : '基础';
});

// 计算属性：折扣文本
const discountText = computed(() => {
  const rate = formData.value.discount_rate || 1;
  if (rate === 1) return '无折扣';
  return `${(rate * 100).toFixed(1)}折`;
});

// 计算属性：已选功能
const selectedFeatures = computed(() => {
  return availableFeatures.value.filter(f => f.selected);
});

// 计算属性：未选功能
const unselectedFeatures = computed(() => {
  return availableFeatures.value.filter(f => !f.selected);
});

// 表单验证规则
const rules = {
  product_id: [
    { required: true, message: '请选择产品', trigger: 'change' }
  ],
  user_count: [
    { required: true, message: '请输入使用人数', trigger: 'blur' }
  ],
  account_count: [
    { required: true, message: '请输入账套数', trigger: 'blur' }
  ],
  duration_months: [
    { required: true, message: '请输入购买时长', trigger: 'blur' }
  ]
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    Object.assign(formData.value, newValue);
  }
}, { immediate: true });

// 手动更新父组件
const updateParent = () => {
  console.log('ProductInfo updateParent 被调用，当前数据:', formData.value);
  emit('update:modelValue', { ...formData.value });
};

// 获取产品列表
const fetchProducts = async () => {
  try {
    const response = await getProducts();
    products.value = response || [];
  } catch (error) {
    console.error('获取产品列表失败:', error);
    ElMessage.error('获取产品列表失败');
  }
};

// 获取产品功能列表
const fetchProductFeatures = async (productId) => {
  try {
    // 确保 productId 是有效的
    if (!productId || productId === null || productId === undefined) {
      console.warn('产品ID无效:', productId);
      availableFeatures.value = [];
      return;
    }

    const response = await getProductFeatures(productId);
    availableFeatures.value = (response || []).map(feature => ({
      ...feature,
      selected: false
    }));
  } catch (error) {
    console.error('获取产品功能失败:', error);
    ElMessage.error('获取产品功能失败');
    availableFeatures.value = [];
  }
};

// 处理产品变化
const handleProductChange = async (productId) => {
  if (!productId) {
    availableFeatures.value = [];
    formData.value.selected_features = [];
    return;
  }

  const product = products.value.find(p => p.id === productId);
  if (product) {
    formData.value.user_count = Math.max(formData.value.user_count, product.base_user_count || 1);
    formData.value.account_count = Math.max(formData.value.account_count, product.base_account_count || 1);
    formData.value.standard_price = parseFloat(product.base_price || 0);

    await fetchProductFeatures(productId);
    // 使用后端计算获取准确价格
    await calculatePriceFromBackend();
    updateParent();
  }
};

// 选择月份
const selectMonth = async (month) => {
  if (!props.isEditing) return;
  formData.value.duration_months = month;
  await calculatePriceFromBackend();
  updateParent();
};



// 静默的后端价格计算（用于实时计算，不显示加载状态）
const calculatePriceFromBackend = async () => {
  if (!selectedProduct.value) {
    return;
  }

  try {
    const requestData = {
      product_id: formData.value.product_id,
      user_count: formData.value.user_count,
      account_count: formData.value.account_count,
      selected_features: availableFeatures.value
        .filter(f => f.selected)
        .map(f => f.id),
      duration_months: formData.value.duration_months
    };

    const result = await calculateProductPrice(requestData);

    if (result.success) {
      formData.value.standard_price = parseFloat(result.standard_price.toFixed(2));
      calculateActualPrice();
    }
  } catch (error) {
    console.error('实时价格计算失败:', error);
    // 静默失败，不显示错误消息，避免干扰用户体验
  }
};

// 处理用户数变化 - 实时调用后端计算
const handleUserCountChange = async () => {
  await calculatePriceFromBackend();
  updateParent();
};

// 处理账套数变化 - 实时调用后端计算
const handleAccountCountChange = async () => {
  await calculatePriceFromBackend();
  updateParent();
};

// 处理时长变化 - 实时调用后端计算
const handleDurationChange = async () => {
  await calculatePriceFromBackend();
  updateParent();
};

// 处理功能变化 - 实时调用后端计算
const handleFeatureChangeWithCalculation = async () => {
  await calculatePriceFromBackend();
  updateParent();
};

// 切换功能选择状态
const toggleFeature = async (feature) => {
  if (!props.isEditing || feature.is_required) return;
  feature.selected = !feature.selected;
  await handleFeatureChangeWithCalculation();
};

// 计算价格（前端简化计算，作为备用）
const calculatePrice = () => {
  if (!selectedProduct.value) return;

  let totalPrice = parseFloat(selectedProduct.value.base_price || 0);

  // 计算功能费用
  const selectedFeaturePrice = availableFeatures.value
    .filter(feature => feature.selected)
    .reduce((sum, feature) => sum + parseFloat(feature.price || 0), 0);

  totalPrice += selectedFeaturePrice;

  // 按时长计算
  totalPrice *= (formData.value.duration_months / 12);

  formData.value.standard_price = parseFloat(totalPrice.toFixed(2));
  calculateActualPrice();
};

// 计算实付价格
const calculateActualPrice = () => {
  const standardPrice = parseFloat(formData.value.standard_price || 0);
  const discountRate = parseFloat(formData.value.discount_rate || 1);
  const otherDiscount = parseFloat(formData.value.other_discount || 0);

  const actualPrice = Math.max(0, standardPrice * discountRate - otherDiscount);
  formData.value.actual_price = parseFloat(actualPrice.toFixed(2));
  updateParent();
};

// 从后端重新计算价格
const recalculateFromBackend = async () => {
  if (!selectedProduct.value) {
    ElMessage.warning('请先选择产品');
    return;
  }

  calculating.value = true;
  try {
    // 准备请求数据
    const requestData = {
      product_id: formData.value.product_id,
      user_count: formData.value.user_count,
      account_count: formData.value.account_count,
      selected_features: availableFeatures.value
        .filter(f => f.selected)
        .map(f => f.id),
      duration_months: formData.value.duration_months
    };

    console.log('发送价格计算请求:', requestData);

    // 调用后端定价服务API
    const result = await calculateProductPrice(requestData);
    console.log('价格计算响应:', result);

    if (result.success) {
      formData.value.standard_price = parseFloat(result.standard_price.toFixed(2));
      calculateActualPrice();
      ElMessage.success('价格重新计算完成');
    } else {
      throw new Error(result.message || '计算失败');
    }
  } catch (error) {
    console.error('重新计算价格失败:', error);
    ElMessage.error(`重新计算价格失败: ${error.message}`);
  } finally {
    calculating.value = false;
  }
};

// 表单验证
const validate = async () => {
  try {
    await formRef.value.validate();
    emit('validate', true);
    return true;
  } catch (error) {
    emit('validate', false);
    return false;
  }
};

// 暴露验证方法给父组件
defineExpose({
  validate
});

// 组件挂载时初始化
onMounted(() => {
  fetchProducts();
});
</script>

<style scoped>
.product-info {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-layout {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.config-section {
  flex: 1;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.config-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.config-item {
  flex: 1;
}

.config-item.full-width {
  flex: none;
  width: 100%;
}

.month-selector {
  display: flex;
  gap: 8px;
}

.feature-management {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.feature-price {
  color: #67c23a;
  font-size: 12px;
}

/* Element Plus 标签样式的功能管理 */
.feature-tags-container {
  width: 100%;
}

.feature-section {
  margin-bottom: 16px;
}

.section-label {
  display: inline-block;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  margin-right: 12px;
}

.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.feature-tag-item.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.feature-tag-item.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.feature-tag .feature-name {
  font-weight: 400;
}

.feature-tag .feature-price {
  margin-left: 3px;
  font-size: 11px;
  opacity: 0.7;
}

.summary-section {
  width: 280px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 0;
}

.summary-header {
  background: #409eff;
  color: white;
  padding: 12px 16px;
  font-weight: 500;
  border-radius: 6px 6px 0 0;
}

.summary-content {
  padding: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row .label {
  color: #606266;
  font-size: 14px;
}

.summary-row .value {
  color: #303133;
  font-weight: 500;
}

.summary-row.price-row .value {
  color: #f56c6c;
}

.summary-row.total-row {
  margin-top: 8px;
  padding-top: 12px;
  border-top: 2px solid #409eff;
  border-bottom: none;
}

.summary-row.total-row .label {
  font-weight: 600;
  color: #303133;
}

.summary-row.total-row .value {
  font-size: 16px;
  font-weight: 600;
  color: #67c23a;
}

.summary-row.editable {
  align-items: center;
}

.summary-row.editable .el-input-number {
  margin-left: auto;
}

.summary-actions {
  margin-top: 16px;
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.service-module {
  color: #409eff;
}

.discount {
  color: #e6a23c;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-input-number {
  width: 100%;
}

@media (max-width: 768px) {
  .product-layout {
    flex-direction: column;
  }

  .summary-section {
    width: 100%;
  }

  .config-row {
    flex-direction: column;
    gap: 0;
  }
}
</style>
