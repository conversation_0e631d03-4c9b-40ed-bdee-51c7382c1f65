const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Followup = sequelize.define('Followup', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: '自增主键'
  },
  enterprise_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联enterprise.id'
  },
  followup_time: {
    type: DataTypes.DATE(6),
    allowNull: false,
    comment: '跟进时间'
  },
  situation: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '跟进情况'
  },
  attachment: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '附件路径（多文件用,分隔）'
  },
  employee_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '跟进员工ID'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'followup',
  timestamps: false, // 表中没有createdAt和updatedAt字段
  charset: 'utf8mb4',
  collate: 'utf8mb4_0900_ai_ci'
});

module.exports = Followup; 