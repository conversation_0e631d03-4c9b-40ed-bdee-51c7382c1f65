//前端API服务 (api/auth.js)：让前端代码可以方便地调用后端的登录接口。

import service from '@/utils/request_extra.js';

const API_PATH = '/auth';

/**
 * 员工登录接口
 * @param {object} credentials - 包含 employee_number 和 password 的对象
 * @returns Promise
 */
export const loginEmployee = (credentials) => {
  return service.post(`${API_PATH}/employee/login`, credentials);
};

/**
 * [!] 新增：员工修改自己密码的接口
 * @param {object} passwordData - 包含 oldPassword 和 newPassword 的对象
 * @returns Promise
 */
export const changePassword = (passwordData) => {
  return service.put(`${API_PATH}/change-password`, passwordData);
};

/**
 * [!] 新增：验证当前登录管理员密码的接口
 * @param {string} password - 管理员的当前密码
 * @returns Promise
 */
export const verifyPassword = (password) => {
  return service.post(`${API_PATH}/verify-password`, { password });
};

// 未来可以添加小程序登录等其他认证相关的API调用 