/**
 * 618活动专题页面
 * @file 全屏618促销活动详情页
 */
const app = getApp()
const navService = require('../../utils/navigator.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    promotionInfo: {},
    countdownInfo: {
      days: '30',
      hours: '00',
      minutes: '55',
      seconds: '27'
    },
    // 产品基本信息
    products: [
      {
        id: 1,
        name: '好会计-财务软件',
        label: '爆款',
        features: '记账报税 | 一键账表 | 随时查账',
        basePrice: 898,
        dailyPrice: '',
        discountPrice: '',
        saving: '',
        image: 'https://mshop.bogoo.net/618/product_hkj.png',
        navigateTo: 'navigateToVersionhkj'
      },
      {
        id: 2,
        name: '易代账-代账软件',
        label: '推荐',
        features: '客户管理 | 批量建账 | 批量报税',
        basePrice: 998,
        dailyPrice: '',
        discountPrice: '',
        saving: '',
        image: 'https://mshop.bogoo.net/618/product_ydz.png',
        navigateTo: 'navigateToVersionydz'
      },
      {
        id: 3,
        name: '好业财-业财税一体软件',
        label: '尊享',
        features: '实时报表 | 财务管理 | 合规申报',
        basePrice: 3000,
        dailyPrice: '',
        discountPrice: '',
        saving: '',
        image: 'https://mshop.bogoo.net/618/product_hyc.png',
        navigateTo: 'navigateToVersionhyc'
      },
      {
        id: 4,
        name: '好生意-进销存系统',
        label: '热销',
        features: '销售管理 | 库存管理 | 采购管理',
        basePrice: 1998,
        dailyPrice: '',
        discountPrice: '',
        saving: '',
        image: 'https://mshop.bogoo.net/618/product_hsy.png',
        navigateTo: 'navigateToVersionhsy'
      }
    ],
    // 默认活动结束时间
    endTime: '2025/06/18 23:59:59'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 确保全屏显示，不显示导航栏
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 设置页面标题（但不显示）
    wx.setNavigationBarTitle({
      title: '618活动专题'
    });
    
    // 获取活动信息
    if (app.globalData.promotionInfo) {
      // 处理折扣规则，添加计算好的省百分比
      let promotionInfo = app.globalData.promotionInfo;
      if (promotionInfo.rules && promotionInfo.rules.length > 0) {
        promotionInfo.rules = promotionInfo.rules.map(rule => {
          // 格式化折扣显示，避免浮点数精度问题
          const discountText = parseFloat((rule.discount * 10).toFixed(1));
          return {
            ...rule,
            discountText: discountText, // 新增用于显示的折扣属性
            savePercent: Math.round((1 - rule.discount) * 100) // 计算省下的百分比
          };
        });
      }
      
      this.setData({
        promotionInfo: promotionInfo,
        endTime: promotionInfo.endTime.replace(/-/g, '/')
      });
      
      // 计算产品的折扣价格
      this.calculateProductPrices();
    }
    
    this.startCountdown();
    
    // 页面动画效果
    this.startPageAnimations();
  },
  
  /**
   * 计算产品的折扣价格
   */
  calculateProductPrices: function() {
    const promotionInfo = app.globalData.promotionInfo;
    if (!promotionInfo || !promotionInfo.rules || promotionInfo.rules.length === 0) {
      return;
    }
    
    // 获取最高折扣率(即discount值最小的)
    const minDiscount = Math.min(...promotionInfo.rules.map(rule => rule.discount));
    const discount = minDiscount;
    
    // 计算折扣百分比 (85折 = 15% off)
    const discountPercent = Math.round((1 - discount) * 100);
    
    const products = this.data.products.map(product => {
      // 计算折扣价格 (保留一位小数)
      const discountPrice = (product.basePrice * discount).toFixed(1);
      
      // 计算节省金额 (保留一位小数)
      const saving = (product.basePrice - discountPrice).toFixed(1);
      
      // 计算每天价格 (保留1位小数)
      const dailyPrice = (discountPrice / 365).toFixed(1);
      
      return {
        ...product,
        discountPrice: discountPrice + '元起',
        saving: saving + '元',
        dailyPrice: dailyPrice + '元/天'
      };
    });
    
    this.setData({ 
      products,
      discountPercent: discountPercent // 添加折扣百分比到data中
    });
  },
  
  /**
   * 启动页面的动画效果
   */
  startPageAnimations: function() {
    // 在实际项目中，可以添加页面加载时的过渡动画效果
    // 由于小程序限制，这里仅作为示例
    console.log('页面动画已启动');
  },
  
  /**
   * 开始倒计时
   */
  startCountdown: function () {
    // 获取活动结束时间
    const endTimeStr = this.data.endTime || '2025/06/18 23:59:59';
    
    // 更新倒计时
    this.updateCountdown(endTimeStr);
    
    // 设置定时器，每秒更新倒计时
    this.countdownTimer = setInterval(() => {
      this.updateCountdown(endTimeStr);
    }, 1000);
  },
  
  /**
   * 更新倒计时
   */
  updateCountdown: function (endTimeStr) {
    // 确保endTimeStr格式正确（替换横杠为斜杠）
    let formattedEndTimeStr = endTimeStr;
    if (formattedEndTimeStr.indexOf('-') !== -1) {
      formattedEndTimeStr = formattedEndTimeStr.replace(/-/g, '/');
    }
    
    // 处理可能的ISO格式日期（包含T的情况）
    if (formattedEndTimeStr.indexOf('T') !== -1) {
      formattedEndTimeStr = formattedEndTimeStr.replace('T', ' ');
    }
    
    // 计算倒计时
    const endTime = new Date(formattedEndTimeStr).getTime();
    const now = new Date().getTime();
    const distance = endTime - now;
    
    // 检查日期是否有效
    if (isNaN(endTime) || distance <= 0) {
      // 日期无效或已过期，设置为0
      this.setData({
        countdownInfo: {
          days: '00',
          hours: '00',
          minutes: '00',
          seconds: '00'
        }
      });
      // 清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
      return;
    }
    
    // 计算天、时、分、秒
    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    // 更新UI
    this.setData({
      countdownInfo: {
        days: days < 10 ? '0' + days : String(days),
        hours: hours < 10 ? '0' + hours : String(hours),
        minutes: minutes < 10 ? '0' + minutes : String(minutes),
        seconds: seconds < 10 ? '0' + seconds : String(seconds)
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '财务软件618爆款秒杀！前100名加赠礼盒',
      path: '/pages/index/index',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },
  
  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '财务软件618爆款秒杀！前100名加赠礼盒',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 隐藏导航栏以达到全屏效果
    wx.hideTabBar({
      animation: false
    });
  },
  
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 恢复导航栏
    wx.showTabBar({
      animation: false
    });
  },
  
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },

  // 跳转到商品详情页
  navigateToDetail: function(e) {
    const productId = e.currentTarget.dataset.id;
    const productKey = 
      productId == 1 ? 'hkj' : 
      productId == 2 ? 'ydz' :
      productId == 3 ? 'hyc' : 'hsy';
    
    navService.navigateToProduct(productKey);
  },

  // 跳转到促销购买页面
  navigateToVersionhkj: function(e) {
    navService.navigateToVersionPage('hkj');
  },
  navigateToVersionydz: function(e) {
    navService.navigateToVersionPage('ydz');
  },
  navigateToVersionhyc: function(e) {
    navService.navigateToVersionPage('hyc');
  },
  navigateToVersionhsy: function(e) {
    navService.navigateToVersionPage('hsy');
  },
  
  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  }
}); 