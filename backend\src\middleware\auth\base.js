/**
 * 基础认证中间件
 * 提供通用的JWT验证功能
 */

const jwt = require('jsonwebtoken');
const JWT_SECRET = process.env.JWT_SECRET || 'your_default_jwt_secret_key';

/**
 * 通用JWT验证中间件
 * 只负责验证token的有效性，不做权限判断
 */
const verifyToken = (req, res, next) => {
  // 支持从 Header 和 Query 两种方式获取 Token
  let token;
  const authHeader = req.headers['authorization'];

  if (authHeader && authHeader.startsWith('Bearer ')) {
    // 1. 优先从 Authorization 请求头获取
    token = authHeader.substring(7, authHeader.length);
  } else if (req.query && req.query.token) {
    // 2. 如果请求头没有，则尝试从URL查询参数获取
    token = req.query.token;
  }

  if (!token) {
    return res.status(403).send({
      message: '授权失败：未提供Token',
    });
  }

  // 验证token
  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      console.error('JWT 验证失败:', err.message);
      return res.status(401).send({
        message: '无权限访问：Token无效或已过期',
      });
    }
    
    // 将解码后的用户信息附加到请求对象上（与原authJwt.js完全一致）
    req.user = {
      id: decoded.id,
      role: decoded.role,
      type: decoded.type // 添加用户类型信息
    };
    next();
  });
};

/**
 * 创建用户类型验证中间件的工厂函数
 * @param {string|string[]} allowedTypes - 允许的用户类型
 * @returns {function} 中间件函数
 */
const requireUserType = (allowedTypes) => {
  const types = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未认证用户' });
    }
    
    if (!types.includes(req.user.type)) {
      return res.status(403).json({ 
        message: `访问被拒绝：需要${types.join('或')}权限` 
      });
    }
    
    next();
  };
};

module.exports = {
  verifyToken,
  requireUserType,
  JWT_SECRET
};
