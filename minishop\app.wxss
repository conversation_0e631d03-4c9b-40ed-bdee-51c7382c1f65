@import './static/fonts/iconfont.wxss';
/**
 * 全局样式表
 */
/**app.wxss**/
page {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  border: none;
  --primary-color: #FF2B22;
  --secondary-color: #FFD700;
  --text-color: #333333;
  --bg-color: #F8F8F8;
  --light-color: #FFFFFF;
  
  background-color: var(--bg-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 16px;
  color: var(--text-color);
}

view, scroll-view, swiper {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
}

/* 618促销主题色 */
.theme-618 {
  background: linear-gradient(to right, var(--primary-color), #FF6347);
  color: var(--light-color);
}

.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  /* padding: 20rpx; */
}

.card {
  width: 100%;
  background-color: var(--light-color);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.price {
  color: var(--primary-color);
  font-size: 36rpx;
  font-weight: bold;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.centered {
  justify-content: center;
  align-items: center;
}

.space-between {
  justify-content: space-between;
}

/* 促销标签 */
.promotion-tag {
  background-color: var(--secondary-color);
  color: var(--primary-color);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

/* 倒计时样式 */
.countdown {
  color: var(--primary-color);
  font-size: 26rpx;
  font-weight: bold;
}

/* 分割线 */
.divider {
  width: 100%;
  height: 1rpx;
  background-color: #EEEEEE;
  margin: 20rpx 0;
}

/* 活动礼品样式 */
.gift-item {
  padding: 10rpx 0;
}

/* 重置按钮默认样式 */
button {
  margin: 0;
  padding: 0;
  background: transparent;
  line-height: normal;
  overflow: visible;
}

button::after {
  border: none;
  outline: none;
}

/* 删除底部按钮容器样式 */