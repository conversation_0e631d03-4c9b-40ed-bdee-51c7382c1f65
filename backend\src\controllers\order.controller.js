const { OrderAttachment, Employee } = require('../models');
const { generateOrderId } = require('../utils/id_helper');
const orderService = require('../services/order.service');
const path = require('path');

/**
 * 获取下一个可用的订单ID
 * 支持产品订单(PO)和服务订单(SO)
 */
exports.getNextOrderId = async (req, res) => {
  try {
    const { orderType } = req.query; // 'product' 或 'service'
    const prefix = orderType === 'service' ? 'SO' : 'PO';
    const nextId = await generateOrderId(prefix);
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个订单ID时出错:', error);
    res.status(500).json({ message: '生成订单ID失败', error: error.message });
  }
};

/**
 * 检查订单号是否已存在
 */
exports.checkOrderIdExists = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { OrderHead } = require('../models');

    const existingOrder = await OrderHead.findOne({
      where: { order_id: orderId },
      attributes: ['id', 'order_id']
    });

    res.status(200).json({
      exists: !!existingOrder,
      order_id: orderId
    });
  } catch (error) {
    console.error('检查订单号时出错:', error);
    res.status(500).json({ message: '检查订单号失败', error: error.message });
  }
};

/**
 * 获取订单审核列表 - 显示所有待审核的订单
 */
exports.getOrdersForReview = async (req, res) => {
    try {
        const orders = await orderService.getOrdersForReview();
        res.status(200).json(orders);
    } catch (error) {
        res.status(500).json({ message: "获取订单审核列表失败", error: error.message });
    }
};

/**
 * 获取合伙人订单列表 - 用于合伙人小程序
 */
exports.getPartnerOrders = async (req, res) => {
    try {
        const userId = req.user.id;
        const { page = 1, pageSize = 100 } = req.query;

        const result = await orderService.getPartnerOrders(userId, { page, pageSize });

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('获取合伙人订单失败:', error);

        if (error.message.includes('不是合伙人')) {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '获取订单列表失败'
        });
    }
};

/**
 * 获取产品订单列表 - 显示已审核通过的产品订单
 */
exports.getProductOrders = async (req, res) => {
    try {
        const orders = await orderService.getProductOrders();
        res.status(200).json(orders);
    } catch (error) {
        res.status(500).json({ message: "获取产品订单列表失败", error: error.message });
    }
};

/**
 * 获取服务订单列表 - 显示已审核通过的服务订单
 */
exports.getServiceOrders = async (req, res) => {
    try {
        const orders = await orderService.getServiceOrders();
        res.status(200).json(orders);
    } catch (error) {
        res.status(500).json({ message: "获取服务订单列表失败", error: error.message });
    }
};

/**
 * 根据ID获取单个订单详情
 */
exports.getOrderById = async (req, res) => {
    try {
        const order = await orderService.getOrderById(req.params.id);
        res.status(200).json(order);
    } catch (error) {
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "获取订单详情失败", error: error.message });
    }
};

/**
 * 创建产品订单
 */
exports.createProductOrder = async (req, res) => {
    try {
        const orderData = req.body;
        const creatorId = req.user?.id;

        const newOrder = await orderService.createProductOrder(orderData, creatorId);
        res.status(201).json(newOrder);
    } catch (error) {
        console.error('创建产品订单失败:', error);
        if (error.message.includes('已存在')) {
            return res.status(409).json({ message: error.message });
        }
        res.status(500).json({ message: "创建产品订单失败", error: error.message });
    }
};

/**
 * 创建服务订单
 */
exports.createServiceOrder = async (req, res) => {
    try {
        const orderData = req.body;
        const creatorId = req.user?.id;

        const newOrder = await orderService.createServiceOrder(orderData, creatorId);
        res.status(201).json(newOrder);
    } catch (error) {
        console.error('创建服务订单失败:', error);
        if (error.message.includes('已存在')) {
            return res.status(409).json({ message: error.message });
        }
        res.status(500).json({ message: "创建服务订单失败", error: error.message });
    }
};

/**
 * 更新订单
 */
exports.updateOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const orderData = req.body;

        const updatedOrder = await orderService.updateOrder(id, orderData);
        res.status(200).json(updatedOrder);
    } catch (error) {
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "更新订单失败", error: error.message });
    }
};

/**
 * 审核订单 - 通过审核
 */
exports.approveOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedOrder = await orderService.approveOrder(id);
        res.status(200).json(updatedOrder);
    } catch (error) {
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "审核订单失败", error: error.message });
    }
};

/**
 * 审核订单 - 拒绝审核
 */
exports.rejectOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const { reason } = req.body;
        const updatedOrder = await orderService.rejectOrder(id, reason);
        res.status(200).json(updatedOrder);
    } catch (error) {
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "拒绝订单失败", error: error.message });
    }
};

/**
 * 删除订单
 */
exports.deleteOrder = async (req, res) => {
    try {
        const { id } = req.params;
        await orderService.deleteOrder(id);
        res.status(200).json({ message: "订单删除成功" });
    } catch (error) {
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "删除订单失败", error: error.message });
    }
};

// === 订单附件相关方法 ===

/**
 * 上传订单附件
 */
exports.addAttachment = async (req, res) => {
    try {
        const { orderId } = req.params;
        const file = req.file;

        if (!file) {
            return res.status(400).json({ message: "请选择要上传的文件" });
        }

        const attachmentData = {
            order_id: orderId,
            filename: req.attachment_filename || file.originalname,  // 使用中间件解码的文件名
            file_path: 'uploads/' + file.filename,  // 和企业营业执照完全一样的存储方式
            file_size: file.size,
            file_type: req.body.file_type || '其他',
            remark: req.body.remark || '',
            uploader_id: req.user?.id || req.employee?.id  // 修复：支持两种认证方式
        };

        const attachment = await OrderAttachment.create(attachmentData);
        res.status(201).json(attachment);
    } catch (error) {
        console.error('上传附件失败:', error);
        res.status(500).json({ message: "上传附件失败", error: error.message });
    }
};

/**
 * 获取订单附件列表
 */
exports.getAttachments = async (req, res) => {
    try {
        const { orderId } = req.params;

        const attachments = await OrderAttachment.findAll({
            where: { order_id: orderId },
            include: [
                {
                    model: Employee,
                    as: 'uploader',
                    attributes: ['id', 'name']
                }
            ],
            order: [['uploaded_at', 'DESC']]
        });

        res.status(200).json(attachments);
    } catch (error) {
        console.error('获取附件列表失败:', error);
        res.status(500).json({ message: "获取附件列表失败", error: error.message });
    }
};

/**
 * 删除订单附件
 */
exports.deleteAttachment = async (req, res) => {
    try {
        const { orderId, attachmentId } = req.params;

        const attachment = await OrderAttachment.findOne({
            where: {
                id: attachmentId,
                order_id: orderId
            }
        });

        if (!attachment) {
            return res.status(404).json({ message: "附件未找到" });
        }

        // 删除文件系统中的文件
        const fs = require('fs');
        if (fs.existsSync(attachment.file_path)) {
            fs.unlinkSync(attachment.file_path);
        }

        // 删除数据库记录
        await attachment.destroy();

        res.status(200).json({ message: "附件删除成功" });
    } catch (error) {
        console.error('删除附件失败:', error);
        res.status(500).json({ message: "删除附件失败", error: error.message });
    }
};

/**
 * 下载订单附件
 */
exports.downloadAttachment = async (req, res) => {
    try {
        const { orderId, attachmentId } = req.params;

        const attachment = await OrderAttachment.findOne({
            where: {
                id: attachmentId,
                order_id: orderId
            }
        });

        if (!attachment || !attachment.file_path) {
            return res.status(404).send({ message: "附件未找到" });
        }

        const path = require('path');

        // 采用和企业营业执照、跟进记录一样的简单方式
        const filename = path.basename(attachment.file_path);
        const uploadsDir = path.join(__dirname, '..', '..', 'uploads');
        const filePath = path.join(uploadsDir, filename);

        // 使用res.download()来发送文件，它会自动设置合适的响应头
        res.download(filePath, attachment.filename, (err) => {
            if (err) {
                console.error('下载文件时出错:', err);
                if (!res.headersSent) {
                    res.status(500).send({ message: '下载文件失败。' });
                }
            }
        });

    } catch (error) {
        console.error('处理下载请求时出错:', error);
        if (!res.headersSent) {
            res.status(500).send({ message: '服务器内部错误。' });
        }
    }
};

/**
 * 预览订单附件
 */
exports.previewAttachment = async (req, res) => {
    try {
        const { orderId, attachmentId } = req.params;

        const attachment = await OrderAttachment.findOne({
            where: {
                id: attachmentId,
                order_id: orderId
            }
        });

        if (!attachment || !attachment.file_path) {
            return res.status(404).send({ message: "附件未找到" });
        }

        const path = require('path');
        const mime = require('mime-types');

        // 采用和企业营业执照、跟进记录一样的简单方式
        const filename = path.basename(attachment.file_path);
        const uploadsDir = path.join(__dirname, '..', '..', 'uploads');
        const filePath = path.join(uploadsDir, filename);

        const fs = require('fs');
        if (!fs.existsSync(filePath)) {
            console.error('文件不存在:', filePath);
            return res.status(404).send({ message: "文件不存在" });
        }

        // 获取文件的MIME类型
        const mimeType = mime.lookup(attachment.filename) || 'application/octet-stream';

        // 设置响应头
        res.setHeader('Content-Type', mimeType);
        res.setHeader('Content-Disposition', 'inline');

        // 使用流式传输文件
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);
    } catch (error) {
        console.error('预览附件失败:', error);
        if (!res.headersSent) {
            res.status(500).send({ message: '服务器内部错误。' });
        }
    }
};