<template>
  <div class="order-review-list">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h2>订单审核</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-form :model="filterForm" inline>
        <el-form-item label="订单类型">
          <el-select v-model="filterForm.orderCategory" placeholder="全部" clearable style="width: 120px">
            <el-option label="产品订单" value="产品订单" />
            <el-option label="服务订单" value="服务订单" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建方式">
          <el-select v-model="filterForm.creationMethod" placeholder="全部" clearable style="width: 120px">
            <el-option label="手工创建" value="手工创建" />
            <el-option label="用户创建" value="用户创建" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select v-model="filterForm.paymentStatus" placeholder="全部" clearable style="width: 120px">
            <el-option label="待支付" value="待支付" />
            <el-option label="已支付" value="已支付" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterForm.orderId" placeholder="输入订单号搜索" style="width: 200px" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">搜索</el-button>
          <el-button @click="handleResetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 订单列表表格 -->
    <div class="table-container">
      <el-table 
        :data="filteredOrders" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <!-- 订单号 -->
        <el-table-column prop="order_id" label="订单号" width="140" fixed="left">
          <template #default="{ row }">
            <div class="order-id-cell">
              <el-icon v-if="row.order_category === '产品订单'" class="order-icon product-icon">
                <Box />
              </el-icon>
              <el-icon v-else class="order-icon service-icon">
                <Tools />
              </el-icon>
              <el-link type="primary" @click="handleViewOrder(row)">
                {{ row.order_id }}
              </el-link>
            </div>
          </template>
        </el-table-column>

        <!-- 订单类型 -->
        <el-table-column prop="order_category" label="订单类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.order_category === '产品订单' ? 'primary' : 'success'">
              {{ row.order_category }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 企业信息 -->
        <el-table-column label="企业信息" width="200">
          <template #default="{ row }">
            <div v-if="row.enterprise">
              <div class="enterprise-name">{{ row.enterprise.name }}</div>
              <div class="enterprise-id">ID: {{ row.enterprise.enterprise_id || 'N/A' }}</div>
            </div>
            <span v-else class="text-muted">未关联企业</span>
          </template>
        </el-table-column>

        <!-- 用户信息 -->
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div v-if="row.user">
              <div>{{ row.user.name }}</div>
              <div class="text-muted">{{ row.user.mobile }}</div>
            </div>
            <span v-else class="text-muted">未关联用户</span>
          </template>
        </el-table-column>

        <!-- 资产信息 -->
        <el-table-column label="资产信息" width="120">
          <template #default="{ row }">
            <span v-if="row.asset">{{ row.asset.asset_id }}</span>
            <span v-else class="text-muted">未关联资产</span>
          </template>
        </el-table-column>

        <!-- 创建方式 -->
        <el-table-column prop="creation_method" label="创建方式" width="100" />

        <!-- 订单类型 -->
        <el-table-column prop="order_type" label="订单类型" width="100" />

        <!-- 订单金额 -->
        <el-table-column label="订单金额" width="120">
          <template #default="{ row }">
            <div>标准: ¥{{ row.standard_amount }}</div>
            <div class="actual-amount">实付: ¥{{ row.actual_amount }}</div>
          </template>
        </el-table-column>

        <!-- 支付状态 -->
        <el-table-column label="支付状态" width="100">
          <template #default="{ row }">
            <el-tag 
              v-if="row.order_category === '产品订单'"
              :type="row.payment_status === '已支付' ? 'success' : 'warning'"
            >
              {{ row.payment_status }}
            </el-tag>
            <span v-else class="text-muted">N/A</span>
          </template>
        </el-table-column>

        <!-- 制单人 -->
        <el-table-column label="制单人" width="100">
          <template #default="{ row }">
            {{ row.creator?.name || 'N/A' }}
          </template>
        </el-table-column>

        <!-- 制单时间 -->
        <el-table-column label="制单时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              @click="handleAudit(row)"
              :loading="auditingOrderId === row.id"
            >
              审核
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedOrders.length > 0" class="batch-actions">
      <el-alert 
        :title="`已选择 ${selectedOrders.length} 个订单`" 
        type="info" 
        show-icon 
        :closable="false"
      >
        <template #default>
          <el-button type="success" size="small" @click="handleBatchApprove">
            批量审核通过
          </el-button>
          <el-button type="danger" size="small" @click="handleBatchReject">
            批量拒绝
          </el-button>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, Box, Tools } from '@element-plus/icons-vue';
import { getOrdersForReview, approveOrder, rejectOrder, deleteOrder } from '@/api/order';
import { formatDateTime } from '@/utils/format';

// Composables
import { useOrderAudit } from '../composables/useOrderAudit';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const orders = ref([]);
const selectedOrders = ref([]);
const auditingOrderId = ref(null);

// 使用审核 composable
const { auditOrder } = useOrderAudit();

// 筛选表单
const filterForm = ref({
  orderCategory: '',
  creationMethod: '',
  paymentStatus: '',
  orderId: ''
});

// 计算属性：过滤后的订单列表
const filteredOrders = computed(() => {
  let filtered = orders.value;
  
  if (filterForm.value.orderCategory) {
    filtered = filtered.filter(order => order.order_category === filterForm.value.orderCategory);
  }
  
  if (filterForm.value.creationMethod) {
    filtered = filtered.filter(order => order.creation_method === filterForm.value.creationMethod);
  }
  
  if (filterForm.value.paymentStatus) {
    filtered = filtered.filter(order => order.payment_status === filterForm.value.paymentStatus);
  }
  
  if (filterForm.value.orderId) {
    filtered = filtered.filter(order => 
      order.order_id.toLowerCase().includes(filterForm.value.orderId.toLowerCase())
    );
  }
  
  return filtered;
});

// 获取订单审核列表
const fetchOrders = async () => {
  loading.value = true;
  try {
    const response = await getOrdersForReview();
    orders.value = response || [];
  } catch (error) {
    console.error('获取订单审核列表失败:', error);
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 事件处理函数
const handleRefresh = () => {
  fetchOrders();
};

const handleFilter = () => {
  // 筛选逻辑已通过计算属性实现
};

const handleResetFilter = () => {
  filterForm.value = {
    orderCategory: '',
    creationMethod: '',
    paymentStatus: '',
    orderId: ''
  };
};

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection;
};

// 查看订单详情
const handleViewOrder = (order) => {
  console.log('点击查看订单:', order.order_id, '订单类型:', order.order_category);

  if (order.order_category === '产品订单') {
    // 跳转到产品订单查看页面
    router.push({
      name: 'ProductOrderForm',
      params: { id: order.id },
      query: { mode: 'view' }
    });
  } else if (order.order_category === '服务订单') {
    // 跳转到服务订单查看页面
    router.push({
      name: 'ServiceOrderForm',
      params: { id: order.id },
      query: { mode: 'view' }
    });
  } else {
    ElMessage.warning('未知的订单类型');
  }
};

// 直接在列表中审核订单
const handleAudit = async (order) => {
  auditingOrderId.value = order.id;
  try {
    await auditOrder(order.id, async () => {
      // 审核成功后刷新列表
      await fetchOrders();
    });
  } finally {
    auditingOrderId.value = null;
  }
};

// 编辑订单
const handleEdit = (order) => {
  const routeName = order.order_category === '产品订单' ? 'ProductOrderForm' : 'ServiceOrderForm';
  router.push({
    name: routeName,
    params: { id: order.id },
    query: { mode: 'edit' }
  });
};

// 删除订单
const handleDelete = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单 "${order.order_id}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await deleteOrder(order.id);
    ElMessage.success('删除成功');
    await fetchOrders();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 批量审核通过
const handleBatchApprove = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量审核通过选中的 ${selectedOrders.value.length} 个订单吗？`,
      '确认批量审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    const promises = selectedOrders.value.map(order => approveOrder(order.id));
    await Promise.all(promises);
    
    ElMessage.success('批量审核成功');
    await fetchOrders();
    selectedOrders.value = [];
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error);
      ElMessage.error('批量审核失败');
    }
  }
};

// 批量拒绝
const handleBatchReject = async () => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因',
      '批量拒绝审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '拒绝原因不能为空'
      }
    );
    
    const promises = selectedOrders.value.map(order => rejectOrder(order.id, reason));
    await Promise.all(promises);
    
    ElMessage.success('批量拒绝成功');
    await fetchOrders();
    selectedOrders.value = [];
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error);
      ElMessage.error('批量拒绝失败');
    }
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
.order-review-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filter-bar {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.order-id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-icon {
  font-size: 16px;
}

.product-icon {
  color: #409eff;
}

.service-icon {
  color: #67c23a;
}

.enterprise-name {
  font-weight: 500;
  color: #303133;
}

.enterprise-id {
  font-size: 12px;
  color: #909399;
}

.actual-amount {
  font-weight: 500;
  color: #67c23a;
}

.text-muted {
  color: #909399;
}

.batch-actions {
  margin-top: 16px;
}

.batch-actions .el-alert {
  border-radius: 4px;
}

.batch-actions .el-button {
  margin-left: 12px;
}
</style>
