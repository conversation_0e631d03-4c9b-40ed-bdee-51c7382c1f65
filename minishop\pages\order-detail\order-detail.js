// pages/order-detail/order-detail.js
const api = require('../../utils/api');

Page({
  data: {
    orderId: null,
    order: null,
    loading: true,
    error: null
  },

  onLoad(options) {
    console.log('订单详情页加载，参数:', options);
    
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    } else {
      this.showError('缺少订单ID参数');
    }
  },

  onShow() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '订单详情'
    });
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    try {
      this.setData({ loading: true, error: null });
      
      console.log('开始加载订单详情，ID:', this.data.orderId);
      
      const response = await api.get(`/orders/${this.data.orderId}`);
      console.log('订单详情响应:', response);

      const orderData = response.data || response;

      if (orderData && orderData.id) {
        this.setData({
          order: orderData,
          loading: false
        });

        // 更新页面标题
        if (orderData.order_number) {
          wx.setNavigationBarTitle({
            title: `订单 ${orderData.order_number}`
          });
        }
      } else {
        this.showError('订单数据格式错误');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.showError('加载订单详情失败');
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({
      loading: false,
      error: message
    });
    wx.showToast({
      title: message,
      icon: 'none'
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 刷新页面
   */
  onPullDownRefresh() {
    this.loadOrderDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showToast({
      title: '请使用右下角咨询按钮',
      icon: 'none'
    });
  },

  /**
   * 支付订单
   */
  payOrder() {
    wx.showToast({
      title: '支付功能开发中',
      icon: 'none'
    });
  },

  /**
   * 取消订单
   */
  cancelOrder() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '取消订单功能开发中',
            icon: 'none'
          });
        }
      }
    });
  }
});
