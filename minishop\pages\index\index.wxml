<view class="activity-container">

  <!-- 顶部图片区域 - 替换为整张图片 -->
  <view class="banner-image-container">
    <image class="banner-full-image" src="https://mshop.bogoo.net/618/banner_full.png" mode="widthFix"></image>
  </view>
  
  <!-- 服务展示区  -->
  <view class="service-buttons-container">
    <view class="service-btn">
      <text class="iconfont icon-zhengpinbaozheng1"></text>
      <text>用友正品</text>
      <view class="service-badge">官方</view>
    </view>
    <view class="service-btn">
      <text class="iconfont icon-kejipeixun"></text>
      <text>专业培训</text>
      <view class="service-badge">免费</view>
    </view>
    <view class="service-btn">
      <text class="iconfont icon-shouhouguanli"></text>
      <text>品质售后</text>
      <view class="service-badge">7×24</view>
    </view>
    <view class="promotion-decor">618</view>
  </view>

    <!-- 折扣优惠区域 -->
    <view class="discount-info-section">
    <view class="discount-header">
      <text class="discount-title">多款产品 · 多买多省</text>
      <view class="premium-tag">年付特惠</view>
    </view>

    <view class="discount-note">好会计 · 易代账 · 好业财 · 好生意</view>

    <view class="discount-cards">
      <view class="discount-card" wx:for="{{promotionInfo.rules}}" wx:key="period">
        <view class="discount-period">{{item.period}}年</view>
        <view class="discount-value">
          <text class="discount-number">{{item.discountText}}</text>
          <text class="discount-unit">折</text>
        </view>
        <view class="discount-save">省{{item.savePercent}}%</view>
        <view wx:if="{{item.period === 3}}" class="best-value-tag">超值</view>
      </view>
    </view>
    
    
  </view>
  
  <!-- 活动礼品区域 -->
  <!-- <view class="gifts-section">
    <view class="section-title">更有活动礼品</view> -->
    
    <!-- 顶部大卡片 - 调整尺寸 -->
    <!-- <view class="gift-large-card">
      <view class="gift-condition-tag">实付满<text class="highlight">{{promotionInfo.gifts[1].threshold}}元</text></view>
      <view class="large-card-content">
        <view class="gift-image-container">
          <view class="send-tag">送</view>
          <image class="gift-image-large" src="https://mshop.bogoo.net/618/gift_2.png" mode="aspectFit"></image>
        </view>
        <view class="gift-info">
          <text class="gift-text-large">{{promotionInfo.gifts[1].name}}</text>
          <view class="gift-tag">三选一</view>
        </view>
      </view>
    
    </view> -->
    
    <!-- 底部两个并排卡片 -->
    <!-- <view class="gift-row-cards"> -->
      <!-- 左侧卡片 - 1000元礼品 -->
      <!-- <view class="gift-small-card">
        <view class="gift-condition-tag">实付满<text class="highlight">{{promotionInfo.gifts[0].threshold}}元</text></view>
        <view class="gift-image-container"> -->
          <!-- 调整送标签位置 -->
          <!-- <view class="send-tag-small">送</view>
          <image class="gift-image-medium" src="https://mshop.bogoo.net/618/gift_1.png" mode="aspectFit"></image>
        </view>
        <view class="gift-info">
          <text class="gift-text-medium">{{promotionInfo.gifts[0].name}}</text>
          <view class="gift-note">三选一</view>
        </view>
      </view> -->
      
      <!-- 右侧卡片 - 4000元礼品 -->
      <!-- <view class="gift-small-card">
        <view class="gift-condition-tag">实付满<text class="highlight">{{promotionInfo.gifts[2].threshold}}元</text></view>
        <view class="gift-image-container"> -->
          <!-- 调整送标签位置 -->
          <!-- <view class="send-tag-small">送</view>
          <image class="gift-image-medium" src="https://mshop.bogoo.net/618/gift_3.png" mode="aspectFit"></image>
        </view>
        <view class="gift-info">
          <text class="gift-text-medium">{{promotionInfo.gifts[2].name}}</text>
          <view class="gift-note">三选一</view>
        </view>
      </view>
    </view>
  </view> -->
  
  <!-- 倒计时区域 -->
  <view class="countdown-section">
    <view class="countdown-header">
      <text class="countdown-title">限时抢购</text>
      <text class="countdown-desc">先到先得 · 限量优惠</text>
    </view>
    <view class="countdown-timer">
      <text class="countdown-label">距离结束还剩：</text>
      <view class="countdown-blocks">
        <view class="countdown-block">
          <text class="countdown-number">{{countdownInfo.days || '30'}}</text>
          <text class="countdown-unit">天</text>
        </view>
        <text class="countdown-separator">:</text>
        <view class="countdown-block">
          <text class="countdown-number">{{countdownInfo.hours || '00'}}</text>
          <text class="countdown-unit">时</text>
        </view>
        <text class="countdown-separator">:</text>
        <view class="countdown-block">
          <text class="countdown-number">{{countdownInfo.minutes || '55'}}</text>
          <text class="countdown-unit">分</text>
        </view>
        <text class="countdown-separator">:</text>
        <view class="countdown-block">
          <text class="countdown-number">{{countdownInfo.seconds || '27'}}</text>
          <text class="countdown-unit">秒</text>
        </view>
      </view>
    </view>
  </view>
  

  
  <!-- 爆款推荐标题 -->
  <view class="section-header">
    <text class="section-header-title">爆款推荐</text>
    <view class="header-tag">618抢先购</view>
  </view>
  
  <!-- 爆款推荐区域 -->
  <view class="hot-products-section">
    <!-- 产品卡片 - 动态循环渲染 -->
    <view class="product-card" wx:for="{{products}}" wx:key="id">
      <view class="product-header">
        <text>{{item.name}}</text>
        <view class="hot-label">{{item.label}}</view>
      </view>
      <view class="product-content">
        <view class="product-info">
          <view class="product-name">{{item.features}}</view>
          <view class="price-container">
            <view class="discount-tag">直降{{discountPercent}}%</view>
           
            
            <view class="discount-price">
              <text>618抢先价</text>
              <text class="price highlight">{{item.discountPrice}}</text>
            </view>
            
            <view class="original-price">
              <text>日均价</text>
              <text class="price">¥{{item.dailyPrice}}</text>
            </view>
          </view>
          <!-- 根据navigateTo属性决定使用哪种按钮 -->
          <view wx:if="{{item.navigateTo !== 'contact'}}" class="buy-button" bindtap="{{item.navigateTo}}">优惠详情</view>
          <button wx:else class="buy-button" open-type="contact">优惠详情</button>
          
          <view class="detail-button" bindtap="navigateToDetail" data-id="{{item.id}}">产品详情</view>
        </view>
        <view class="product-image">
          <image src="{{item.image}}" mode="aspectFit"></image>
          <view class="discount-circle">
            <text>省</text>
            <text>{{item.saving}}</text>
          </view>
        </view>

      </view>

    </view>

  </view>
  
  <!-- 底部联系区域 -->
  <view class="contact-section">
    <button class="contact-btn" open-type="contact">联系客服咨询优惠</button>
    <view class="copyright">© 2025 贝克信息 版权所有</view>
  </view>

  <!-- 添加底部导航栏 -->
  <bottom-nav currentTab="activity" theme="red" bindbottomnav="onBottomNavEvent"></bottom-nav>
</view> 