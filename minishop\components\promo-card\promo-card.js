Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 初始是否显示卡片
    initialShow: {
      type: Boolean,
      value: true
    },
    // 页面唯一标识
    pageKey: {
      type: String,
      value: ''
    },
    // 卡片主题色
    themeColor: {
      type: String,
      value: 'purple' // 可选值：purple, blue, red, green
    },
    // 自定义促销标题
    customTitle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 展示状态：normal-正常显示
    displayMode: 'normal',
    // 是否已加载完成
    isLoaded: false,
    // 促销信息(从app.globalData获取)
    promoInfo: null,
    // 用于渲染的动态数据
    renderData: null,
    // 主题色样式
    themeStyle: '',
    // 导航状态标记
    isNavigating: false
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      const app = getApp();
      const promoInfo = app.globalData.promotionInfo;

      // 定义各产品的标准价格
      const productPrices = {
        'hkj': 898,
        'ydz': 998,
        'hsy': 1298,
        'hyc': 3000,
        'tonline': 3400,
        'tzsypjb': 3400
      };

      // 找到最大的折扣（即discount值最小的规则）
      let bestDiscountRule = { discount: 1 }; // 默认为无折扣
      if (promoInfo && promoInfo.rules && promoInfo.rules.length > 0) {
        bestDiscountRule = [...promoInfo.rules].sort((a, b) => a.discount - b.discount)[0];
      }

      // 根据pageKey获取当前产品的标准价格
      const pageKey = this.properties.pageKey;
      const originalPrice = productPrices[pageKey] || 0;

      // 计算活动价格
      const currentPrice = Math.round(originalPrice * bestDiscountRule.discount);
      
      // 设置主题色样式
      this.setThemeStyle();
      
      this.setData({
        promoInfo: promoInfo,
        displayMode: 'normal',
        renderData: {
          originalPrice: originalPrice,
          currentPrice: currentPrice,
          discountText: (bestDiscountRule.discount * 10).toFixed(1)
        }
      });
      
      // 延迟一帧设置isLoaded，以确保CSS过渡生效
      setTimeout(() => {
        this.setData({
          isLoaded: true
        });
      }, 50);
    },
    
    detached: function() {
      // 不需要清理
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'themeColor': function(newVal) {
      this.setThemeStyle();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 设置主题色样式
     */
    setThemeStyle: function() {
      let style = '';
      
      switch(this.properties.themeColor) {
        case 'blue':
          style = 'linear-gradient(135deg, #0ea5e9, #3b82f6, #1d4ed8)';
          break;
        case 'red':
          style = 'linear-gradient(135deg, #f43f5e, #e11d48, #be123c)';
          break;
        case 'green':
          style = 'linear-gradient(135deg, #10b981, #059669, #047857)';
          break;
        case 'purple':
        default:
          style = 'linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899)';
          break;
      }
      
      this.setData({
        themeStyle: style
      });
    },
    
    /**
     * 处理点击立即参与
     */
    handleJoinPromo: function() {
      // 防止重复点击导致多次触发
      if (this.data.isNavigating) return;
      this.setData({ isNavigating: true });

      // 从页面pageKey属性映射到产品productKey
      const pageKey = this.properties.pageKey;
      const productKeyMap = {
        'pdtonline': 'tonline',
        'pdhkj': 'hkj',
        'pdydz': 'ydz',
        'pdhyc': 'hyc',
        'pdhsy': 'hsy',
        'pdtzsypjb': 'tzsypjb'
      };
      const productKey = productKeyMap[pageKey] || pageKey;

      // 向上触发事件，将产品Key传递给父页面
      console.log(`[promo-card] 触发 joinpromo 事件, productKey: ${productKey}`);
      this.triggerEvent('joinpromo', { productKey: productKey });

      // 短暂延时后重置状态，允许再次点击
      setTimeout(() => {
        this.setData({ isNavigating: false });
      }, 300);
    }
  }
}) 