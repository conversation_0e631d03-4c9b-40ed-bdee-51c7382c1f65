/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  display: flex;
  background: white;
  z-index: 90;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 0 10rpx;
}

.nav-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748B; /* 默认灰色文字 */
  position: relative;
  transition: all 0.3s ease;
  padding: 12rpx 0;
}

/* 下划线 - 默认隐藏 */
.nav-tab::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  width: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #2563EB, #3B82F6); /* 渐变蓝色 */
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(-50%);
  border-radius: 2rpx;
}

/* 激活状态下的下划线 */
.nav-tab.active::after {
  width: 40rpx;
  opacity: 1;
}

.nav-tab .nav-text {
  font-size: 24rpx;
  transition: all 0.3s ease;
}

/* 图标样式 - 默认灰色 */
.nav-tab .iconfont {
  font-size: 46rpx;
  margin-bottom: 6rpx;
  transition: all 0.3s ease;
  color: #94A3B8; /* 默认灰色图标 */
}

/* 激活状态 - 文字和图标都变为渐变蓝色 */
.nav-tab.active .iconfont,
.nav-tab.active .nav-text {
  background-image: linear-gradient(135deg, #2563EB, #3B82F6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 激活状态文字加粗 */
.nav-tab.active .nav-text {
  font-weight: 500;
}

/* 点击效果 */
.nav-tab:active {
  opacity: 0.8;
}

/* 红色主题下划线 */
.nav-tab.red-theme::after {
  background: linear-gradient(90deg, #e11d48, #f43f5e); /* 渐变红色 */
}

/* 红色主题激活状态 - 文字和图标都变为渐变红色 */
.nav-tab.active.red-theme .iconfont,
.nav-tab.active.red-theme .nav-text {
  background-image: linear-gradient(135deg, #e11d48, #f43f5e); /* 渐变红色 */
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}