{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "618%E4%BF%83%E9%94%80%E6%B4%BB%E5%8A%A8", "setting": {"compileHotReLoad": true, "bigPackageSizeSupport": true, "urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "useIsolateContext": true}, "libVersion": "3.8.7", "condition": {"miniprogram": {"list": [{"name": "pages/login/login", "pathName": "pages/login/login", "query": "", "scene": null, "launchMode": "default"}, {"name": "pages/versionhsy/versionhsy", "pathName": "pages/versionhsy/versionhsy", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/versionhkj/versionhkj", "pathName": "pages/versionhkj/versionhkj", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/versionydz/versionydz", "pathName": "pages/versionydz/versionydz", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/assets/assets", "pathName": "pages/assets/assets", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/me/me", "pathName": "pages/me/me", "query": "", "launchMode": "default", "scene": null}]}}}