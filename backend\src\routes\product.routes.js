// 引入 Express 的路由功能
const express = require('express');
const router = express.Router();
const { employee } = require('../middleware/auth');

// 引入我们刚刚创建的产品控制器
const productController = require('../controllers/product.controller');

// --- 定义产品相关的路由 ---

// GET /api/products - 获取所有产品列表
// 将请求代理到 productController 的 getAllProducts 函数
router.get('/', employee.verifyEmployee, productController.getAllProducts);

// [新增] 获取下一个可用ID
router.get('/next-id', employee.verifyAdmin, productController.getNextProductId);

// GET /api/products/:id - 获取单个产品的详细信息
// :id 是一个URL参数，例如 /api/products/HKJ01
router.get('/:id', employee.verifyEmployee, productController.getProductById);

// POST /api/products - 创建一个新产品
// 请求的数据在请求体(req.body)中
router.post('/', employee.verifyAdmin, productController.createProduct);

// PUT /api/products/:id - 更新一个现有产品
// 请求的数据在请求体(req.body)中
router.put('/:id', employee.verifyAdmin, productController.updateProduct);

// DELETE /api/products/:id - 删除一个产品
router.delete('/:id', employee.verifyAdmin, productController.deleteProduct);

// --- 新增：产品与功能关联的路由 ---

// [新增] GET /api/products/:id/features - 获取一个产品的所有可选功能列表
router.get('/:id/features', employee.verifyEmployee, productController.getProductFeatures);

// POST /api/products/:productId/features - 为产品添加一个功能
router.post('/:productId/features', employee.verifyAdmin, productController.addProductFeature);

// PUT /api/products/:productId/features/:featureId - 更新产品和功能的关联信息（如价格）
router.put('/:productId/features/:featureId', employee.verifyAdmin, productController.updateProductFeature);

// DELETE /api/products/:productId/features/:featureId - 从产品中移除一个功能
router.delete('/:productId/features/:featureId', employee.verifyAdmin, productController.removeProductFeature);

// 导出配置好的路由
module.exports = router; 