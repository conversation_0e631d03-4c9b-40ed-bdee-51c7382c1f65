# 悬浮咨询按钮组件

## 功能介绍

悬浮咨询按钮是一个可定位、可拖拽的小程序组件，提供微信咨询、电话咨询和等待回电三种联系方式。
特色功能：
- 毛玻璃背景效果
- 点击展开动画菜单
- 长按开启拖拽模式
- 自动保存位置到本地存储
- 支持不同页面设置不同位置

## 使用方法

### 简单引用

在页面的WXML中添加以下代码：

```xml
<float-consult positionKey="页面唯一标识"></float-consult>
```

示例：

```xml
<float-consult positionKey="pdhsy"></float-consult>
```

### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|-----|------|-------|-----|
| positionKey | String | 'default' | 位置存储键名，用于区分不同页面的位置设置 |
| position | Object | {right: '30rpx', bottom: '120rpx'} | 初始位置，会被本地存储覆盖 |

### 界面交互

1. **点击**按钮：展开/折叠菜单
2. **长按**按钮：切换拖拽模式
3. **拖拽**按钮：在拖拽模式下可调整位置
4. **点击**遮罩层：关闭菜单

## 自定义指南

### 修改颜色和样式

在组件的WXSS文件中：
- 主按钮颜色：`.main-button` 中的 `background` 属性
- 菜单项颜色：各 `.menu-item` 下的样式
- 遮罩层透明度：`.backdrop.active` 中的 `background` 属性

### 修改菜单项

在组件的WXML文件中修改 `menu-items` 下的内容。
在组件的JS文件中的 `handleAction` 方法中处理相应菜单项的点击事件。

## 技术实现

1. 拖拽功能通过触摸事件实现
2. 位置信息通过 `wx.setStorageSync` 和 `wx.getStorageSync` 存储和读取
3. 毛玻璃效果通过 `backdrop-filter` 实现
4. 动画效果通过 CSS 过渡和动画实现

## 注意事项

1. 确保您的小程序已导入了图标字体库
2. 拖拽结束后位置会自动保存
3. 不同的 positionKey 可以在不同页面保存不同位置设定 