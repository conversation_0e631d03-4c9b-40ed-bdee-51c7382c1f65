// 引入 User 模型
const db = require('../models');
const bcrypt = require('bcryptjs');
const { generateSequentialId, generateUserId, generatePartnerId } = require('../utils/id_helper');
const { Op } = require('sequelize'); // 引入 Op
const User = db.User;
const UserPassword = db.UserPassword; // 引入密码模型

/**
 * [新增] 获取下一个可用的用户ID
 */
exports.getNextUserId = async (req, res) => {
  try {
    const nextId = await generateUserId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个用户ID时出错:', error);
    res.status(500).json({ message: '生成用户ID失败', error: error.message });
  }
};

/**
 * [新增] 获取下一个可用的合伙人ID
 */
exports.getNextPartnerId = async (req, res) => {
  try {
    const nextId = await generatePartnerId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个合伙人ID时出错:', error);
    res.status(500).json({ message: '生成合伙人ID失败', error: error.message });
  }
};

/**
 * 获取所有用户信息
 */
exports.getAllUsers = async (req, res) => {
  try {
    const { q } = req.query;
    const where = {};

    if (q) {
      where[Op.or] = [
        { name: { [Op.like]: `%${q}%` } },
        { mobile: { [Op.like]: `%${q}%` } },
        { user_id: { [Op.like]: `%${q}%` } },
        { partner_id: { [Op.like]: `%${q}%` } }
      ];
    }

    const users = await User.findAll({
      where,
      limit: 20, // 仅返回前20条匹配结果
      attributes: { exclude: ['password'] } // 永远不要在列表API中返回密码
    });
    res.status(200).json(users);
  } catch (error) {
    res.status(500).json({ message: '获取用户列表失败', error: error.message });
  }
};

/**
 * 根据ID获取单个用户信息
 */
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] } // 同样排除密码
    });
    if (user) {
      res.status(200).json(user);
    } else {
      res.status(404).json({ message: '未找到指定ID的用户' });
    }
  } catch (error) {
    res.status(500).json({ message: '获取用户信息失败', error: error.message });
  }
};


// 创建新用户
exports.createUser = async (req, res) => {
  try {
    const { password, is_partner, ...userData } = req.body;

    if (!password) {
        return res.status(400).json({ message: '创建用户时必须提供密码' });
    }

    // --- [修改] user_id 的处理 ---
    if (!userData.user_id) {
      userData.user_id = await generateUserId();
    } else {
      const existing = await User.findOne({ where: { user_id: userData.user_id } });
      if (existing) {
        return res.status(409).json({ message: `用户ID '${userData.user_id}' 已存在。` });
      }
    }

    // --- [修改] partner_id 的处理 ---
    if (is_partner) {
      userData.is_partner = true;
      if (!userData.partner_id) {
        userData.partner_id = await generatePartnerId();
      } else {
        const existing = await User.findOne({ where: { partner_id: userData.partner_id } });
        if (existing) {
          return res.status(409).json({ message: `合伙人ID '${userData.partner_id}' 已存在。` });
        }
      }
    } else {
        userData.is_partner = false;
        userData.partner_id = null;
    }
    
    const user = await User.create(userData);
    
    const hashedPassword = await bcrypt.hash(password, 8);
    await UserPassword.create({
      user_id: user.id, // 使用新创建用户的自增 id
      password: hashedPassword
    });

    // 7. 返回成功响应
    const userResponse = user.toJSON();
    res.status(201).json(userResponse);

  } catch (error) {
    // 统一的错误处理
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0].path;
      const value = error.errors[0].value;
      let fieldName = field;
      if (field === 'mobile' || field === 'idx_mobile') fieldName = '手机号';
      if (field === 'email') fieldName = '邮箱';
      if (field === 'partner_id') fieldName = '合伙人ID';
      if (field === 'user_id' || field === 'idx_user_id_unique') fieldName = '用户业务ID';
      return res.status(409).json({ message: `${fieldName}为 '${value}' 的记录已存在，不能重复。` });
    }
    console.error('创建用户失败:', error);
    res.status(500).json({ message: '创建用户失败', error: error.message });
  }
};

// 更新用户信息
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { password, ...userData } = req.body;
    
    const existingUser = await User.findByPk(id);

    if (!existingUser) {
        return res.status(404).json({ message: '未找到指定ID的用户' });
    }

    // --- [修改] user_id 的唯一性验证 ---
    if (userData.user_id) {
      const existing = await User.findOne({ where: { user_id: userData.user_id, id: { [Op.ne]: id } } });
      if (existing) {
        return res.status(409).json({ message: `用户ID '${userData.user_id}' 已被其他用户占用。` });
      }
    }

    // --- [修改] partner_id 的处理逻辑 ---
    // 场景1: 正在将用户变为合伙人
    if (userData.is_partner === true && !existingUser.is_partner) {
      if (!userData.partner_id) {
        userData.partner_id = await generatePartnerId();
      } else {
        const existing = await User.findOne({ where: { partner_id: userData.partner_id, id: { [Op.ne]: id } } });
        if (existing) {
          return res.status(409).json({ message: `合伙人ID '${userData.partner_id}' 已被其他用户占用。` });
        }
      }
    } 
    // 场景2: 正在修改一个已经是合伙人的用户的 partner_id
    else if (userData.is_partner === true && existingUser.is_partner && userData.partner_id && userData.partner_id !== existingUser.partner_id) {
        const existing = await User.findOne({ where: { partner_id: userData.partner_id, id: { [Op.ne]: id } } });
        if (existing) {
          return res.status(409).json({ message: `合伙人ID '${userData.partner_id}' 已被其他用户占用。` });
        }
    }
    // 场景3: 正在将用户从合伙人变为普通用户
    else if (userData.is_partner === false) {
      userData.partner_id = null; // 清空合伙人ID
    }
    
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 8);
      // 更新或创建密码记录
      await UserPassword.upsert({
        user_id: existingUser.id, // 使用查询到的用户的自增id，这里没变
        password: hashedPassword,
      });
    }

    // [重要修改] 使用主键ID更新 user 表
    const [updated] = await User.update(userData, { where: { id: id } });

    if (updated) {
      // [重要修改] 直接返回已更新的对象，或再次用findByPk查询
      const updatedUser = await User.findByPk(id, {
        attributes: { exclude: ['password'] }
      });
      res.status(200).json(updatedUser);
    } else {
      // 正常情况下，因为前面已经 findByPk 成功，这里不太可能执行到
      res.status(404).json({ message: '更新失败，未找到指定ID的用户' });
    }
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
        const field = error.errors[0].path;
        const value = error.errors[0].value;
        let fieldName = field;
        if (field === 'mobile') fieldName = '手机号';
        if (field === 'email') fieldName = '邮箱';
        if (field === 'partner_id') fieldName = '合伙人ID';
        return res.status(409).json({ message: `${fieldName}为 '${value}' 的记录已存在，不能重复。` });
    }
    console.error('更新用户时出错:', error);
    res.status(500).json({ message: '更新用户失败', error: error.message });
  }
};

// 用户自我更新信息（只允许更新昵称和邮箱）
exports.updateUserProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const { nickname, email } = req.body;

    // 权限检查：用户只能更新自己的信息
    if (req.user.id !== parseInt(id)) {
      return res.status(403).json({ message: '您只能修改自己的信息' });
    }

    const existingUser = await User.findByPk(id);
    if (!existingUser) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 构建更新数据（只允许更新昵称和邮箱）
    const updateData = {};
    if (nickname !== undefined) {
      updateData.nickname = nickname.trim();
    }
    if (email !== undefined) {
      // 邮箱格式验证
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return res.status(400).json({ message: '邮箱格式不正确' });
      }
      updateData.email = email.trim();
    }

    // 更新用户信息
    const [updated] = await User.update(updateData, { where: { id: id } });

    if (updated) {
      const updatedUser = await User.findByPk(id, {
        attributes: { exclude: ['password'] }
      });
      res.status(200).json(updatedUser);
    } else {
      res.status(404).json({ message: '更新失败' });
    }
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0].path;
      const value = error.errors[0].value;
      let fieldName = field;
      if (field === 'email') fieldName = '邮箱';
      return res.status(409).json({ message: `${fieldName}为 '${value}' 的记录已存在，不能重复。` });
    }
    console.error('更新用户信息时出错:', error);
    res.status(500).json({ message: '更新用户信息失败', error: error.message });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params; // 这个 id 现在是自增主键 ID
    // [重要修改] 使用主键ID删除
    const deleted = await User.destroy({ where: { id: id } });
    if (deleted) {
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定ID的用户' });
    }
  } catch (error) {
    res.status(500).json({ message: '删除用户失败', error: error.message });
  }
}; 