const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserAuthentication = sequelize.define('UserAuthentication', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true, // 一个用户只有一条认证记录
    comment: '关联user.id'
  },
  id_card: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '身份认证号（如身份证）'
  },
  bank_card: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '银行卡号'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'user_authentication',
  timestamps: true,
  comment: '用户认证信息表',
  charset: 'utf8mb4',
  collate: 'utf8mb4_0900_ai_ci'
});

module.exports = UserAuthentication; 