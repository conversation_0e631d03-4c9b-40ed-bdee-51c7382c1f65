Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前激活的tab索引
    activeTab: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        this.scrollToCenter(newVal);
      }
    },
    // 导航栏标题列表
    tabs: {
      type: Array,
      value: [
        { id: 'hkj', name: '好会计' },
        { id: 'ydz', name: '易代账' },
        { id: 'tonline', name: 'T+Online' },
        { id: 'hyc', name: '好业财' },
        { id: 'hsy', name: '好生意' },
        { id: 'tzsypjb', name: 'T+专普' }
      ]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    scrollLeft: 0,
    tabWidths: [],
    tabPositions: [],
    navWidth: 0,
    // 玻璃效果始终激活
    glassEffectActive: true,
    // 玻璃效果透明度固定为最大值
    glassOpacity: 1,
    // 添加总内容宽度
    totalContentWidth: 0,
    // 这里可能有其他数据属性
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 在组件被挂载后，初始化标签宽度
      this.initTabsWidth();
      
      // 监听窗口尺寸变化，重新计算宽度
      // 使用推荐的新API替代已弃用的wx.getSystemInfo
      const windowInfo = wx.getWindowInfo();
      this.setData({
        windowWidth: windowInfo.windowWidth
      });
      
      // 延迟执行以确保组件已完全渲染
      setTimeout(() => {
        this.initTabsWidth();
      }, 100);
    },
    
    detached: function() {
      // 组件销毁时清理工作
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化所有标签的宽度和位置数据
    initTabsWidth: function() {
      const query = this.createSelectorQuery();
      query.selectAll('.nav-item').boundingClientRect();
      query.select('.nav-scroll').boundingClientRect();
      
      query.exec(res => {
        if (res && res[0] && res[1]) {
          const tabItems = res[0];
          const navScroll = res[1];
          
          const widths = tabItems.map(item => item.width);
          const positions = [];
          
          let currentPos = 0;
          let totalWidth = 0;
          
          for (let i = 0; i < widths.length; i++) {
            positions.push(currentPos);
            currentPos += widths[i];
            totalWidth += widths[i];
          }
          
          // 添加额外的右侧内边距，确保最后一个标签完全可见
          totalWidth += 100; // 添加额外的右侧内边距
          
          this.setData({
            tabWidths: widths,
            tabPositions: positions,
            navWidth: navScroll.width,
            totalContentWidth: totalWidth
          });
          
          // 初始化完成后滚动到当前活动标签
          this.scrollToCenter(this.data.activeTab);
        }
      });
    },
    
    /**
     * 计算并滚动到目标位置，使选中的tab居中
     */
    scrollToCenter: function(index) {
      // 如果尚未初始化宽度数据，先进行初始化
      if (this.data.tabWidths.length === 0) {
        setTimeout(() => {
          this.initTabsWidth();
        }, 10);
        return;
      }
      
      const { tabPositions, tabWidths, navWidth } = this.data;
      
      if (tabPositions.length <= index || tabWidths.length <= index) {
        return;
      }
      
      // 计算标签的中心点位置
      const tabCenter = tabPositions[index] + tabWidths[index] / 2;
      
      // 计算需要滚动的距离，使标签居中
      let scrollLeft = tabCenter - navWidth / 2;
      
      // 边界处理，确保不会滚动过头
      scrollLeft = Math.max(0, scrollLeft);
      
      // 确保最后一个标签可以完全滚动到视图中
      const maxScrollLeft = Math.max(0, this.data.totalContentWidth - navWidth);
      scrollLeft = Math.min(scrollLeft, maxScrollLeft);
      
      // 更新滚动位置
      this.setData({ scrollLeft });
    },

    /**
     * 切换标签页
     */
    onTabClick: function(e) {
      const index = e.currentTarget.dataset.index;
      
      // 如果点击的是当前已激活的标签，不执行任何操作
      if (index === this.data.activeTab) {
        return;
      }
      
      // 更新激活的标签
      this.setData({
        activeTab: index
      });
      
      // 传递tabchange事件到父组件
      this.triggerEvent('tabchange', { 
        index,
        id: this.properties.tabs[index].id
      });
      
      // 移除直接跳转的逻辑，由父组件处理页面跳转
    }
  }
}) 

