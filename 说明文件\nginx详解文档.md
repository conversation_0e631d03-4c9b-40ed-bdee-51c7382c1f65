# ecosystem.config.js 配置文件详解

## 回答你的问题

### 1. 这个文件还需要吗？
**需要！** 但是需要大幅修改。

### 2. 这个文件干什么的？
`ecosystem.config.js` 是 **PM2 进程管理器的配置文件**，用来管理你的 Node.js 后端服务。

### 3. 前端部分代码还需要吗？
**完全不需要！** 因为：
- 你已经用 nginx 直接提供前端静态文件（从 `dist` 目录）
- 前端不需要运行开发服务器
- 生产环境只需要构建好的静态文件

## 修改前后对比

### 修改前（有问题的配置）
```javascript
{
  name: "customer-frontend",  // ❌ 不需要
  script: "npm",
  args: "run dev",           // ❌ 开发模式
  cwd: "./frontend",
  watch: true,
  env: {
    PORT: 5173,              // ❌ 开发端口
    NODE_ENV: "development"  // ❌ 开发环境
  }
}
```

### 修改后（生产环境配置）
```javascript
{
  name: "customer-backend",
  script: "index.js",
  cwd: "./backend",
  instances: 1,
  exec_mode: "fork",
  watch: false,              // ✅ 生产环境关闭watch
  max_memory_restart: "1G",  // ✅ 内存限制
  env: {
    PORT: 3002,
    NODE_ENV: "production"   // ✅ 生产环境
  },
  // ✅ 日志配置
  log_file: "./logs/customer-backend.log",
  error_file: "./logs/customer-backend-error.log",
  out_file: "./logs/customer-backend-out.log"
}
```

## 你的当前架构

### 生产环境架构图
```
用户请求
    ↓
Nginx (端口 80/443)
    ↓
├── admin.bogoo.net → 静态文件 (frontend/dist) ✅
└── service.bogoo.net → PM2管理的后端 (端口3002) ✅
```

### PM2 当前状态
```
┌────┬─────────────────────┬─────────┬───────────┐
│ id │ name                │ status  │ 说明      │
├────┼─────────────────────┼─────────┼───────────┤
│ 1  │ customer-backend    │ online  │ ✅ 正常   │
│ 3  │ partner-admin       │ errored │ ❌ 错误   │
│ 2  │ partner-server      │ errored │ ❌ 错误   │
└────┴─────────────────────┴─────────┴───────────┘
```

## 重启服务的步骤

### 1. 重启后端服务（应用新配置）
```bash
cd /var/www/customer_system
pm2 restart customer-backend
```

### 2. 或者完全重新加载配置
```bash
pm2 delete customer-backend
pm2 start ecosystem.config.js
```

### 3. 检查服务状态
```bash
pm2 list
pm2 logs customer-backend
```

## 前端部署流程

### 开发时
```bash
cd frontend
npm run dev    # 开发服务器 (端口5173)
```

### 部署到生产环境
```bash
cd frontend
npm run build  # 构建到 dist 目录
# nginx 直接提供 dist 目录的静态文件
```

## 总结

### 你的配置修改要点：
1. **删除了前端配置** - 因为 nginx 直接提供静态文件
2. **后端改为生产环境** - `NODE_ENV: "production"`
3. **关闭了 watch** - 生产环境不需要文件监控
4. **添加了日志配置** - 便于排查问题
5. **添加了内存限制** - 防止内存泄漏

### 为什么这样配置？
- **前端**：nginx 直接提供静态文件，性能更好
- **后端**：PM2 管理进程，自动重启，日志记录
- **分离部署**：前后端独立，更容易维护

你的架构是标准的生产环境配置！

# Nginx详解文档 - 基于你的项目配置

## 1. Nginx是什么？干什么用的？

### 简单理解
Nginx（读作"engine-x"）是一个**Web服务器**和**反向代理服务器**。你可以把它想象成一个**智能的门卫**，站在你的服务器前面，负责：

1. **接收**来自互联网的所有请求
2. **分发**这些请求到正确的地方
3. **处理**SSL证书加密
4. **返回**结果给用户

### 具体作用
- **Web服务器**：直接提供静态文件（HTML、CSS、JS、图片等）
- **反向代理**：把请求转发给后端应用程序
- **负载均衡**：在多个服务器之间分配请求
- **SSL终端**：处理HTTPS加密和解密

## 2. 为什么有了域名还要Nginx？

### 域名只是"地址"，不是"服务员"

想象一下：
- **域名**就像你家的**地址**（比如：北京市朝阳区XX街XX号）
- **Nginx**就像你家的**门卫**或**管家**

当有人（用户）想访问你家（网站）时：
1. 他们通过地址（域名）找到你家
2. 但到了门口，需要门卫（Nginx）来：
   - 确认身份（SSL证书验证）
   - 指引方向（路由到正确的服务）
   - 提供服务（返回网页内容）

### 没有Nginx会怎样？

如果没有Nginx，用户访问你的域名时：
- 无法处理HTTPS（不安全）
- 无法同时运行多个服务
- 无法优化性能
- 无法处理静态文件

## 3. 基于你的配置详解

### 你的架构图
```
互联网用户
    ↓
域名解析 (DNS)
    ↓
Nginx (端口80/443)
    ↓
分发到不同服务：
├── admin.bogoo.net → 前端静态文件 (/var/www/customer_system/frontend/dist)
└── service.bogoo.net → 后端API (localhost:3002)
```

### 配置文件分析

#### admin.bogoo.net 配置
```nginx
server {
    server_name admin.bogoo.net;
    root /var/www/customer_system/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

**作用**：
- 当用户访问 `admin.bogoo.net` 时
- Nginx直接从 `/var/www/customer_system/frontend/dist` 目录提供静态文件
- 这是你的**管理后台前端**

#### service.bogoo.net 配置
```nginx
server {
    server_name service.bogoo.net;
    
    location /api/ {
        proxy_pass http://localhost:3002;
        # 各种代理头部设置...
    }
    
    location /uploads/ {
        proxy_pass http://localhost:3002;
        # 各种代理头部设置...
    }
}
```

**作用**：
- 当用户访问 `service.bogoo.net/api/xxx` 时
- Nginx把请求转发给运行在3002端口的后端服务
- 这是你的**API服务接口**

## 4. SSL证书的关系

### SSL证书是什么？
SSL证书就像**身份证**，证明你的网站是真实可信的，并且提供加密通信。

### 在你的配置中的作用

#### HTTPS监听（端口443）
```nginx
listen 443 ssl;
ssl_certificate /etc/letsencrypt/live/service.bogoo.net/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/service.bogoo.net/privkey.pem;
```

**解释**：
- Nginx监听443端口（HTTPS端口）
- 使用Let's Encrypt免费SSL证书
- 自动加密所有通信

#### HTTP重定向（端口80）
```nginx
server {
    listen 80;
    server_name service.bogoo.net;
    return 301 https://$host$request_uri;
}
```

**解释**：
- 当用户访问HTTP（不安全）时
- 自动重定向到HTTPS（安全）
- 强制使用加密连接

### SSL证书的好处
1. **数据加密**：防止信息被窃取
2. **身份验证**：证明网站真实性
3. **SEO优势**：搜索引擎更喜欢HTTPS网站
4. **用户信任**：浏览器显示"安全"标识

## 5. 实际工作流程

### 用户访问admin.bogoo.net的流程：
1. 用户在浏览器输入 `admin.bogoo.net`
2. DNS解析到你的服务器IP
3. Nginx接收请求
4. 检查SSL证书，建立加密连接
5. 根据server_name匹配到admin配置
6. 从 `/var/www/customer_system/frontend/dist` 返回HTML文件
7. 用户看到管理后台页面

### 用户访问service.bogoo.net/api的流程：
1. 前端JavaScript发起API请求到 `service.bogoo.net/api/xxx`
2. Nginx接收请求
3. 匹配到service配置的 `/api/` location
4. 转发请求到 `localhost:3002`
5. 后端处理请求并返回数据
6. Nginx将结果返回给前端

## 6. 总结

### Nginx的核心价值
1. **统一入口**：所有请求都通过Nginx进入
2. **安全保障**：处理SSL证书和HTTPS
3. **性能优化**：缓存、压缩、负载均衡
4. **服务分离**：前端和后端可以独立部署
5. **域名管理**：一个服务器可以托管多个域名

### 为什么必须要Nginx？
- **域名**只是告诉用户"去哪里"
- **Nginx**决定"到了之后做什么"
- 没有Nginx，域名就像一个没有门卫的大楼，用户找到了地址但进不去门

你的项目通过Nginx实现了：
- 前后端分离部署
- HTTPS安全访问
- API接口代理
- 静态文件服务

这就是为什么有了域名还需要Nginx的原因！
