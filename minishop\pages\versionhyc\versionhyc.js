const app = getApp()
const navService = require('../../utils/navigator.js');

Page({
  data: {
    promotionInfo: {},
    // 账套数基础价格配置
    packageBasePrice: {
      '普及版': 3000,
      '标准版': 5660,
      '专业版': 8980
    },
    // 当前选择
    selectedPackage: '普及版',    // 默认选择标准版账套
    selectedDuration: 1,     // 默认选择1年
    selectedDiscount: 0.85,  // 默认折扣
    selectedDiscountText: '', // 新增：用于显示的折扣文本
    // 计算结果
    originalPrice: 0,        // 原价
    discountPrice: 0,        // 特惠价
    dailyPrice: 0,           // 每账套每天价格
    giftProduct: '',         // 赠品产品
    progressPercent: '0%',   // 满减进度条百分比
    // 好业财各版本功能特点
    packageFeatures: {
      '普及版': [
        '总账报表管理、多账套管理、固定资产管理、工资管理：员工小程序查工资',
        '采购订单、采购进货单、发票记录、采购费用分摊、多维度采购统计分析',
        '销售订单、销售出库单、发票记录、销售费用分摊、销售价格策略、智能批量改、多维度销售毛利分析',
        '盘点、调拨、组装拆卸、手机电脑随时查库存、 自动生成收发存汇总表、库存成本实时核算',
        '往来资金：收款、付款、预收、预付管理、费用支出、其他收入管理、日记账、现金银行管理、客户\供应商智能对账',
        '经营管理：手机端电脑端查经营日报表、实时查看公司业务利润预估',
        '企业上下游协同：商品档案同步、购销业务高效协同',
        '行业特性：批次、保质期管理、商品多种计量方式管理、商品多规格管理、商品条码管理',
        '特色功能：手机端、电脑端、平板电脑、手持设备PDA多端应用、远程异地打印、自定义专属表单、自定义专属报表'
      ],
      '标准版': [
        '包含普及版所有功能',
        '营销管理',
        '采购申请流程',
        '业务员提成自动计算',
        '客户信用管控',
        '车访销管理(需加购)',
        '生产管理',
        '审批流',
        '智能仓储WMS(基础应用)',
        '发票管理',
        '经营管理-商品智能分析、客户分层分析'
      ],
      '专业版': [
        '包含标准版所有功能',       
        '财务多机构',        
        '采购损耗、采购暂估、采购冲抵等',
        '销售损耗、销售冲抵等',
        '预算管理、合同管理、进度管理、收入合同、支出合同、项目报表、项目档案',
        '智慧门店、零售 pos 收银端、微商城、微商城自提点',      
        '手机旺铺、自定义报表、条码管理、WMS、数智参谋、零售看板数据、服务看板数据、自定义表单、单据多模板(按需加购)'
      ]
    },
    // 当前版本的功能列表
    hkjFeatures: [],
    activeTab: 0,
    tabs: ['套餐详情', '活动规则', '常见问题'],
    activityTimeRange: ""
  },

  onLoad: function () {
    // 获取促销信息
    let promotionInfo = app.globalData.promotionInfo || {};
    
    // 格式化折扣规则，添加用于显示的 discountText
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      promotionInfo.rules = promotionInfo.rules.map(rule => {
        return {
          ...rule,
          discountText: parseFloat((rule.discount * 10).toFixed(1))
        };
      });
    }

    // 格式化活动时间范围
    const startTime = promotionInfo.startTime || '';
    const endTime = promotionInfo.endTime || '';
    const activityTimeRange = startTime.replace(/\//g, '-').substring(0, 10) + 
                             ' 至 ' + 
                             endTime.replace(/\//g, '-').substring(0, 10);
    
    // 获取默认折扣（1年折扣）
    let defaultDiscount = 0.85;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      const rule = promotionInfo.rules.find(item => item.period === 1);
      if (rule) {
        defaultDiscount = rule.discount;
      }
    }
    
    // 初始化默认版本的功能特点
    const defaultFeatures = this.data.packageFeatures['普及版'] || [];
    
    this.setData({
      promotionInfo: promotionInfo,
      activityTimeRange: activityTimeRange,
      selectedDiscount: defaultDiscount,
      selectedDiscountText: parseFloat((defaultDiscount * 10).toFixed(1)), // 设置初始折扣文本
      hkjFeatures: defaultFeatures
    });
    
    // 计算初始价格
    this.calculatePrice();
  },
  
  /**
   * 选择账套数
   */
  selectPackage: function(e) {
    const packageSize = e.currentTarget.dataset.package;
    
    // 获取选择版本的功能特点
    const features = this.data.packageFeatures[packageSize] || [];
    
    this.setData({ 
      selectedPackage: packageSize,
      hkjFeatures: features
    });
    this.calculatePrice();
  },
  
  /**
   * 选择时长
   */
  selectDuration: function(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    
    // 从促销规则中找出对应时长的折扣
    let discount = 1;
    if (this.data.promotionInfo.rules && this.data.promotionInfo.rules.length > 0) {
      const rule = this.data.promotionInfo.rules.find(item => item.period === duration);
      if (rule) {
        discount = rule.discount;
      }
    }
    
    this.setData({ 
      selectedDuration: duration,
      selectedDiscount: discount,
      selectedDiscountText: parseFloat((discount * 10).toFixed(1)) // 更新折扣文本
    });
    this.calculatePrice();
  },
  
  /**
   * 计算满减进度百分比
   */
  calculateProgressPercent: function(price) {
    const gifts = this.data.promotionInfo.gifts || [];
    if (!gifts || gifts.length < 3) {
      return '0%';
    }
    
    try {
      if (price >= gifts[2].threshold) {
        return '100%';
      } else if (price >= gifts[1].threshold) {
        return '66%';
      } else if (price >= gifts[0].threshold) {
        return '33%';
      } else {
        return '0%';
      }
    } catch (e) {
      console.error('计算满减进度出错:', e);
      return '0%';
    }
  },
  
  /**
   * 计算价格和赠品
   */
  calculatePrice: function() {
    const { selectedPackage, selectedDuration, packageBasePrice, promotionInfo } = this.data;
    
    // 1. 计算原价
    const basePrice = packageBasePrice[selectedPackage];
    const originalPrice = basePrice * selectedDuration;
    
    // 2. 获取对应年限的折扣
    let discount = 1;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      // 从促销规则中查找对应年限的折扣
      const rule = promotionInfo.rules.find(item => item.period === selectedDuration);
      if (rule) {
        discount = rule.discount;
      }
    }
    
    // 3. 计算特惠价
    const discountPrice = (originalPrice * discount).toFixed(1);
    
    // 4. 计算每版本每天价格
    const dailyPrice = (discountPrice / (selectedDuration * 365) ).toFixed(2);
    
    // 5. 根据特惠价确定赠品
    let giftProduct = '';
    const price = parseFloat(discountPrice);
    const gifts = promotionInfo.gifts || [];
    
    // 从大到小查找适用的赠品区间
    for (let i = gifts.length - 1; i >= 0; i--) {
      if (price >= gifts[i].threshold) {
        giftProduct = gifts[i].name;
        break;
      }
    }
    
    // 6. 计算满减进度条百分比
    const progressPercent = this.calculateProgressPercent(price);
    
    this.setData({
      originalPrice,
      discountPrice,
      dailyPrice,
      giftProduct,
      progressPercent
    });
  },
  


  /**
   * 切换标签页
   */
  switchTab: function(e) {
    const tabIndex = parseInt(e.currentTarget.dataset.index);
    this.setData({
      activeTab: tabIndex
    });
  },



  /**
   * 用户分享
   */
  onShareAppMessage: function () {
    return {
      title: '好业财618超级特惠，力度空前，一年仅此一次！',
      path: '/pages/promotion/promotion',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '好业财618超级特惠，力度空前，一年仅此一次！',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },



  /**
   * 显示产品详情
   */
  showProductDetail: function() {
    navService.navigateToProduct('hyc');
  }
}) 