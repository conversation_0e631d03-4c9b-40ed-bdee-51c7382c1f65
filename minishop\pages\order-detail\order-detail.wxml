<!--pages/order-detail/order-detail.wxml-->
<wxs module="utils">
  // 格式化价格
  function formatPrice(price) {
    if (!price) return '0.00';
    return parseFloat(price).toFixed(2);
  }
  
  // 格式化日期
  function formatDate(dateStr) {
    if (!dateStr) return '';
    var date = getDate(dateStr);
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString().padStart(2, '0');
    var day = date.getDate().toString().padStart(2, '0');
    var hour = date.getHours().toString().padStart(2, '0');
    var minute = date.getMinutes().toString().padStart(2, '0');
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
  }
  
  // 获取订单状态样式
  function getStatusClass(status) {
    switch(status) {
      case '待付款': return 'pending';
      case '已付款': return 'paid';
      case '已完成': return 'completed';
      case '已取消': return 'cancelled';
      default: return 'default';
    }
  }
  
  module.exports = {
    formatPrice: formatPrice,
    formatDate: formatDate,
    getStatusClass: getStatusClass
  };
</wxs>

<view class="page-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="navbar-title">订单详情</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <view class="error-text">{{error}}</view>
    <button class="retry-btn" bindtap="loadOrderDetail">重试</button>
  </view>

  <!-- 订单详情内容 -->
  <view wx:else class="content-container">
    <!-- 订单状态卡片 -->
    <view class="info-card status-card">
      <view class="status-header">
        <view class="status-icon {{utils.getStatusClass(order.status)}}">
          <text wx:if="{{order.status === '待付款'}}">💰</text>
          <text wx:elif="{{order.status === '已付款'}}">✅</text>
          <text wx:elif="{{order.status === '已完成'}}">🎉</text>
          <text wx:else>❌</text>
        </view>
        <view class="status-info">
          <view class="status-text {{utils.getStatusClass(order.status)}}">{{order.status}}</view>
          <view class="status-desc" wx:if="{{order.status === '待付款'}}">请尽快完成支付</view>
          <view class="status-desc" wx:elif="{{order.status === '已付款'}}">订单已支付成功</view>
          <view class="status-desc" wx:elif="{{order.status === '已完成'}}">订单已完成</view>
          <view class="status-desc" wx:else>订单已取消</view>
        </view>
      </view>
    </view>

    <!-- 订单基本信息 -->
    <view class="info-card">
      <view class="card-title">
        <text class="title-icon">📋</text>
        <text>订单信息</text>
      </view>
      
      <view class="order-info-list">
        <view class="info-item">
          <view class="info-label">订单号</view>
          <view class="info-value">{{order.order_number}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">下单时间</view>
          <view class="info-value">{{utils.formatDate(order.createdAt)}}</view>
        </view>
        <view class="info-item" wx:if="{{order.user}}">
          <view class="info-label">下单用户</view>
          <view class="info-value">{{order.user.name}}</view>
        </view>
        <view class="info-item" wx:if="{{order.enterprise}}">
          <view class="info-label">所属企业</view>
          <view class="info-value">{{order.enterprise.enterprise_name}}</view>
        </view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="info-card">
      <view class="card-title">
        <text class="title-icon">📦</text>
        <text>商品清单</text>
      </view>
      
      <view class="product-list">
        <view wx:for="{{order.items}}" wx:key="id" class="product-item">
          <view class="product-info">
            <view class="product-name">{{item.product ? item.product.product_name : '未知产品'}}</view>
            <view class="product-spec" wx:if="{{item.product && item.product.version_name}}">
              版本：{{item.product.version_name}}
            </view>
            <view class="product-quantity">数量：{{item.quantity}}</view>
          </view>
          <view class="product-price">
            <view class="unit-price">单价：¥{{utils.formatPrice(item.unit_price)}}</view>
            <view class="total-price">小计：¥{{utils.formatPrice(item.total_price)}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 价格汇总 -->
    <view class="info-card">
      <view class="card-title">
        <text class="title-icon">💰</text>
        <text>费用明细</text>
      </view>
      
      <view class="price-summary">
        <view class="summary-item">
          <view class="summary-label">商品总额</view>
          <view class="summary-value">¥{{utils.formatPrice(order.total_amount)}}</view>
        </view>
        <view class="summary-item total">
          <view class="summary-label">订单总额</view>
          <view class="summary-value">¥{{utils.formatPrice(order.total_amount)}}</view>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="info-card" wx:if="{{order.remark}}">
      <view class="card-title">
        <text class="title-icon">📝</text>
        <text>订单备注</text>
      </view>
      <view class="remark-content">{{order.remark}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        wx:if="{{order.status === '待付款'}}" 
        class="action-btn primary" 
        bindtap="payOrder"
      >
        立即付款
      </button>
      
      <button 
        wx:if="{{order.status === '待付款'}}" 
        class="action-btn secondary" 
        bindtap="cancelOrder"
      >
        取消订单
      </button>
      
      <button 
        class="action-btn secondary" 
        bindtap="contactService"
      >
        联系客服
      </button>
    </view>
  </view>
  
  <!-- 浮动咨询组件 -->
  <float-consult />
</view>
