<template>
  <div class="asset-form-header">
    <!-- 资产表单表头组件 - 处理资产ID、企业ID、用户ID、status字段 -->
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px" :disabled="readonly" class="responsive-form">
      <div class="form-grid">
        <!-- 资产ID -->
        <el-form-item label="资产ID" prop="asset_id" class="field-asset-id">
          <el-input
            v-model="formData.asset_id"
            placeholder="自动生成"
            readonly
          />
        </el-form-item>

        <!-- 企业ID -->
        <el-form-item label="关联企业" prop="enterprise_id" class="field-enterprise">
          <!-- 只读模式显示企业名称 -->
          <el-input
            v-if="readonly"
            :value="getEnterpriseName()"
            readonly
            placeholder="未选择企业"
          />
          <!-- 编辑模式显示选择器 -->
          <el-select
            v-else
            v-model="formData.enterprise_id"
            placeholder="请选择企业"
            filterable
            clearable
            @change="handleEnterpriseChange"
          >
            <el-option
              v-for="item in filteredEnterpriseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 用户ID -->
        <el-form-item label="关联用户" prop="user_id" class="field-user">
          <!-- 只读模式显示用户名称 -->
          <el-input
            v-if="readonly"
            :value="getUserName()"
            readonly
            placeholder="未选择用户"
          />
          <!-- 编辑模式显示选择器 -->
          <el-select
            v-else
            v-model="formData.user_id"
            placeholder="请选择用户"
            filterable
            clearable
            @change="handleUserChange"
          >
            <el-option
              v-for="item in filteredUserOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 资产状态 -->
        <el-form-item label="资产状态" prop="status" class="field-status">
          <el-select
            v-model="formData.status"
            placeholder="请选择状态"
            :disabled="readonly"
          >
            <el-option label="在线" value="在线" />
            <el-option label="过期" value="过期" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props定义
const props = defineProps({
  // 表单数据对象
  formData: {
    type: Object,
    required: true
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  // 企业选项列表
  enterpriseOptions: {
    type: Array,
    default: () => []
  },
  // 用户选项列表
  userOptions: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['enterprise-change', 'user-change'])

// 表单引用
const formRef = ref(null)

// 表单验证规则
const formRules = {
  asset_id: [
    { required: true, message: '资产ID不能为空', trigger: 'blur' }
  ],
  enterprise_id: [
    { required: true, message: '请选择关联企业', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择资产状态', trigger: 'change' }
  ]
}

// 计算属性：根据选择的企业过滤用户选项
const filteredUserOptions = computed(() => {
  if (!props.formData.enterprise_id || !props.userOptions.length) {
    return props.userOptions
  }

  // 找到选中的企业
  const selectedEnterprise = props.enterpriseOptions.find(
    enterprise => enterprise.id === props.formData.enterprise_id
  )

  // 如果企业有关联用户，只显示该用户；否则显示所有用户
  if (selectedEnterprise && selectedEnterprise.user_id) {
    return props.userOptions.filter(user => user.id === selectedEnterprise.user_id)
  }

  return props.userOptions
})

// 计算属性：根据选择的用户过滤企业选项
const filteredEnterpriseOptions = computed(() => {
  if (!props.formData.user_id || !props.enterpriseOptions.length) {
    return props.enterpriseOptions
  }

  // 查找该用户关联的企业
  const userEnterprises = props.enterpriseOptions.filter(
    enterprise => enterprise.user_id === props.formData.user_id
  )

  // 如果用户有关联企业，只显示这些企业；否则显示所有企业
  return userEnterprises.length > 0 ? userEnterprises : props.enterpriseOptions
})

// 获取企业名称（用于只读模式显示）
const getEnterpriseName = () => {
  if (!props.formData.enterprise_id) return ''

  // 优先从资产数据的关联对象中获取
  if (props.formData.enterprise && props.formData.enterprise.name) {
    return props.formData.enterprise.name
  }

  // 从选项列表中查找
  const enterprise = props.enterpriseOptions.find(
    item => item.id === props.formData.enterprise_id
  )
  return enterprise ? enterprise.name : `ID: ${props.formData.enterprise_id}`
}

// 获取用户名称（用于只读模式显示）
const getUserName = () => {
  if (!props.formData.user_id) return ''

  // 优先从资产数据的关联对象中获取
  if (props.formData.user && props.formData.user.name) {
    return props.formData.user.name
  }

  // 从选项列表中查找
  const user = props.userOptions.find(
    item => item.id === props.formData.user_id
  )
  return user ? user.name : `ID: ${props.formData.user_id}`
}

// 企业变更处理
const handleEnterpriseChange = (enterpriseId) => {
  // 找到选中的企业
  const selectedEnterprise = props.enterpriseOptions.find(
    enterprise => enterprise.id === enterpriseId
  )

  // 如果企业有关联用户，自动选择该用户
  if (selectedEnterprise && selectedEnterprise.user_id) {
    props.formData.user_id = selectedEnterprise.user_id
  } else {
    // 如果企业没有关联用户，清空用户选择
    props.formData.user_id = null
  }

  // 触发企业变更事件
  emit('enterprise-change', enterpriseId)
}

// 用户变更处理
const handleUserChange = (userId) => {
  // 如果选择了用户，需要过滤企业选项
  if (userId) {
    // 查找该用户关联的企业
    const userEnterprises = props.enterpriseOptions.filter(
      enterprise => enterprise.user_id === userId
    )

    // 如果用户只关联一个企业，自动选择
    if (userEnterprises.length === 1) {
      props.formData.enterprise_id = userEnterprises[0].id
    }
    // 如果用户关联多个企业或没有关联企业，保持当前企业选择
  }

  // 触发用户变更事件
  emit('user-change', userId)
}

// 监听用户变更
watch(() => props.formData.user_id, (newUserId) => {
  handleUserChange(newUserId)
})

// 表单验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单验证
const resetValidation = () => {
  formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetValidation
})
</script>

<style scoped>
.asset-form-header {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  /* 启用容器查询 */
  container-type: inline-size;
}

/* 响应式Grid布局 - 使用固定列数避免布局问题 */
.responsive-form .form-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距 */
  /* 使用固定2列布局，确保不会挤压 */
  grid-template-columns: 1fr 1fr;
  align-items: start; /* 顶部对齐 */
}

/* 输入框宽度优化 - 确保所有输入框都有合适的宽度 */
.responsive-form .el-input,
.responsive-form .el-select {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

/* 表单项间距和溢出控制 */
.responsive-form .el-form-item {
  margin-bottom: 16px; /* 适当的间距 */
  overflow: hidden; /* 防止内容溢出 */
}

.responsive-form .el-form-item__content {
  overflow: hidden; /* 防止内容溢出 */
}

/* 响应式布局适配 */
@media (max-width: 768px) {
  .responsive-form .form-grid {
    grid-template-columns: 1fr; /* 小屏幕使用单列 */
    gap: 16px;
  }

  .asset-form-header {
    padding: 16px;
  }
}

/* 只读模式样式调整 */
.el-form--disabled .el-input__inner,
.el-form--disabled .el-select .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}
</style>
