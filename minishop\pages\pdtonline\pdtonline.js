/**
 * T+Online产品介绍页面
 * 支持左右滑动切换页面、上下滑动查看详情
 */
const navService = require('../../utils/navigator.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品海报信息
    posterTitle: 'T+Online',
    posterSubtitle: '做生意、管业务、控费用',
    posterSlogan: '财务数据本地存·云端多终端连接',
    
    // 触摸相关数据
    touchStartY: 0,
    touchMoveY: 0,
    touchStartX: 0,
    touchMoveX: 0,
    isAnimating: false,
    lastSwipeTime: 0,
    swipeThreshold: 50,
    
    // 页面配置
    activeTab: 2,
    productKey: 'tonline',
    isAtFirstScreen: true,
    
    // 核心亮点图标
    highlightIcons: {
      autoAccounting: 'icon-kuaisugaoxiao',
      ufoReport: 'icon-tubiaozhutu',
      endPeriod: 'icon-lianjie',
      voucherManagement: 'icon-fapiao',
      crossYearQuery: 'icon-bingtutubiao',
      multiAccounting: 'icon-renminbi',
      purchaseManagement: 'icon-wuliu',
      salesManagement: 'icon-dingdan',
      inventoryAccounting: 'icon-cangchucangku'
    },
    
    // 功能对比数据
    isMoreFunctionsVisible: false,
    allFunctions: [
      {
        icon: 'icon-dianzixinxi',
        name: '移动办公',
        desc: '随时随地处理业务',
        traditionalDesc: '传统软件需在电脑端操作，不支持移动端'
      },
      {
        icon: 'icon-shujufenxi',
        name: '数据同步',
        desc: '多端数据实时同步',
        traditionalDesc: '各系统数据独立，需手动同步'
      },
      {
        icon: 'icon-tubiaozhutu',
        name: '智能报表',
        desc: '一键生成各类报表',
        traditionalDesc: '报表生成繁琐，格式单一'
      },
      {
        icon: 'icon-kuaisugaoxiao',
        name: '处理速度',
        desc: '云端高速处理',
        traditionalDesc: '受本地设备性能限制，处理速度慢'
      },
      {
        icon: 'icon-yinzhangrenzheng',
        name: '数据安全',
        desc: '多重加密保护',
        traditionalDesc: '本地存储，易丢失，缺乏备份机制'
      },
      {
        icon: 'icon-dingdan',
        name: '业务管理',
        desc: '全流程一体化管理',
        traditionalDesc: '各业务环节分散，难以统一管理'
      },
      {
        icon: 'icon-renminbi',
        name: '成本优势',
        desc: '按需付费，成本可控',
        traditionalDesc: '一次性投入大，后期维护成本高'
      },
      {
        icon: 'icon-shujufenxi',
        name: '数据分析',
        desc: '智能分析业务数据',
        traditionalDesc: '数据分析能力弱，缺乏决策支持'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 打印调试信息
    console.log('==== T+Online页面加载 ====');
    console.log('allFunctions数据:', this.data.allFunctions);
    console.log('allFunctions长度:', this.data.allFunctions ? this.data.allFunctions.length : 0);
    
    // 检查allFunctions初始化是否正确
    if (!this.data.allFunctions || this.data.allFunctions.length === 0) {
      // 手动初始化allFunctions数据，以防数据丢失
      this.setData({
        allFunctions: [
          {
            icon: 'icon-dianzixinxi',
            name: '移动办公',
            desc: '随时随地处理业务',
            traditionalDesc: '传统软件需在电脑端操作，不支持移动端'
          },
          {
            icon: 'icon-shujufenxi',
            name: '数据同步',
            desc: '多端数据实时同步',
            traditionalDesc: '各系统数据独立，需手动同步'
          },
          {
            icon: 'icon-tubiaozhutu',
            name: '智能报表',
            desc: '一键生成各类报表',
            traditionalDesc: '报表生成繁琐，格式单一'
          },
          {
            icon: 'icon-kuaisugaoxiao',
            name: '处理速度',
            desc: '云端高速处理',
            traditionalDesc: '受本地设备性能限制，处理速度慢'
          },
          {
            icon: 'icon-yinzhangrenzheng',
            name: '数据安全',
            desc: '多重加密保护',
            traditionalDesc: '本地存储，易丢失，缺乏备份机制'
          },
          {
            icon: 'icon-dingdan',
            name: '业务管理',
            desc: '全流程一体化管理',
            traditionalDesc: '各业务环节分散，难以统一管理'
          },
          {
            icon: 'icon-renminbi',
            name: '成本优势',
            desc: '按需付费，成本可控',
            traditionalDesc: '一次性投入大，后期维护成本高'
          },
          {
            icon: 'icon-shujufenxi',
            name: '数据分析',
            desc: '智能分析业务数据',
            traditionalDesc: '数据分析能力弱，缺乏决策支持'
          }
        ]
      });
      console.log('已重新初始化allFunctions数据');
    }
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll: function(e) {
    // 如果滚动位置超过一定值（如100px），认为已经离开首屏
    const isAtFirstScreen = e.scrollTop < 100;
    
    // 只有当状态需要变化时才更新，减少不必要的setData
    if (isAtFirstScreen !== this.data.isAtFirstScreen) {
      this.setData({
        isAtFirstScreen: isAtFirstScreen
      });
    }
  },

  /**
   * 处理顶部导航切换
   */
  handleTabChange: function(e) {
    navService.handleNavBarTabChange(e);
  },

  /**
   * 触摸开始事件处理
   */
  onTouchStart: function(e) {
    this.setData({
      touchStartY: e.changedTouches[0].clientY,
      touchStartX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY,
      touchMoveX: e.changedTouches[0].clientX
    });
  },

  /**
   * 触摸移动事件处理
   */
  onTouchMove: function(e) {
    if (this.data.isAnimating) return;

    this.setData({
      touchMoveX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY
    });
  },

  /**
   * 触摸结束事件处理
   */
  onTouchEnd: function(e) {
    const { 
      touchStartX, 
      touchMoveX,
      touchStartY,
      touchMoveY,
      isAnimating,
      swipeThreshold,
      lastSwipeTime,
      productKey,
      isAtFirstScreen
    } = this.data;
    
    if (isAnimating) return;
    
    const moveX = touchStartX - touchMoveX;
    const moveY = touchStartY - touchMoveY;
    const now = Date.now();
    
    if (now - lastSwipeTime < 300) return;
    this.setData({ lastSwipeTime: now });
    
    // 只有在首屏且为水平滑动时才允许切换页面
    if (isAtFirstScreen && Math.abs(moveX) > Math.abs(moveY) && Math.abs(moveX) > swipeThreshold) {
      const direction = moveX > 0 ? 'left' : 'right';
      this.setData({ isAnimating: true });
      
      // 使用导航服务切换产品页面
      navService.switchProductPage(productKey, direction);
      
      // 动画结束后重置状态
      setTimeout(() => {
        this.setData({ isAnimating: false });
      }, 350);
    }
  },

  /**
   * 处理来自promo-card组件的跳转请求
   */
  handleJoinPromo: function(e) {
    const { productKey } = e.detail;
    console.log(`[pdtonline page] 接收到 joinpromo 事件, productKey: ${productKey}`);
    if (productKey) {
      navService.navigateToVersionPage(productKey);
    }
  },

  /**
   * 导航到更多页面
   */
  navigateToMore: function() {
    navService.navigateToProductService(this.data.productKey, 'consult');
  },

  /**
   * 导航到版本页面
   */
  navigateToVersionhkj: function(e) {
    navService.navigateToVersionPage('tonline');
  },

  /**
   * 打电话
   */
  makePhoneCall: function() {
    navService.makePhoneCall();
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'T+Online - 企业级云ERP解决方案',
      path: '/pages/pdtonline/pdtonline',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: 'T+Online - 企业级云ERP解决方案',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },
  
  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  },

  /**
   * 查看更多功能对比
   */
  showMoreFunctions: function() {
    this.setData({
      isMoreFunctionsVisible: true
    });
  },
  
  /**
   * 查询详情或体验功能
   */
  handleFinanceAction: function(e) {
    const { action } = e.currentTarget.dataset;
    
    if (action === 'consult') {
      navService.navigateToProductService(this.data.productKey, 'consult');
    } else if (action === 'experience') {
      navService.navigateToVersionPage('tonline');
    }
  }
}); 