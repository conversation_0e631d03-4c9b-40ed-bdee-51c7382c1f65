/**
 * 好会计产品介绍页面
 * 支持左右滑动切换页面、上下滑动查看详情
 */
const navService = require('../../utils/navigator.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品海报信息
    posterTitle: '好会计',
    posterSubtitle: '智能云财务软件',
    posterSlogan: '票 财 税 档一体化管理',
    
    // 触摸相关数据
    touchStartY: 0,
    touchMoveY: 0,
    touchStartX: 0,
    touchMoveX: 0,
    isAnimating: false,
    lastSwipeTime: 0,
    swipeThreshold: 50,
    
    // 页面配置
    activeTab: 0,
    productKey: 'hkj',
    isAtFirstScreen: true,
    
    // 生态互联项目数据
    ecoItems: [
      { title: '员工', icon: 'icon-user', items: ['在线报销', '在线开票', '薪资个税'], isExpanded: false },
      { title: '老板', icon: 'icon-gongwenbao', items: ['查看报表', '消息推送', '报销审批', '经营决策'], isExpanded: false },
      { title: '库管', icon: 'icon-cangchucangku', items: ['出入库、退换货', '物料清单等管理'], isExpanded: false },
      { title: '财务', icon: 'icon-renminbi', items: ['软件自动做账、会计审核', '自动生成税表', '资金健康分析、税务风险自查', '四大财务报表、多类管理报表'], isExpanded: true },
      { title: '税局', icon: 'icon-shuzihua', items: ['在好会计软件内：', '可一键提取发票', '获取最新财税政策', '更新财税报表'], isExpanded: false }
    ],
    
    // 服务项目数据
    serviceItems: [
      { icon: 'icon-hetong', name: '发票管理', desc: '从开票、取票、识票、查验、归档全流程支持发票电子化' },
      { icon: 'icon-shuzihua', name: '智能财务', desc: '核算智能化、账务自动化' },
      { icon: 'icon-renminbi', name: '资金管理', desc: '银行流水、对账单自动生凭证' },
      { icon: 'icon-fuwuqi', name: '资产管理', desc: '固定/无形资产、待摊费用全生命周期管理' },
      { icon: 'icon-zhinengheyue', name: '工资管理', desc: '算薪算税报个税、4步轻松搞定工资个税计算' },
      { icon: 'icon-cangchucangku', name: '库存核算', desc: '以票控税、精准核算库存成本' },
      { icon: 'icon-piaoju', name: '报销管理', desc: '小额报销、让您实现无纸化、异地报销' },
      { icon: 'icon-zhengfu', name: '智能税务', desc: '税表自动生成、智能算税' }
    ],
    
    // 服务保障数据
    serviceGuarantees: [
      {
        icon: 'icon-gongyi',
        title: '指导导账',
        desc: '专业团队迁移账套，调试到位'
      },
      {
        icon: 'icon-icon-yixueshiwu',
        title: '免费培训',
        desc: '一对一培训，让您轻松上手'
      },
      {
        icon: 'icon-zhuanyepeixunfuwu',
        title: '7×24小时售后',
        desc: '专业团队随时待命，解决您的问题'
      },
      {
        icon: 'icon-banbenshengji-05',
        title: '终身升级',
        desc: '持续更新迭代，功能不断完善'
      }
    ],
    ecoDescriptionVisible: false,
    featureAnimationTriggered: false,
    testimonialAnimationTriggered: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({ 
      activeTab: 0
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll: function(e) {
    const scrollTop = e.scrollTop;
    // 如果滚动位置超过一定值（如100px），认为已经离开首屏
    const isAtFirstScreen = scrollTop < 100;
    
    // 只有当状态需要变化时才更新，减少不必要的setData
    if (isAtFirstScreen !== this.data.isAtFirstScreen) {
      this.setData({
        isAtFirstScreen: isAtFirstScreen
      });
    }
  },

  /**
   * 处理顶部导航切换
   */
  handleTabChange: function(e) {
    navService.handleNavBarTabChange(e);
  },

  /**
   * 触摸开始事件处理
   */
  onTouchStart: function(e) {
    this.setData({
      touchStartY: e.changedTouches[0].clientY,
      touchStartX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY,
      touchMoveX: e.changedTouches[0].clientX
    });
  },

  /**
   * 触摸移动事件处理
   */
  onTouchMove: function(e) {
    if (this.data.isAnimating) return;
    
    this.setData({
      touchMoveX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY
    });
  },

  /**
   * 触摸结束事件处理
   */
  onTouchEnd: function(e) {
    const { 
      touchStartX, 
      touchMoveX,
      touchStartY,
      touchMoveY,
      isAnimating,
      swipeThreshold,
      lastSwipeTime,
      productKey,
      isAtFirstScreen
    } = this.data;
    
    if (isAnimating) return;
    
    const moveX = touchStartX - touchMoveX;
    const moveY = touchStartY - touchMoveY;
    const now = Date.now();
    
    if (now - lastSwipeTime < 300) return;
    this.setData({ lastSwipeTime: now });
    
    // 只有在首屏且为水平滑动时才允许切换页面
    if (isAtFirstScreen && Math.abs(moveX) > Math.abs(moveY) && Math.abs(moveX) > swipeThreshold) {
      const direction = moveX > 0 ? 'left' : 'right';
      this.setData({ isAnimating: true });
      
      // 使用导航服务切换产品页面
      navService.switchProductPage(productKey, direction);
      
      // 动画结束后重置状态
      setTimeout(() => {
        this.setData({ isAnimating: false });
      }, 350);
    }
  },

  /**
   * 打电话
   */
  makePhoneCall: function() {
    navService.makePhoneCall();
  },

  /**
   * 处理来自promo-card组件的跳转请求
   */
  handleJoinPromo: function(e) {
    const { productKey } = e.detail;
    console.log(`[pdhkj page] 接收到 joinpromo 事件, productKey: ${productKey}`);
    if (productKey) {
      navService.navigateToVersionPage(productKey);
    }
  },

  /**
   * 切换生态项目展开状态
   */
  toggleEcoItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const ecoItems = this.data.ecoItems;
    
    // 关闭其他项，只保留当前点击项展开
    ecoItems.forEach((item, i) => {
      item.isExpanded = (i === index) ? !item.isExpanded : false;
    });
    
    this.setData({ ecoItems });
  },

  /**
   * 导航到易代账页面
   */
  navToYdz: function() {
    if (this.data.isAnimating) return;
    
    this.setData({
      isAnimating: true
    });
    navService.navigateToProduct('ydz', 'slide-in-right');
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 350);
  },

  /**
   * 导航到T+Online页面
   */
  navToTOnline: function() {
    if (this.data.isAnimating) return;
    
    this.setData({
      isAnimating: true
    });
    navService.navigateToProduct('tonline', 'slide-in-right');
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 350);
  },

  /**
   * 导航到好业财页面
   */
  navToHyc: function() {
    if (this.data.isAnimating) return;
    
    this.setData({
      isAnimating: true
    });
    navService.navigateToProduct('hyc', 'slide-in-right');
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 350);
  },

  /**
   * 导航到好生意页面
   */
  navToHsy: function() {
    if (this.data.isAnimating) return;
    
    this.setData({
      isAnimating: true
    });
    navService.navigateToProduct('hsy', 'slide-in-right');
    setTimeout(() => {
      this.setData({
        isAnimating: false
      });
    }, 350);
  },

  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  },
  
  /**
   * 分享配置
   */
  onShareAppMessage: function () {
    return {
      title: '好会计 - 智能财务管理软件',
      path: '/pages/pdhkj/pdhkj',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },
  
  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '好会计 - 智能财务管理软件',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  }
}); 