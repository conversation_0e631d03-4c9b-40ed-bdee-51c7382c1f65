<template>
  <div class="asset-change-compare">
    <!-- 资产变更对比组件 - 支持只读和编辑模式的变更对比组件，引用其他子组件实现左右对比功能 -->
    
    <div class="compare-container">
      <!-- 左侧面板（原始数据） -->
      <div class="compare-panel original-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <el-tag type="info" size="large">变更前</el-tag>
          </h3>
        </div>
        
        <div class="panel-content">
          <!-- 资产表单表头 -->
          <AssetFormHeader
            :formData="originalData"
            :readonly="true"
            :enterpriseOptions="enterpriseOptions"
            :userOptions="userOptions"
          />
          
          <!-- 产品详情 -->
          <AssetProductDetail
            :formData="originalData"
            :readonly="true"
            :productOptions="productOptions"
          />
          
          <!-- 激活信息 -->
          <AssetActivationInfo
            :formData="originalData"
            :readonly="true"
          />
        </div>
      </div>
      
      <!-- 中间分隔线 -->
      <div class="compare-divider">
        <div class="divider-line"></div>
      </div>
      
      <!-- 右侧面板（变更后数据） -->
      <div class="compare-panel changed-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <el-tag 
              :type="editMode ? 'warning' : 'success'" 
              size="large"
            >
              {{ editMode ? '变更后（编辑中）' : '变更后' }}
            </el-tag>
          </h3>
        </div>
        
        <div class="panel-content">
          <!-- 资产表单表头 -->
          <AssetFormHeader
            :formData="changedData"
            :readonly="!editMode"
            :enterpriseOptions="enterpriseOptions"
            :userOptions="userOptions"
            @enterprise-change="handleEnterpriseChange"
            @user-change="handleUserChange"
            ref="headerFormRef"
          />
          
          <!-- 产品详情 -->
          <AssetProductDetail
            :formData="changedData"
            :readonly="!editMode"
            :productOptions="productOptions"
            @product-change="handleProductChange"
            @data-change="handleDataChange"
            ref="productFormRef"
          />
          
          <!-- 激活信息 -->
          <AssetActivationInfo
            :formData="changedData"
            :readonly="!editMode"
            @data-change="handleDataChange"
            ref="activationFormRef"
          />
        </div>
      </div>
    </div>
    

  </div>
</template>

<script setup>
import { ref } from 'vue'

// 导入子组件
import AssetFormHeader from './AssetFormHeader.vue'
import AssetProductDetail from './AssetProductDetail.vue'
import AssetActivationInfo from './AssetActivationInfo.vue'

// Props定义
const props = defineProps({
  // 原始资产数据
  originalData: {
    type: Object,
    required: true
  },
  // 变更后的资产数据
  changedData: {
    type: Object,
    required: true
  },
  // 是否编辑模式
  editMode: {
    type: Boolean,
    default: false
  },
  // 企业选项列表
  enterpriseOptions: {
    type: Array,
    default: () => []
  },
  // 用户选项列表
  userOptions: {
    type: Array,
    default: () => []
  },
  // 产品选项列表
  productOptions: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['data-change'])

// 表单引用
const headerFormRef = ref(null)
const productFormRef = ref(null)
const activationFormRef = ref(null)



// 企业变更处理
const handleEnterpriseChange = (enterpriseId) => {
  handleDataChange()
}

// 用户变更处理
const handleUserChange = (userId) => {
  handleDataChange()
}

// 产品变更处理
const handleProductChange = (productId) => {
  handleDataChange()
}

// 数据变更处理
const handleDataChange = () => {
  emit('data-change', props.changedData)
}

// 表单验证方法
const validate = async () => {
  try {
    // 验证所有子表单
    const headerValid = await headerFormRef.value?.validate()
    const productValid = await productFormRef.value?.validate()
    const activationValid = await activationFormRef.value?.validate()
    
    return headerValid && productValid && activationValid
  } catch (error) {
    return false
  }
}

// 重置表单验证
const resetValidation = () => {
  headerFormRef.value?.resetValidation()
  productFormRef.value?.resetValidation()
  activationFormRef.value?.resetValidation()
}



// 暴露方法给父组件
defineExpose({
  validate,
  resetValidation
})
</script>

<style scoped>
.asset-change-compare {
  margin-bottom: 30px;
}

.compare-container {
  display: flex;
  gap: 24px; /* 增加面板间距 */
  margin-bottom: 20px;
  /* 移除固定最小高度，让内容自适应 */
  min-height: 0; /* 确保flex子项可以收缩 */
}

.compare-panel {
  flex: 1;
  min-width: 0; /* 允许flex子项收缩 */
  border-radius: 8px;
  overflow: visible; /* 改为可见，避免滚动条 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.original-panel {
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

.changed-panel {
  background-color: #fff;
  border-color: #409eff;
}

.changed-panel.edit-mode {
  border-color: #e6a23c;
  box-shadow: 0 4px 20px rgba(230, 162, 60, 0.2);
}

.panel-header {
  padding: 16px;
  background: linear-gradient(135deg, #ecf5ff 0%, #e1f3d8 100%);
  border-bottom: 1px solid #e4e7ed;
  position: relative;
}

.panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-content {
  padding: 20px; /* 适当的内边距 */
  /* 移除最大高度限制和滚动条，让内容自然展开 */
  overflow: visible; /* 确保内容可见 */
}

/* 优化表单间距 */
.panel-content .el-form-item {
  margin-bottom: 16px; /* 合适的表单项间距 */
}

.panel-content .form-section {
  margin-bottom: 20px; /* 合适的区块间距 */
}

/* 确保子组件不会超出面板宽度 */
.panel-content > * {
  max-width: 100%;
  overflow: hidden;
}

/* 移除了自定义滚动条样式，因为不再需要滚动 */

.compare-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  position: relative;
  background: linear-gradient(180deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  margin: 0 10px;
}

.divider-line {
  position: absolute;
  top: 20px;
  bottom: 20px;
  left: 50%;
  width: 3px;
  background: linear-gradient(180deg, #409eff, #67c23a);
  transform: translateX(-50%);
  border-radius: 2px;
}



.changed-panel :deep(.el-form-item.changed-field .el-form-item__label) {
  color: #e6a23c;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .compare-container {
    flex-direction: column;
    gap: 20px;
  }

  .compare-divider {
    width: 100%;
    height: 60px;
    flex-direction: row;
    margin: 10px 0;
  }

  .divider-line {
    top: 50%;
    left: 20px;
    right: 20px;
    width: auto;
    height: 3px;
    transform: translateY(-50%);
    background: linear-gradient(90deg, #409eff, #67c23a);
  }
}

@media (max-width: 768px) {
  .panel-content {
    padding: 15px;
  }

  .compare-divider {
    height: 40px;
  }
}
</style>
