// /frontend/src/api/pricing.js
import service from '@/utils/request_extra.js';

const API_PATH = '/pricing';

/**
 * 调用后端定价服务计算产品价格
 * @param {Object} params - 计算参数
 * @param {string|number} params.product_id - 产品ID
 * @param {number} params.user_count - 使用人数
 * @param {number} params.account_count - 账套数
 * @param {number[]} params.selected_features - 选中的功能ID列表
 * @param {number} params.duration_months - 购买时长（月）
 * @returns {Promise<number>} 返回计算出的标准价格
 */
export const calculateProductPrice = (params) => {
  return service.post(`${API_PATH}/calculate`, params);
};

/**
 * 批量计算多个产品配置的价格
 * @param {Array} products - 产品配置列表
 * @returns {Promise<Array>} 返回价格计算结果列表
 */
export const batchCalculatePrice = (products) => {
  return service.post(`${API_PATH}/batch-calculate`, { products });
};
