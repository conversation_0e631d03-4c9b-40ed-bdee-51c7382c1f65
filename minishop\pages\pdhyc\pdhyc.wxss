/*
 * @pdhyc.wxss
 * 重构和重新排序以匹配 pdhyc.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
@import '/static/fonts/iconfont.wxss';

page {
  margin: 0;
  padding: 0;
  border: none;
  background-color: transparent;
}

.container {
  width: 100vw;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  left: 0;
  right: 0;
  top: 0;
}

.full-width-nav {
  width: 100vw !important;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
  border: none;
}

/* 全局元素边框消除 */
view,
scroll-view,
text,
image,
button {
  box-sizing: border-box;
  border: none;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100vw; /* 使用视口宽度单位 */
  height: 66vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #5e35b1 0%, #3949ab 60%, #1e88e5 100%);
  color: #fff;
  margin: 0;
  padding: 0;
  border: none;
  left: 0;
  right: 0;
  top: 0;
}

/* --- 背景动效 --- */
.poster-background {} /* No styles in original, but selector exists in WXML */

.bg-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: 50rpx 50rpx;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  opacity: 0.4;
  transform: perspective(500rpx) rotateX(60deg) scale(2);
  transform-origin: center bottom;
  z-index: 0;
  border: none;
}

.connection-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  opacity: 0.4;
  z-index: 1;
  transform-origin: left center;
  border: none;
}

.line-1 { width: 30%; top: 15%; left: 20%; transform: rotate(15deg); }
.line-2 { width: 25%; top: 35%; left: 60%; transform: rotate(-20deg); }
.line-3 { width: 28%; top: 55%; left: 15%; transform: rotate(25deg); }
.line-4 { width: 35%; top: 75%; left: 65%; transform: rotate(-15deg); }

/* Note: .bg-element and its variants are in WXML but not in the provided WXSS */

.particles-container {} /* No styles in original, but selector exists in WXML */

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  z-index: 2;
  border: none;
}

.p-1 { top: 10%; left: 20%; animation: particleRotate 40s linear infinite; }
.p-2 { top: 15%; left: 85%; animation: particleRotate 45s linear infinite reverse; }
.p-3 { top: 25%; left: 40%; animation: particleRotate 50s linear infinite; }
.p-4 { top: 30%; left: 10%; animation: particleRotate 55s linear infinite reverse; }
.p-5 { top: 35%; left: 60%; animation: particleRotate 48s linear infinite; }
.p-6 { top: 40%; left: 80%; animation: particleRotate 52s linear infinite reverse; }
.p-7 { top: 45%; left: 25%; animation: particleRotate 46s linear infinite; }
.p-8 { top: 50%; left: 90%; animation: particleRotate 54s linear infinite reverse; }

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 100rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 20%, #7dd3fc 80%);
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 6rpx 24rpx rgba(0, 64, 255, 0.18);
  margin-bottom: 18rpx;
  animation: fadeInUp 1s;
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #38bdf8 0%, #fff 100%);
  border-radius: 4rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(56, 189, 248, 0.5);
  animation: fadeIn 1.5s;
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  animation: fadeInUp 1.2s;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
  animation: fadeInUp 1.4s;
}

/* --- 促销卡片 --- */
.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 90%;
  max-width: 650rpx;
}

/* --- 底部羽化 --- */
/* This is implemented on .poster::after. The .poster-bottom-fade view is unused. */
.poster::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 85%, #ffffff 100%);
  z-index: 2;
  pointer-events: none;
}

/* ==================================
   详情内容区域
   ================================== */
.detail-content {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  padding: 40rpx 30rpx 0;
  margin-top: -80rpx;
  z-index: 10;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
}

/* --- 主标题区域 --- */
.hero-banner {
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
}

.hero-content {
  text-align: center;
}

.hero-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #0052d9;
  margin-bottom: 20rpx;
}

.divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  margin: 20rpx auto;
  border-radius: 3rpx;
}

.hero-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 0 20rpx;
}

/* --- 价值主张区域 --- */
.value-proposition {
  padding: 10rpx 0rpx 40rpx;
  background: #fff;
  border-radius: 20rpx;
}

.roles-showcase {
  /* No direct styles */
}

.roles-scroll {
  white-space: nowrap;
  padding: 20rpx 0;
}

.role-card {
  display: inline-block;
  width: 330rpx;
  background: #f9fafc;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  vertical-align: top;
  transition: all 0.3s ease;
  text-align: center;
}

.role-card:active {
  transform: scale(0.98);
}

.role-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 60rpx;
  margin: 0 auto 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.role-avatar .iconfont {
  font-size: 50rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  line-height: 1;
}

.role-avatar.boss { background-color: #0052d9; }
.role-avatar.finance { background-color: #00b578; }
.role-avatar.warehouse { background-color: #fa8c16; }
.role-avatar.salesman { background-color: #722ed1; }
.role-avatar.project { background-color: #eb2f96; }

.role-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
}

.role-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  white-space: normal;
  height: 150rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  text-align: center;
}

/* --- 一套软件搞定 --- */
.all-in-one {
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  margin: 30rpx 0rpx;
  border-radius: 20rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 82, 217, 0.2);
  position: relative;
  overflow: hidden;
  text-align: center;
  color: #fff;
}

.all-in-one::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(255,255,255,0.2), transparent 70%);
  z-index: 0;
}

.all-in-one-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

/* The .all-in-one-subtitle class is not used in the WXML */

/* --- 功能模块展示 --- */
.modules-showcase {
  padding: 40rpx 0rpx;
  background: #fff;
  margin: 30rpx 10rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

/* .modules-title and .modules-subtitle are not in WXML */

.modules-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 30rpx;
}

.module-item {
  background: #f9fafc;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.module-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}

.module-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 auto 20rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 82, 217, 0.1);
}

.module-icon .iconfont {
  font-size: 40rpx;
  color: #0052d9;
}

.module-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.module-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* --- 功能区块 (业财融合, 进销存等) --- */
.feature-section {
  padding: 50rpx 10rpx;
  background: #fff;
  margin: 30rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-section:active {
  transform: scale(0.98);
}

.feature-section::before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(0, 82, 217, 0.03);
  z-index: 0;
}

.feature-section::after {
  content: '';
  position: absolute;
  bottom: -150rpx;
  left: -150rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: rgba(0, 82, 217, 0.03);
  z-index: 0;
}

.feature-header {
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.section-icon {
  width: 8rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, #0052d9, #3a90ff);
  border-radius: 4rpx;
  margin-right: 20rpx;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.2);
}

.section-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  letter-spacing: 1rpx;
}

.section-desc {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
  margin-left: 32rpx;
  opacity: 0.85;
  font-weight: 500;
}

.feature-content {
  display: flex;
  margin-top: 30rpx;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.feature-list {
  margin-bottom: 40rpx;
  position: relative;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-item:active {
  transform: translateX(5rpx);
  background-color: rgba(0, 82, 217, 0.02);
}

.feature-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3rpx;
  background: linear-gradient(to bottom, #0052d9, #3a90ff);
  opacity: 0.6;
}

.feature-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  margin-right: 20rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
  box-shadow: 0 2rpx 5rpx rgba(0, 82, 217, 0.3);
  position: relative;
}

.feature-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: white;
  opacity: 0.8;
}

.feature-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.feature-image {
  width: 100%;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 15rpx 25rpx rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.feature-image:active {
  transform: translateY(-5rpx);
  box-shadow: 0 20rpx 30rpx rgba(0, 0, 0, 0.15);
}

.integration { background: linear-gradient(135deg, #f0f5ff, #ffffff); }
.integration .feature-item { border-left: 3rpx solid rgba(0, 82, 217, 0.3); padding-left: 25rpx; }
.integration .feature-item::before { display: none; }
.supply-chain { background: linear-gradient(135deg, #e6f7ff, #ffffff); }
.supply-chain .feature-item { border-bottom: 2rpx solid rgba(0, 82, 217, 0.1); margin-bottom: 15rpx; padding-bottom: 25rpx; }
.supply-chain .feature-item:last-child { border-bottom: none; }
.supply-chain .feature-dot { background: linear-gradient(135deg, #0052d9, #36cfc9); }
.online-store { background: linear-gradient(135deg, #f6ffed, #ffffff); }
.online-store .feature-item { padding-left: 30rpx; border-radius: 10rpx; }
.online-store .feature-dot { background: linear-gradient(135deg, #52c41a, #73d13d); }
.project-contract { background: linear-gradient(135deg, #fff2e8, #ffffff); }
.project-contract .feature-item { border-radius: 10rpx 30rpx 30rpx 10rpx; }
.project-contract .feature-dot { background: linear-gradient(135deg, #fa8c16, #ffd591); }
.expense-control { background: linear-gradient(135deg, #fcffe6, #ffffff); }
.expense-control .feature-item { border-radius: 0 20rpx 20rpx 0; padding-left: 30rpx; }
.expense-control .feature-dot { background: linear-gradient(135deg, #bae637, #73d13d); }
.retail-chain { background: linear-gradient(135deg, #e6fffb, #ffffff); }
.retail-chain .feature-item { margin-left: 10rpx; padding-left: 25rpx; }
.retail-chain .feature-dot { background: linear-gradient(135deg, #13c2c2, #36cfc9); }

@media screen and (min-width: 768px) {
  .feature-content { flex-direction: row; align-items: flex-start; }
  .feature-content.reverse { flex-direction: row-reverse; }
  .feature-list { width: 50%; margin-bottom: 0; padding-right: 30rpx; }
  .feature-content.reverse .feature-list { padding-right: 0; padding-left: 30rpx; }
  .feature-image { width: 48%; align-self: center; }
  .feature-section:hover { transform: translateY(-5rpx); box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1); }
}

/* --- 行业解决方案 --- */
.industry-solutions {
  padding: 40rpx 0rpx;
  background: #fff;
  margin: 30rpx 10rpx;
  border-radius: 20rpx;
  /* box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05); */
}

.industry-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.industry-title::after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0052d9, #3a90ff);
  border-radius: 2rpx;
}

.industry-tabs-container {
  position: relative;
  margin: 40rpx 0 30rpx;
  overflow: hidden;
}

.industry-tabs-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 10rpx 0;
}

.industry-tabs {
  display: inline-flex;
  padding: 6rpx;
  background: rgba(0, 82, 217, 0.06);
  border-radius: 16rpx;
  box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.02);
}

.industry-tab {
  flex-shrink: 0;
  text-align: center;
  padding: 16rpx 28rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin: 0 4rpx;
}

.industry-tab.active {
  color: #fff;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  font-weight: 500;
  box-shadow: 0 6rpx 12rpx rgba(0, 82, 217, 0.2);
}

.industry-detail {
  background: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease;
  border: 1px solid rgba(0, 82, 217, 0.08);
}

.industry-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.industry-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #0052d9, #3a90ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.industry-badge {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  position: relative;
  opacity: 0.8;
}

.industry-badge:before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border-radius: 50%;
  background: rgba(0, 82, 217, 0.2);
  animation: pulse 2s infinite;
}

.industry-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  line-height: 1.6;
}

.industry-section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin: 30rpx 0 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.industry-section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 26rpx;
  background: linear-gradient(to bottom, #0052d9, #3a90ff);
  border-radius: 3rpx;
}

.industry-challenges {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}

.challenge-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
  background: rgba(0, 82, 217, 0.04);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  max-width: 100%;
  box-sizing: border-box;
}

.check-icon {
  width: 26rpx;
  height: 26rpx;
  background: rgba(0, 82, 217, 0.1);
  border-radius: 50%;
  margin-right: 10rpx;
  position: relative;
  flex-shrink: 0;
}

.check-icon::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 6rpx;
  border-left: 2rpx solid #0052d9;
  border-bottom: 2rpx solid #0052d9;
  transform: rotate(-45deg);
  top: 8rpx;
  left: 6rpx;
}

.challenge-item text {
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
}

.industry-solution-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  padding: 0 10rpx;
  text-align: justify;
}

.industry-action {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.consult-btn {
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(0, 82, 217, 0.2);
  transition: all 0.3s;
}

.consult-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 82, 217, 0.2);
}

/* --- 选择理由 --- */
.choose-reason {
  padding: 40rpx 0rpx;
  background: #fff;
  margin: 30rpx 10rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.reason-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
  position: relative;
}

.reason-title::after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0052d9, #3a90ff);
  border-radius: 2rpx;
}

.reason-cards {
  display: flex;
  flex-direction: column;
}

.reason-card {
  background: #f9fafc;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.reason-card:last-child {
  margin-bottom: 0;
}

.reason-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}

.reason-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.reason-icon .iconfont {
  font-size: 40rpx;
  color: #fff;
}

.reason-icon.brand { background: linear-gradient(135deg, #0052d9, #3a90ff); }
.reason-icon.savings { background: linear-gradient(135deg, #00b578, #52c41a); }
.reason-icon.service { background: linear-gradient(135deg, #fa8c16, #faad14); }
.reason-icon.security { background: linear-gradient(135deg, #722ed1, #eb2f96); }

.reason-content {
  flex: 1;
}

.reason-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.reason-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* --- FAQ部分 --- */
.faq-section {
  padding: 40rpx 30rpx;
  background: #fff;
  margin: 30rpx 10rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.faq-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
  position: relative;
}

.faq-title::after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #0052d9, #3a90ff);
  border-radius: 2rpx;
}

.faq-item {
  border-bottom: 1rpx solid #eee;
  padding: 30rpx 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.arrow {
  width: 30rpx;
  height: 30rpx;
  position: relative;
  transition: all 0.3s ease;
}

.arrow.down::before, .arrow.down::after,
.arrow.up::before, .arrow.up::after {
  content: '';
  position: absolute;
  width: 15rpx;
  height: 3rpx;
  background-color: #999;
  top: 50%;
}

.arrow.down::before { left: 4rpx; transform: rotate(45deg); }
.arrow.down::after { right: 4rpx; transform: rotate(-45deg); }
.arrow.up::before { left: 4rpx; transform: rotate(-45deg); }
.arrow.up::after { right: 4rpx; transform: rotate(45deg); }

.faq-answer {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 0 20rpx;
}

/* --- 体验咨询区域 --- */
.cta-section {
  margin: 30rpx 10rpx;
  margin-bottom: 100rpx;
  padding: 0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #0052d9, #3a90ff);
  box-shadow: 0 15rpx 40rpx rgba(0, 82, 217, 0.3);
  overflow: hidden;
  position: relative;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(255,255,255,0.15), rgba(255,255,255,0));
  opacity: 0.8;
}

.cta-content {
  padding: 50rpx 40rpx;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 15rpx;
  text-align: center;
}

.cta-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40rpx;
  text-align: center;
}

.cta-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;
}

.cta-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  margin: 0 15rpx;
}

.cta-btn::after {
  border: none;
}

.cta-btn.outline {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.cta-btn.primary {
  background: #fff;
  color: #0052d9;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.cta-btn.primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.5), rgba(255,255,255,0));
  transform: skewX(-25deg);
  animation: shine 3s infinite;
  z-index: 1;
}

.btn-text {
  position: relative;
  z-index: 2;
}

/* ==================================
   动画
   ================================== */
@keyframes particleRotate {
  from { transform: rotate(0deg) translateX(100rpx) rotate(0deg); }
  to { transform: rotate(360deg) translateX(100rpx) rotate(-360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.5); opacity: 0.3; }
  100% { transform: scale(1); opacity: 0.7; }
}

@keyframes shine {
  0% { left: -150%; }
  100% { left: 150%; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} 