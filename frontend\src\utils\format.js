/**
 * 格式化日期时间字符串
 * @param {string | Date} isoString - 符合 ISO 8601 格式的日期字符串或 Date 对象
 * @returns {string} 格式化后的字符串，如 "2023/8/8 08:30:05"
 */
export const formatDateTime = (isoString) => {
  if (!isoString) return '';
  const d = new Date(isoString);

  // 检查日期是否有效
  if (isNaN(d.getTime())) {
    return '无效日期';
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化日期字符串
 * @param {string | Date} isoString - 符合 ISO 8601 格式的日期字符串或 Date 对象
 * @returns {string} 格式化后的字符串，如 "2023/08/08"
 */
export const formatDate = (isoString) => {
  if (!isoString) return '';
  const d = new Date(isoString);

  if (isNaN(d.getTime())) {
    return '无效日期';
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');

  return `${year}/${month}/${day}`;
};

/**
 * 获取当前时间的数据库格式字符串（本地时间）
 * @returns {string} 格式化后的字符串，如 "2023-08-08 14:30:25"
 */
export const getCurrentDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 将日期时间字符串转换为本地时间格式（用于表单显示）
 * @param {string} dateTimeStr - 数据库中的日期时间字符串
 * @returns {string} 本地时间格式的字符串
 */
export const toLocalDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  // 如果已经是本地格式，直接返回
  if (typeof dateTimeStr === 'string' && dateTimeStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    return dateTimeStr;
  }

  const date = new Date(dateTimeStr);
  if (isNaN(date.getTime())) {
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};