//中央状态管理器 (store/auth.js)：这是我们前端的“大脑”，
// 负责管理用户的登录状态、存储令牌，并能在浏览器刷新后恢复登录状态。


import { reactive, readonly } from 'vue';
import { loginEmployee as apiLoginEmployee } from '@/api/auth.js';
import router from '@/router'; // 引入路由实例用于跳转

// 这是一个简单的、响应式的全局状态存储
// 我们在这里管理用户的登录状态、Token和个人信息
const state = reactive({
  isAuthenticated: false,
  user: null,
  token: null,
});

/**
 * 登录方法
 * @param {object} credentials - 包含工号和密码的对象
 */
async function login(credentials) {
  try {
    const response = await apiLoginEmployee(credentials);
    // 从后端响应中获取token和用户信息
    const { token, user } = response;

    // 更新状态
    state.token = token;
    state.user = user;
    state.isAuthenticated = true;

    // [重要] 将Token存储到浏览器的localStorage中，以便在刷新页面后保持登录状态
    localStorage.setItem('authToken', token);
    localStorage.setItem('authUser', JSON.stringify(user));

    // 登录成功后跳转到主页
    router.push('/');

  } catch (error) {
    // 登录失败时，确保状态是清晰的
    state.isAuthenticated = false;
    state.user = null;
    state.token = null;
    console.error('登录失败:', error);
    // 抛出错误，让调用方（例如登录页面）可以捕获并显示提示
    throw error;
  }
}

/**
 * 登出方法
 */
function logout() {
  // 清空状态
  state.isAuthenticated = false;
  state.user = null;
  state.token = null;

  // [重要] 从localStorage中移除Token
  localStorage.removeItem('authToken');
  localStorage.removeItem('authUser');
  
  // 登出后跳转到登录页面
  router.push('/login');
}

/**
 * 应用初始化时调用的方法
 * 它的作用是检查localStorage中是否已存在有效的Token
 */
function initAuthStore() {
  const token = localStorage.getItem('authToken');
  const user = localStorage.getItem('authUser');

  if (token && user) {
    state.token = token;
    state.user = JSON.parse(user);
    state.isAuthenticated = true;
  }
}

// 导出我们的状态和方法
// 使用 readonly(state) 可以防止外部组件直接修改状态，必须通过我们提供的 login/logout 方法
export const useAuth = () => {
  return {
    state: readonly(state),
    login,
    logout,
    initAuthStore,
  };
}; 