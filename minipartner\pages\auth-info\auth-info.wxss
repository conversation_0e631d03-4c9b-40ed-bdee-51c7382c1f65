/* 认证信息页面样式 */
.auth-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 30rpx;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666666;
}

/* 认证状态卡片 */
.status-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  align-self: flex-start;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1rpx solid #ffeaa7;
}

.status-approved {
  background: #d4edda;
  color: #155724;
  border: 1rpx solid #c3e6cb;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1rpx solid #f5c6cb;
}

.status-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 表单容器 */
.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 查看模式 */
.info-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 25rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  min-width: 160rpx;
}

.info-value {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
}

.info-empty {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #999999;
}

.copy-btn {
  padding: 8rpx 16rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 22rpx;
  margin-left: 20rpx;
}

.copy-btn:active {
  background: #5a6fd8;
}

/* 编辑模式 */
.form-section {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.input:focus {
  border-color: #667eea;
  background: #ffffff;
}

/* 按钮区域 */
.button-section {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.edit-btn, .save-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.cancel-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  color: #666666;
  border: 2rpx solid #e0e0e0;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn:active, .save-btn:not(.disabled):active {
  transform: scale(0.98);
}

.cancel-btn:active {
  background: #f8f9fa;
}

/* 温馨提示卡片 */
.tips-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
}

.tip-dot {
  color: #667eea;
  font-weight: bold;
  margin-top: 2rpx;
}

.tip-text {
  flex: 1;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}
