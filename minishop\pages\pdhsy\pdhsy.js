/**
 * 好生意产品介绍页面
 * 支持左右滑动切换页面、上下滑动查看详情
 */
const app = getApp()
const navService = require('../../utils/navigator.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品海报信息
    posterTitle: '好生意',
    posterSubtitle: '专为商贸企业量身打造',
    posterSlogan: '智能生意系统',
    touchStartY: 0,
    touchMoveY: 0,
    touchStartX: 0,
    touchMoveX: 0,
    isAnimating: false,
    lastSwipeTime: 0,
    swipeThreshold: 50,
    activeTab: 4,
    productKey: 'hsy',
    // 添加首屏判断标记
    isAtFirstScreen: true,
    animationData: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({ activeTab: 4 });
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll: function(e) {
    // 如果滚动位置超过一定值（如100px），认为已经离开首屏
    const isAtFirstScreen = e.scrollTop < 100;
    
    // 只有当状态需要变化时才更新，减少不必要的setData
    if (isAtFirstScreen !== this.data.isAtFirstScreen) {
      this.setData({
        isAtFirstScreen: isAtFirstScreen
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '好生意数字化解决方案 - 贝克智软',
      path: '/pages/pdhsy/pdhsy',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    }
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '好生意数字化解决方案 - 贝克智软',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 处理顶部导航切换
   */
  handleTabChange: function(e) {
    // 直接使用navigator服务中的统一处理方法
    navService.handleNavBarTabChange(e);
  },

  navigateToVersionhkj: function(e) {
    navService.navigateToVersionPage('hsy');
  },

  /**
   * 滚动到产品介绍部分
   */
  scrollToIntro: function() {
    wx.createSelectorQuery()
      .select('#product-intro')
      .boundingClientRect(function(rect){
        // 使用动画滚动到产品介绍区域
        wx.pageScrollTo({
          scrollTop: rect.top,
          duration: 300
        });
      })
      .exec();
  },

  /**
   * 处理免费试用导航
   */
  navigateToTrial: function() {
    navService.navigateToProductService(this.data.productKey, 'trial');
  },

  /**
   * 处理来自promo-card组件的跳转请求
   */
  handleJoinPromo: function(e) {
    const { productKey } = e.detail;
    console.log(`[pdhsy page] 接收到 joinpromo 事件, productKey: ${productKey}`);
    if (productKey) {
      navService.navigateToVersionPage(productKey);
    }
  },

  /**
   * 打电话
   */
  makePhoneCall: function() {
    navService.makePhoneCall();
  },

  /**
   * 处理水平滑动切换页面
   */
  handleHorizontalSwipe: function(direction) {
    if (this.data.isAnimating) return;
    navService.switchProductPage(this.data.productKey, direction);
    this.setData({ isAnimating: true });
    setTimeout(() => {
      this.setData({ isAnimating: false });
    }, 350);
  },

  /**
   * 触摸开始事件处理
   */
  onTouchStart: function(e) {
    // 记录起始触摸点
    this.setData({
      touchStartY: e.changedTouches[0].clientY,
      touchStartX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY,
      touchMoveX: e.changedTouches[0].clientX
    });
  },

  /**
   * 触摸移动事件处理
   */
  onTouchMove: function(e) {
    const { 
      touchStartX,
      isAnimating
    } = this.data;
    
    if (isAnimating) return;
    
    const currentX = e.changedTouches[0].clientX;
    const currentY = e.changedTouches[0].clientY;
    
    // 保存当前触摸位置
    this.setData({
      touchMoveX: currentX,
      touchMoveY: currentY
    });
  },

  /**
   * 触摸结束事件处理
   */
  onTouchEnd: function(e) {
    const { 
      touchStartX, 
      touchMoveX,
      touchStartY,
      touchMoveY,
      isAnimating,
      swipeThreshold,
      lastSwipeTime,
      isAtFirstScreen
    } = this.data;
    
    if (isAnimating) return;
    
    const moveX = touchStartX - touchMoveX;
    const moveY = touchStartY - touchMoveY;
    const now = Date.now();
    
    if (now - lastSwipeTime < 300) return;
    this.setData({ lastSwipeTime: now });
    
    // 判断是水平滑动还是垂直滑动
    if (Math.abs(moveX) > Math.abs(moveY)) {
      // 水平滑动
      if (isAtFirstScreen && Math.abs(moveX) > swipeThreshold) {
        if (moveX > 0) {
          // 左滑
          this.handleHorizontalSwipe('left');
        } else {
          // 右滑
          this.handleHorizontalSwipe('right');
        }
      }
    }
  },

  /**
   * 导航到更多页面
   */
  navigateToMore: function() {
    navService.navigateToProductService(this.data.productKey, 'consult');
  },
  
  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  }
}); 