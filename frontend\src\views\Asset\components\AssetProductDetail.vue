<template>
  <div class="asset-product-detail">
    <!-- 产品详情组件 - 处理产品相关信息 -->
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px" :disabled="readonly">
      
      <!-- 产品基础信息 -->
      <div class="form-section">
        <h4 class="section-title">产品基础信息</h4>
        <div class="product-basic-grid">
          <!-- 产品ID-版本号 -->
          <el-form-item label="产品版本" prop="product_id" class="field-product">
            <!-- 只读模式显示产品名称 -->
            <el-input
              v-if="readonly"
              :value="getProductName()"
              readonly
              placeholder="未选择产品"
            />
            <!-- 编辑模式显示选择器 -->
            <el-select
              v-else
              v-model="formData.product_id"
              placeholder="请选择产品版本"
              filterable
              clearable
              @change="handleProductChange"
            >
              <el-option
                v-for="item in productOptions"
                :key="item.id"
                :label="`${item.product_name} (${item.version_name || 'undefined'})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <!-- 使用人数 -->
          <el-form-item label="使用人数" prop="user_count" class="field-user-count">
            <el-input-number
              v-model="formData.user_count"
              :min="1"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- 账套数 -->
          <el-form-item label="账套数" prop="account_count" class="field-account-count">
            <el-input-number
              v-model="formData.account_count"
              :min="1"
              :disabled="readonly"
            />
          </el-form-item>
        </div>

        <div class="product-dates-grid">
          <!-- 购买时长 -->
          <el-form-item label="购买时长" prop="duration_months" class="field-duration">
            <div class="duration-input">
              <el-input-number
                v-model="formData.duration_months"
                :min="1"
                :disabled="readonly"
              />
              <span class="unit-text">月</span>
            </div>
          </el-form-item>

          <!-- 购买日期 -->
          <el-form-item label="购买日期" prop="purchase_date" class="field-purchase-date">
            <el-date-picker
              v-model="formData.purchase_date"
              type="date"
              placeholder="选择购买日期"
              :disabled="readonly"
              @change="handlePurchaseDateChange"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 产品功能 -->
      <div class="form-section">
        <h4 class="section-title">产品功能</h4>

        <!-- 已选功能 -->
        <el-form-item label="选择产品功能" prop="selected_features">
          <div class="selected-features">
            <transition-group name="feature-tag" tag="div" class="tag-container">
              <el-tag
                v-for="feature in selectedFeatures"
                :key="feature.id"
                :closable="!readonly"
                @close="removeFeature(feature.id)"
                type="primary"
                class="feature-tag"
              >
                {{ feature.feature_name }} {{ feature.price }}元
              </el-tag>
            </transition-group>
            <span v-if="selectedFeatures.length === 0" class="placeholder-text">
              请从下方选择功能
            </span>
          </div>
        </el-form-item>

        <!-- 分隔线 -->
        <div class="feature-divider"></div>

        <!-- 待选功能 -->
        <div class="available-features">
          <span v-if="availableFeatures.length === 0" class="no-features-text">
            请先选择产品版本以加载可选功能
          </span>
          <el-tag
            v-for="feature in availableFeatures"
            :key="feature.id"
            @click="!readonly && addFeature(feature.id)"
            :class="{ 'clickable': !readonly }"
            class="available-feature-tag"
          >
            {{ feature.feature_name }} {{ feature.price }}元
          </el-tag>
        </div>
      </div>

      <!-- 到期日期 -->
      <div class="form-section">
        <h4 class="section-title">到期日期</h4>
        <div class="expiry-dates-grid">
          <!-- 产品到期日 -->
          <el-form-item label="产品到期日" prop="product_expiry_date" class="field-product-expiry">
            <el-date-picker
              v-model="formData.product_expiry_date"
              type="date"
              placeholder="选择产品到期日"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- SPS到期日 -->
          <el-form-item label="SPS到期日" prop="sps_expiry_date" class="field-sps-expiry">
            <el-date-picker
              v-model="formData.sps_expiry_date"
              type="date"
              placeholder="选择SPS到期日"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- 服务到期日 -->
          <el-form-item label="服务到期日" prop="after_sales_expiry_date" class="field-service-expiry">
            <el-date-picker
              v-model="formData.after_sales_expiry_date"
              type="date"
              placeholder="选择服务到期日"
              :disabled="readonly"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 价格信息 -->
      <div class="form-section">
        <h4 class="section-title">价格信息</h4>
        <div class="price-info-grid">
          <!-- 产品标准价 -->
          <el-form-item label="产品标准价" prop="product_standard_price" class="field-standard-price">
            <el-input-number
              v-model="formData.product_standard_price"
              :min="0"
              :precision="2"
              :controls="false"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- SPS年费 -->
          <el-form-item label="SPS年费" prop="sps_annual_fee" class="field-sps-fee">
            <el-input-number
              v-model="formData.sps_annual_fee"
              :min="0"
              :precision="2"
              :controls="false"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- 售后服务费用 -->
          <el-form-item label="售后服务费用" prop="after_sales_service_fee" class="field-service-fee">
            <el-input-number
              v-model="formData.after_sales_service_fee"
              :min="0"
              :precision="2"
              :controls="false"
              :disabled="readonly"
            />
          </el-form-item>

          <!-- 实施费用 -->
          <el-form-item label="实施费用" prop="implementation_fee" class="field-implementation-fee">
            <el-input-number
              v-model="formData.implementation_fee"
              :min="0"
              :precision="2"
              :controls="false"
              :disabled="readonly"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getProductFeaturesByProductId } from '@/api/product.js'

// Props定义
const props = defineProps({
  // 表单数据对象
  formData: {
    type: Object,
    required: true
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  // 产品选项列表
  productOptions: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['product-change', 'data-change'])

// 表单引用
const formRef = ref(null)

// 状态数据
const allFeatures = ref([])

// 表单验证规则
const formRules = {
  product_id: [
    { required: true, message: '请选择产品版本', trigger: 'change' }
  ],
  user_count: [
    { required: true, message: '使用人数不能为空', trigger: 'blur' },
    { type: 'number', min: 1, message: '使用人数必须大于0', trigger: 'blur' }
  ],
  account_count: [
    { required: true, message: '账套数不能为空', trigger: 'blur' },
    { type: 'number', min: 1, message: '账套数必须大于0', trigger: 'blur' }
  ]
}

// 计算属性：已选择的功能
const selectedFeatures = computed(() => {
  if (!props.formData.selected_features || !allFeatures.value.length) return []
  const selectedIds = props.formData.selected_features || []
  return allFeatures.value.filter(f => selectedIds.includes(f.id))
})

// 计算属性：可选择的功能
const availableFeatures = computed(() => {
  if (!allFeatures.value.length) return []
  const selectedIds = props.formData.selected_features || []
  return allFeatures.value.filter(f => !selectedIds.includes(f.id))
})

// 加载产品功能列表 - 根据产品ID加载对应功能
const loadProductFeatures = async (productId) => {
  try {
    if (!productId) {
      allFeatures.value = []
      return
    }

    const features = await getProductFeaturesByProductId(productId)
    allFeatures.value = features || []
  } catch (error) {
    console.error('加载产品功能失败:', error)
    allFeatures.value = []
  }
}

// 获取产品名称（用于只读模式显示）
const getProductName = () => {
  if (!props.formData.product_id) return ''

  // 优先从资产数据的关联对象中获取
  if (props.formData.product && props.formData.product.product_name) {
    const versionName = props.formData.product.version_name || 'undefined'
    return `${props.formData.product.product_name} (${versionName})`
  }

  // 从选项列表中查找
  const product = props.productOptions.find(
    item => item.id === props.formData.product_id
  )
  if (product) {
    const versionName = product.version_name || 'undefined'
    return `${product.product_name} (${versionName})`
  }

  return `ID: ${props.formData.product_id}`
}

// 产品变更处理
const handleProductChange = async (productId) => {
  // 自动填充产品基础数据
  autoFillProductData(productId)

  // 重新加载产品对应的功能
  await loadProductFeatures(productId)

  // 触发产品变更事件
  emit('product-change', productId)
  emit('data-change')
}

// 自动填充产品数据
const autoFillProductData = (productId) => {
  const selectedProduct = props.productOptions.find(product => product.id === productId)

  if (selectedProduct && props.formData) {
    // 自动填充基础数据，但允许用户修改
    if (selectedProduct.base_user_count !== undefined) {
      props.formData.user_count = Number(selectedProduct.base_user_count)
    }
    if (selectedProduct.base_account_count !== undefined) {
      props.formData.account_count = Number(selectedProduct.base_account_count)
    }
    if (selectedProduct.base_price !== undefined) {
      props.formData.product_standard_price = Number(selectedProduct.base_price)
    }

    // 清空之前选择的功能，等待新产品的功能加载完成后再设置
    props.formData.selected_features = []
  }
}

// 购买日期变更处理 - 自动计算到期日期
const handlePurchaseDateChange = (date) => {
  if (date && props.formData.duration_months) {
    const purchaseDate = new Date(date)
    const expiryDate = new Date(purchaseDate)
    expiryDate.setMonth(expiryDate.getMonth() + props.formData.duration_months)
    
    // 自动设置产品到期日
    props.formData.product_expiry_date = expiryDate
    
    // SPS到期日通常与产品到期日相同
    props.formData.sps_expiry_date = new Date(expiryDate)
    
    // 服务到期日通常比产品到期日晚一些（比如3个月）
    const serviceExpiryDate = new Date(expiryDate)
    serviceExpiryDate.setMonth(serviceExpiryDate.getMonth() + 3)
    props.formData.after_sales_expiry_date = serviceExpiryDate
  }
  
  emit('data-change')
}

// 添加功能
const addFeature = (featureId) => {
  if (props.readonly) return

  if (!props.formData.selected_features) {
    props.formData.selected_features = []
  }

  if (!props.formData.selected_features.includes(featureId)) {
    props.formData.selected_features.push(featureId)
    emit('data-change')
  }
}

// 移除功能
const removeFeature = (featureId) => {
  if (props.readonly) return

  const features = props.formData.selected_features || []
  const index = features.indexOf(featureId)
  if (index > -1) {
    features.splice(index, 1)
    emit('data-change')
  }
}

// 表单验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单验证
const resetValidation = () => {
  formRef.value?.resetFields()
}

// 生命周期
onMounted(() => {
  // 如果已经选择了产品，加载对应的功能
  if (props.formData.product_id) {
    loadProductFeatures(props.formData.product_id)
  }
})

// 监听产品ID变化
watch(() => props.formData.product_id, (newProductId) => {
  if (newProductId) {
    loadProductFeatures(newProductId)
  } else {
    allFeatures.value = []
  }
})

// 监听表单数据变化，确保数字字段的类型正确
watch(() => props.formData, (newData) => {
  if (newData) {
    // 确保数字字段是数字类型，而不是字符串
    const numberFields = [
      'user_count', 'account_count', 'duration_months',
      'product_standard_price', 'sps_annual_fee', 'after_sales_service_fee', 'implementation_fee'
    ]

    numberFields.forEach(field => {
      if (newData[field] !== undefined && newData[field] !== null && newData[field] !== '') {
        const numValue = Number(newData[field])
        if (!isNaN(numValue) && newData[field] !== numValue) {
          newData[field] = numValue
        }
      }
    })
  }
}, { deep: true, immediate: true })

// 暴露方法给父组件
defineExpose({
  validate,
  resetValidation
})
</script>

<style scoped>
.asset-product-detail {
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

/* 功能选择样式 */
.selected-features {
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center; /* 与文字中间对齐 */
  padding: 0;
  margin: 0;
}

.feature-tag {
  margin: 0;
  transition: all 0.3s ease; /* 添加平滑动画 */
  font-weight: 500; /* 已选标签字体稍微加粗 */
}

.placeholder-text {
  color: #909399;
  font-size: 14px;
  font-style: italic;
  line-height: 32px; /* 与标签高度对齐 */
}

/* 分隔线样式 */
.feature-divider {
  margin: 16px 0 12px 120px; /* 与表单标签宽度对齐 */
  border-top: 1px dashed #d9d9d9;
  position: relative;
}

.feature-divider::before {
  content: "待选功能";
  position: absolute;
  top: -10px;
  left: 0;
  background: white;
  padding: 0 8px;
  font-size: 12px;
  color: #909399;
}

.available-features {
  margin-left: 120px; /* 与上方表单项标签宽度对齐 */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0; /* 增加一些垂直间距 */
}

.no-features-text {
  color: #909399;
  font-size: 14px;
  font-style: italic;
  line-height: 32px;
}

.available-feature-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
  background: #f5f7fa; /* 浅灰背景 */
  color: #606266; /* 深灰文字 */
  border: 1px dashed #c0c4cc; /* 虚线边框 */
  font-weight: normal; /* 普通字重 */
}

.available-feature-tag.clickable:hover {
  background: #409eff;
  color: white;
  border: 1px solid #409eff; /* 实线边框 */
  transform: translateY(-1px); /* 轻微上浮效果 */
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.available-feature-tag:not(.clickable) {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 标签容器 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center; /* 与文字中间对齐 */
}

/* 标签动画效果 */
.feature-tag-enter-active,
.feature-tag-leave-active {
  transition: all 0.3s ease;
}

.feature-tag-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(-10px);
}

.feature-tag-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(-10px);
}

/* 标签移动动画 */
.feature-tag-move {
  transition: transform 0.3s ease;
}

/* 响应式Grid布局 */
.asset-product-detail {
  container-type: inline-size;
}

/* 产品基础信息Grid - 使用固定列数避免布局问题 */
.product-basic-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距 */
  /* 使用固定3列布局，确保不会挤压 */
  grid-template-columns: 1fr 1fr 1fr;
  align-items: start; /* 顶部对齐 */
}

/* 日期信息Grid - 使用2列布局 */
.product-dates-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距 */
  grid-template-columns: 1fr 1fr;
  align-items: start;
}

/* 到期日期Grid - 使用3列布局 */
.expiry-dates-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距 */
  grid-template-columns: 1fr 1fr 1fr;
  align-items: start;
}

/* 价格信息Grid - 使用2列布局避免挤压 */
.price-info-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距 */
  grid-template-columns: 1fr 1fr;
  align-items: start;
}

/* 购买时长特殊样式 */
.duration-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.duration-input .el-input-number {
  flex: 1;
}

.unit-text {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
}

/* 输入框宽度优化 - 确保所有输入框都有合适的宽度 */
.product-basic-grid .el-input,
.product-basic-grid .el-select,
.product-basic-grid .el-input-number {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

.product-dates-grid .el-input-number,
.product-dates-grid .el-date-picker {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

.expiry-dates-grid .el-date-picker {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

.price-info-grid .el-input-number {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

/* 确保表单项不会超出容器 */
.el-form-item {
  margin-bottom: 16px;
  overflow: hidden; /* 防止内容溢出 */
}

.el-form-item__content {
  overflow: hidden; /* 防止内容溢出 */
}

/* 响应式布局适配 */
@media (max-width: 1200px) {
  .product-basic-grid {
    grid-template-columns: 1fr 1fr; /* 中等屏幕使用2列 */
    gap: 16px 20px;
  }

  .expiry-dates-grid {
    grid-template-columns: 1fr 1fr; /* 中等屏幕使用2列 */
    gap: 16px 20px;
  }

  .price-info-grid {
    grid-template-columns: 1fr 1fr; /* 保持2列 */
    gap: 16px 20px;
  }
}

@media (max-width: 768px) {
  .product-basic-grid,
  .product-dates-grid,
  .expiry-dates-grid,
  .price-info-grid {
    grid-template-columns: 1fr; /* 小屏幕全部使用单列 */
    gap: 16px;
  }
}

@media (max-width: 600px) {
  .asset-product-detail {
    padding: 12px;
  }

  .form-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 16px;
  }
}

.el-form-item {
  margin-bottom: 0; /* Grid已处理间距 */
}

/* 只读模式样式调整 */
.el-form--disabled .el-input__inner,
.el-form--disabled .el-select .el-input__inner,
.el-form--disabled .el-input-number .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}
</style>
