/**
 * 好云仓产品介绍页面
 * 支持左右滑动切换页面、上下滑动查看详情
 */
const navService = require('../../utils/navigator.js');
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品海报信息
    posterTitle: '好业财',
    posterSubtitle: '简单易用的业财一体化软件',
    posterSlogan: '进销存+财务+线上商城+项目合同',
    touchStartY: 0,
    touchMoveY: 0,
    touchStartX: 0,
    touchMoveX: 0,
    isAnimating: false,
    lastSwipeTime: 0,
    swipeThreshold: 50,
    activeTab: 3,
    productKey: 'hyc',
    // 添加首屏判断标记
    isAtFirstScreen: true,
    // 当前激活的案例标签
    activeCase: 0,
    // 新增行业案例选项卡状态
    activeIndustry: 0,
    // 新增FAQ展开状态
    faqExpanded: [false, false, false, false],
    // 行业案例数据
    industryData: [
      {
        name: '食品行业',
        desc: '食品行业品类多，对批次、保质期、灵活改需求高，对订单进行千客千价、精准送货、智能补货管理需求高',
        challenges: ['批次管理', '保质期管理', '智能补货', '库存预警', '智能改价'],
        solution: '好业财基于多年食品行业服务经验，针对性解决食品企业在采购入库、仓储管理、销售发货、数据统计等环节的管理难题，一体化打通业务与财务，保证数据实时更新、账务准确无误。'
      },
      {
        name: '日用百货',
        desc: '手工下单效率低易出错，进货价格浮动大，出货量大，需要随时掌握库存情况',
        challenges: ['手机查库存', '采购历史价格跟踪', '智能补货', '畅销滞销分析', '手机开单对账'],
        solution: '好业财为日用百货企业提供从采购、仓储到销售的全链路数字化解决方案，灵活的价格体系和多种营销工具帮助企业提升竞争力，精准分析销售数据辅助决策。'
      },
      {
        name: '设备配件',
        desc: '设备参数型号多，"以销定采"销售采购关联难，设备配件组装用料盲目，领料不规范，应收周期长，对账繁杂',
        challenges: ['以销定采', '组装拆卸', '账期管理', '库存预警', '智能查账对账'],
        solution: '好业财针对设备配件行业特点，提供完整的项目合同管理功能，支持项目全生命周期管理，确保从销售、发货到售后的全流程可追溯，帮助企业提升管理效率和客户满意度。'
      },
      {
        name: '建筑安装',
        desc: '做项目的企业经常面临 项目多、参与人员多、成本核算不清、项目延期、盈利下降的情况',
        challenges: ['项目进度管理', '自动归集收入、费用', '项目分包', '项目预算', '报销审批', '项目看板'],
        solution: '好业财为建筑安装企业提供项目全周期管理方案，从预算编制、材料采购到人工结算、进度监控，实现项目成本精细化管控，提高项目利润率，解决多项目并行管理复杂性问题。'
      },
      {
        name: '五金建材',
        desc: '五金建材商品型号繁杂、统计不清容易造成积压和缺货，订单错发漏发问题多，客户、厂家对账统计易出错',
        challenges: ['商品多规格', '多计量单位', '预警', '手机报价开单', '财务税务'],
        solution: '好业财针对五金建材行业特点，提供批发零售一体化管理，支持灵活的价格体系设置，多种计量单位转换，批发客户专属价格管理，帮助企业降低库存成本，提高周转率和客户满意度。'
      },
      {
        name: '酒水饮料',
        desc: '酒水整件、散件的价格、库存量管理难，仓库商品跑冒滴漏严重，厂家返利难统计，包装物的退回、损耗、押金管理难',
        challenges: ['多计量管理', '手机查库存', '开单', '多级价格管控', '返利计算', '包装物管理'],
        solution: '好业财为酒水饮料企业提供渠道分销一体化解决方案，支持多级经销商管理，促销活动精准投放，终端铺货数据实时采集，库存预警自动提醒，助力企业打造高效畅通的销售网络。'
      },
      {
        name: '医疗器械',
        desc: '医疗器械要求严谨，档案信息管理要求多，一品多厂商品渠道管理复杂，手工记录溯源难，应收应付统计难',
        challenges: ['组装拆卸管理', '项目合同管理', '以销定采', '预算成本分析','业务财务一体化'],
        solution: '好业财为医疗器械企业提供符合行业法规的管理系统，支持产品批次全程追溯，证照效期管理，医院客户档案维护，专业销售团队绩效考核，售后服务全流程记录，助力企业规范经营，降低合规风险。'
      },
      {
        name: '服装鞋帽',
        desc: '款式、颜色、尺码多样，出库、入库核对困难，不同销售渠道客户报价不同，节假日制定促销政策多',
        challenges: ['商品多规格', '扫码出入库', '不同渠道价格管理', '促销打折特价', '智能对账'],
        solution: '好业财为服装鞋帽企业提供灵活的多属性SKU管理，支持按季节、款式、颜色、尺码等多维度管理商品，线上商城与门店销售无缝对接，助力企业实现全渠道库存共享和灵活调拨。'
      }
    ],
    // FAQ数据
    faqData: [
      {
        question: '好业财适合哪些规模的企业使用？',
        answer: '好业财主要适合中小微企业和成长型企业使用，尤其是有进销存、财务、多渠道销售等业务需求的企业。无论您是10人以下的小微企业，还是数百人的中型企业，好业财都能满足您的业务管理需求。'
      },
      {
        question: '好业财如何保障企业数据安全？',
        answer: '好业财采用银行级数据安全标准，数据传输全程加密，多重备份确保数据不丢失。我们的系统通过了等保三级认证，为客户提供安全可靠的数据存储和使用环境。'
      },
      {
        question: '使用好业财需要专门的IT人员吗？',
        answer: '不需要。好业财设计简单易用，操作直观，普通员工经过简单培训即可上手使用。我们还提供7*24小时在线客服支持，随时解答您在使用过程中遇到的问题。'
      },
      {
        question: '好业财可以和其他系统对接吗？',
        answer: '是的，好业财提供丰富的API接口，可以与电商平台、支付系统、物流系统等第三方系统对接。我们还支持数据导入导出功能，方便您迁移历史数据或与其他系统交换数据。'
      }
    ],
    scrollTop: 0,
    showNavBackground: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({ activeTab: 3 });
  },

  /**
   * 监听页面滚动事件
   */
  onPageScroll: function(e) {
    // 如果滚动位置超过一定值（如100px），认为已经离开首屏
    const isAtFirstScreen = e.scrollTop < 100;
    
    // 只有当状态需要变化时才更新，减少不必要的setData
    if (isAtFirstScreen !== this.data.isAtFirstScreen) {
      this.setData({
        isAtFirstScreen: isAtFirstScreen
      });
    }
  },

  /**
   * 切换案例标签
   */
  switchCase: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeCase: index
    });
  },

  /**
   * 处理顶部导航切换
   */
  handleTabChange: function(e) {
    // 直接使用navigator服务中的统一处理方法
    navService.handleNavBarTabChange(e);
  },

  /**
   * 触摸开始事件处理
   */
  onTouchStart: function(e) {
    // 记录起始触摸点
    this.setData({
      touchStartY: e.changedTouches[0].clientY,
      touchStartX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY,
      touchMoveX: e.changedTouches[0].clientX
    });
  },

  /**
   * 触摸移动事件处理
   */
  onTouchMove: function(e) {
    if (this.data.isAnimating) return;
    
    this.setData({
      touchMoveX: e.changedTouches[0].clientX,
      touchMoveY: e.changedTouches[0].clientY
    });
  },

  /**
   * 触摸结束事件处理
   */
  onTouchEnd: function(e) {
    const { 
      touchStartX, 
      touchMoveX,
      touchStartY,
      touchMoveY,
      isAnimating,
      swipeThreshold,
      lastSwipeTime,
      productKey,
      isAtFirstScreen
    } = this.data;
    
    if (isAnimating) return;
    
    const moveX = touchStartX - touchMoveX;
    const moveY = touchStartY - touchMoveY;
    const now = Date.now();
    
    if (now - lastSwipeTime < 300) return;
    this.setData({ lastSwipeTime: now });
    
    // 只有在首屏且为水平滑动时才允许切换页面
    if (isAtFirstScreen && Math.abs(moveX) > Math.abs(moveY) && Math.abs(moveX) > swipeThreshold) {
      const direction = moveX > 0 ? 'left' : 'right';
      this.setData({ isAnimating: true });
      
      // 使用导航服务切换产品页面
      navService.switchProductPage(productKey, direction);
      
      // 动画结束后重置状态
      setTimeout(() => {
        this.setData({ isAnimating: false });
      }, 350);
    }
  },

  /**
   * 导航到更多页面
   */
  navigateToMore: function() {
    navService.navigateToProductService(this.data.productKey, 'consult');
  },

  navigateToVersionhkj: function(e) {
    navService.navigateToVersionPage('hyc');
  },

  /**
   * 打电话
   */
  makePhoneCall: function() {
    navService.makePhoneCall();
  },

  /**
   * 处理来自promo-card组件的跳转请求
   */
  handleJoinPromo: function(e) {
    const { productKey } = e.detail;
    console.log(`[pdhyc page] 接收到 joinpromo 事件, productKey: ${productKey}`);
    if (productKey) {
      navService.navigateToVersionPage(productKey);
    }
  },

  // 切换行业案例
  switchIndustry: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeIndustry: index
    });
  },

  // 切换FAQ展开状态
  toggleFaq: function(e) {
    const index = e.currentTarget.dataset.index;
    let expanded = this.data.faqExpanded;
    expanded[index] = !expanded[index];
    this.setData({
      faqExpanded: expanded
    });
  },

  // 立即体验
  startExperience: function() {
    wx.navigateTo({
      url: '/pages/experience/experience'
    });
  },

  // 滚动到指定位置
  scrollToSection: function(e) {
    const section = e.currentTarget.dataset.section;
    wx.createSelectorQuery().select(`#${section}`).boundingClientRect(function(rect){
      wx.pageScrollTo({
        scrollTop: rect.top,
        duration: 300
      });
    }).exec();
  },

  onShareAppMessage: function () {
    return {
      title: '好业财 - 业财一体化管理软件',
      path: '/pages/pdhyc/pdhyc',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '好业财 - 业财一体化管理软件',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 处理底部导航事件
   */
  onBottomNavEvent: function(e) {
    const tab = e.detail.tab;
    navService.handleBottomNav(tab);
  },

  /**
   * 处理海报滑动事件
   */
  handlePosterSwipe: function(e) {
    // 处理海报滑动事件
    const direction = e.detail.direction;
    console.log('Poster swiped:', direction);
    
    // 使用导航服务切换产品页面
    navService.switchProductPage(this.data.productKey, direction);
  }
}); 