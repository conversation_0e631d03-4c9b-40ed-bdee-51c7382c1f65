<script setup>
import { ref, watch, defineProps, defineEmits, computed, onMounted } from 'vue';
import { getFeatures } from '@/api/feature.js';
import { 
  addFeatureToProduct, 
  removeFeatureFromProduct, 
  updateFeatureOnProduct 
} from '@/api/product.js';
import { ElMessage } from 'element-plus';

// -- Props and Emits --
const props = defineProps({
  visible: { type: Boolean, required: true },
  product: { type: Object, required: true }
});

const emit = defineEmits(['update:visible', 'features-updated']);

// -- 内部状态 --
const allFeatures = ref([]);
const featureForm = ref({
  feature_id: '',
  feature_price: 0,
  remark: ''
});

const editFeatureDialogVisible = ref(false);
const editFeatureForm = ref({
  feature_id: '',
  feature_name: '',
  feature_price: 0,
  remark: ''
});

// 计算出当前产品尚未拥有的功能列表
const availableFeatures = computed(() => {
  if (!props.product || !props.product.features) return [];
  const existingFeatureIds = props.product.features.map(f => f.feature_id);
  return allFeatures.value.filter(f => !existingFeatureIds.includes(f.feature_id));
});

// -- 方法 --
const loadAllFeatures = async () => {
    try {
        allFeatures.value = await getFeatures();
    } catch(error) {
        ElMessage.error('获取可用功能列表失败');
    }
}

const handleAddFeature = async () => {
  if (!featureForm.value.feature_id) {
    ElMessage.warning('请选择一个功能');
    return;
  }
   if (featureForm.value.feature_price <= 0) {
    ElMessage.warning('请输入有效价格');
    return;
  }
  try {
    await addFeatureToProduct(props.product.id, {
      feature_id: featureForm.value.feature_id,
      feature_price: Number(featureForm.value.feature_price),
      remark: featureForm.value.remark
    });
    ElMessage.success('功能添加成功');
    featureForm.value = { feature_id: '', feature_price: 0, remark: '' };
    emit('features-updated'); // 通知父组件刷新
  } catch (error) {
    ElMessage.error('添加功能失败: ' + (error.response?.data?.message || '未知错误'));
  }
};

const handleRemoveFeature = async (featureId) => {
  try {
    await removeFeatureFromProduct(props.product.id, featureId);
    ElMessage.success('功能移除成功');
    emit('features-updated'); // 通知父组件刷新
  } catch (error) {
    ElMessage.error('移除功能失败');
  }
};

const handleOpenEditFeatureDialog = (feature) => {
  editFeatureForm.value = {
    feature_id: feature.feature_id,
    feature_name: feature.feature_name,
    feature_price: feature.ProductFeatureRelation.feature_price,
    remark: feature.ProductFeatureRelation.remark,
  };
  editFeatureDialogVisible.value = true;
};

const handleUpdateFeature = async () => {
  try {
    await updateFeatureOnProduct(
      props.product.id,
      editFeatureForm.value.feature_id,
      {
        feature_price: Number(editFeatureForm.value.feature_price),
        remark: editFeatureForm.value.remark
      }
    );
    ElMessage.success('更新成功');
    editFeatureDialogVisible.value = false;
    emit('features-updated'); // 通知父组件刷新
  } catch (error) {
    ElMessage.error('更新失败: ' + (error.response?.data?.message || '未知错误'));
  }
};

const handleClose = () => {
  emit('update:visible', false);
}

// 在组件挂载时加载所有可用功能
onMounted(loadAllFeatures);

// [新增] 侦听 product prop 的变化，重新加载可用功能列表
watch(() => props.product, () => {
    loadAllFeatures();
}, { deep: true });
</script>

<template>
  <div>
    <!-- 主对话框：管理产品功能 -->
    <el-dialog 
      :model-value="visible" 
      title="管理产品功能" 
      width="800px" 
      @update:modelValue="handleClose"
      :close-on-click-modal="false"
    >
        <div v-if="product">
            <h3>正在为产品: {{ product.product_name }} ({{ product.product_id }}) 管理功能</h3>
            
            <!-- 已关联功能列表 -->
            <h4>已关联功能</h4>
            <el-table :data="product.features" border stripe>
                <el-table-column prop="ProductFeatureRelation.id" label="关联关系ID" width="120" />
                <el-table-column prop="feature_id" label="功能ID" width="120" />
                <el-table-column prop="feature_name" label="功能名称" />
                <el-table-column prop="ProductFeatureRelation.feature_price" label="价格">
                    <template #default="scope">
                        ￥{{ scope.row.ProductFeatureRelation.feature_price }}
                    </template>
                </el-table-column>
                <el-table-column prop="ProductFeatureRelation.remark" label="备注" />
                <el-table-column label="操作" width="180">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="handleOpenEditFeatureDialog(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleRemoveFeature(scope.row.feature_id)">移除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-divider />

            <!-- 添加新功能表单 -->
            <h4>添加新功能</h4>
            <el-form :model="featureForm" label-width="80px" inline>
                <el-form-item label="选择功能">
                    <el-select v-model="featureForm.feature_id" placeholder="选择一个功能" style="width: 200px">
                        <el-option 
                            v-for="f in availableFeatures" 
                            :key="f.feature_id" 
                            :label="f.feature_name" 
                            :value="f.feature_id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="价格">
                    <el-input-number v-model="featureForm.feature_price" :min="0" controls-position="right" style="width: 150px;"></el-input-number>
                </el-form-item>
                 <el-form-item label="备注">
                    <el-input v-model="featureForm.remark" placeholder="可选备注" style="width: 150px;"></el-input>
                </el-form-item>
                <el-form-item>
                     <el-button type="primary" @click="handleAddFeature">添加</el-button>
                </el-form-item>
            </el-form>
            
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 嵌套的子对话框：编辑功能关联 -->
    <el-dialog 
      v-model="editFeatureDialogVisible" 
      title="编辑功能价格" 
      width="500px" 
      append-to-body
    >
      <el-form :model="editFeatureForm" label-width="80px">
        <el-form-item label="功能名称">
          <el-input :value="editFeatureForm.feature_name" disabled />
        </el-form-item>
        <el-form-item label="价格">
          <el-input-number v-model="editFeatureForm.feature_price" :min="0" controls-position="right" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editFeatureForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editFeatureDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateFeature">提交更新</el-button>
      </template>
    </el-dialog>
  </div>
</template> 