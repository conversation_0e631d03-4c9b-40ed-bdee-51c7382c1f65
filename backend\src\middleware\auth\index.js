/**
 * 认证中间件统一入口
 * 提供清晰的API接口，同时保持向后兼容
 */

const { verifyToken, requireUserType } = require('./base');
const employeeAuth = require('./employee');
const userAuth = require('./user');

// 向后兼容的isAdmin中间件（与原authJwt.js完全一致）
const isAdmin = (req, res, next) => {
  // 这个中间件应该在 verifyToken 之后使用
  // 所以我们可以安全地访问 req.user
  if (req.user && req.user.role === 'admin') {
    next(); // 用户是管理员，放行
  } else {
    res.status(403).json({ message: '操作失败：需要管理员权限！' });
  }
};

// 向后兼容的导出（保持原有API不变）
const authJwt = {
  verifyToken,
  isAdmin,
  // 添加新的分类API
  employee: employeeAuth,
  user: userAuth,
  base: { verifyToken, requireUserType }
};

module.exports = authJwt;
