const db = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

const Enterprise = db.Enterprise;
const User = db.User;
const Asset = db.Asset;
const OrderHead = db.OrderHead;

/**
 * 获取仪表板统计数据
 */
exports.getDashboardStats = async (req, res) => {
  try {
    // 获取企业统计
    const enterpriseStats = await Enterprise.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END")), 'monthly_new']
      ],
      raw: true
    });

    // 获取用户统计
    const userStats = await User.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END")), 'monthly_new'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN is_partner = 1 THEN 1 END")), 'partner_count'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN is_partner = 0 THEN 1 END")), 'regular_user_count']
      ],
      raw: true
    });

    // 获取资产统计
    const assetStats = await Asset.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = '在线' THEN 1 END")), 'online'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = '过期' THEN 1 END")), 'expired'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END")), 'monthly_changes']
      ],
      raw: true
    });

    // 获取订单统计
    const orderStats = await OrderHead.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END")), 'monthly'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN audit_status = '待审核' THEN 1 END")), 'pending_audit'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN audit_status = '已审核' THEN 1 END")), 'approved'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN audit_status = '已拒绝' THEN 1 END")), 'rejected'],
        [sequelize.fn('SUM', sequelize.col('actual_amount')), 'total_amount'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN actual_amount ELSE 0 END")), 'monthly_amount']
      ],
      raw: true
    });

    // 获取佣金统计
    const commissionStats = await OrderHead.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN commission_status = '已发放' THEN commission_amount ELSE 0 END")), 'total_paid'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN commission_status = '未发放' THEN commission_amount ELSE 0 END")), 'total_pending'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN commission_status = '已发放' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN commission_amount ELSE 0 END")), 'monthly_paid']
      ],
      where: {
        commission_amount: {
          [Op.gt]: 0
        }
      },
      raw: true
    });

    // 组装返回数据
    const dashboardData = {
      enterprises: {
        total: parseInt(enterpriseStats[0].total) || 0,
        monthly_new: parseInt(enterpriseStats[0].monthly_new) || 0,
        active: parseInt(enterpriseStats[0].total) || 0, // 假设所有企业都是活跃的
        pending: 0 // 企业没有待审核状态，设为0
      },
      users: {
        total: parseInt(userStats[0].total) || 0,
        monthly_new: parseInt(userStats[0].monthly_new) || 0,
        active: parseInt(userStats[0].regular_user_count) || 0,
        partner_count: parseInt(userStats[0].partner_count) || 0
      },
      partners: {
        total: parseInt(userStats[0].partner_count) || 0,
        verified: parseInt(userStats[0].partner_count) || 0, // 假设所有合伙人都已认证
        pending: 0 // 暂时设为0，可以后续添加认证状态字段
      },
      assets: {
        total: parseInt(assetStats[0].total) || 0,
        online: parseInt(assetStats[0].online) || 0,
        expired: parseInt(assetStats[0].expired) || 0,
        monthly_changes: parseInt(assetStats[0].monthly_changes) || 0,
        total_value: 0 // 资产表没有价值字段，暂时设为0
      },
      orders: {
        total: parseInt(orderStats[0].total) || 0,
        monthly: parseInt(orderStats[0].monthly) || 0,
        pending: parseInt(orderStats[0].pending_audit) || 0,
        approved: parseInt(orderStats[0].approved) || 0,
        rejected: parseInt(orderStats[0].rejected) || 0,
        total_amount: parseFloat(orderStats[0].total_amount) || 0,
        monthly_amount: parseFloat(orderStats[0].monthly_amount) || 0
      },
      commissions: {
        total: parseFloat(commissionStats[0].total_paid) || 0,
        pending: parseFloat(commissionStats[0].total_pending) || 0,
        monthly: parseFloat(commissionStats[0].monthly_paid) || 0
      }
    };

    res.status(200).json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('获取仪表板统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表板统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取用户增长趋势数据
 */
exports.getUserGrowthTrend = async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    
    let dateFormat, intervalDays;
    switch (period) {
      case '7days':
        dateFormat = '%Y-%m-%d';
        intervalDays = 7;
        break;
      case '3months':
        dateFormat = '%Y-%m';
        intervalDays = 90;
        break;
      default: // 30days
        dateFormat = '%Y-%m-%d';
        intervalDays = 30;
    }

    // 获取用户注册趋势
    const userTrend = await User.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'user_count']
      ],
      where: {
        createdAt: {
          [Op.gte]: sequelize.literal(`DATE_SUB(NOW(), INTERVAL ${intervalDays} DAY)`)
        }
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat)],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'ASC']],
      raw: true
    });

    // 获取企业注册趋势
    const enterpriseTrend = await Enterprise.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'enterprise_count']
      ],
      where: {
        createdAt: {
          [Op.gte]: sequelize.literal(`DATE_SUB(NOW(), INTERVAL ${intervalDays} DAY)`)
        }
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat)],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('createdAt'), dateFormat), 'ASC']],
      raw: true
    });

    // 获取订单趋势
    const orderTrend = await OrderHead.findAll({
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), dateFormat), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'order_count']
      ],
      where: {
        created_at: {
          [Op.gte]: sequelize.literal(`DATE_SUB(NOW(), INTERVAL ${intervalDays} DAY)`)
        }
      },
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), dateFormat)],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('created_at'), dateFormat), 'ASC']],
      raw: true
    });

    res.status(200).json({
      success: true,
      data: {
        users: userTrend,
        enterprises: enterpriseTrend,
        orders: orderTrend
      }
    });

  } catch (error) {
    console.error('获取用户增长趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户增长趋势失败',
      error: error.message
    });
  }
};

/**
 * 获取订单状态分布
 */
exports.getOrderStatusDistribution = async (req, res) => {
  try {
    const statusDistribution = await OrderHead.findAll({
      attributes: [
        'audit_status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['audit_status'],
      raw: true
    });

    const formattedData = statusDistribution.map(item => ({
      name: item.audit_status,
      value: parseInt(item.count)
    }));

    res.status(200).json({
      success: true,
      data: formattedData
    });

  } catch (error) {
    console.error('获取订单状态分布失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单状态分布失败',
      error: error.message
    });
  }
};

/**
 * 获取最近活动记录
 */
exports.getRecentActivities = async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    
    // 这里我们从多个表获取最近的活动记录
    const activities = [];

    // 最近的企业创建
    const recentEnterprises = await Enterprise.findAll({
      attributes: ['name', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    recentEnterprises.forEach(enterprise => {
      activities.push({
        type: '新增企业',
        description: `企业"${enterprise.name}"完成注册`,
        user: '系统',
        time: enterprise.createdAt
      });
    });

    // 最近的用户注册
    const recentUsers = await User.findAll({
      attributes: ['name', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    recentUsers.forEach(user => {
      activities.push({
        type: '新增用户',
        description: `用户"${user.name}"完成注册`,
        user: '系统',
        time: user.createdAt
      });
    });

    // 最近的订单审核
    const recentOrders = await OrderHead.findAll({
      attributes: ['order_id', 'audit_status', 'updated_at'],
      where: {
        audit_status: {
          [Op.in]: ['已审核', '已拒绝']
        }
      },
      order: [['updated_at', 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    recentOrders.forEach(order => {
      activities.push({
        type: '订单审核',
        description: `订单 ${order.order_id} ${order.audit_status === '已审核' ? '审核通过' : '审核拒绝'}`,
        user: '审核员',
        time: order.updated_at
      });
    });

    // 按时间排序并限制数量
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));
    const limitedActivities = activities.slice(0, parseInt(limit));

    res.status(200).json({
      success: true,
      data: limitedActivities
    });

  } catch (error) {
    console.error('获取最近活动记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最近活动记录失败',
      error: error.message
    });
  }
};
