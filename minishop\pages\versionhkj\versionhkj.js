const app = getApp()
const navService = require('../../utils/navigator.js');

Page({
  data: {
    productName: '好会计',
    promotionInfo: {},
    activityTimeRange: '',
    activeTab: 0,

    // 套餐选项
    packages: ['标准版', '专业版', '旗舰版', '普及版'],

    // 账套数基础价格配置
    packageBasePrice: {
      '标准版': 898,
      '专业版': 1998,
      '旗舰版': 2998,
      '普及版': 498
    },

    // 当前选择
    selectedPackage: '标准版',    // 默认选择标准版账套
    selectedDuration: 1,     // 默认选择1年
    selectedDiscount: 0.85,  // 默认折扣
    selectedDiscountText: '', // 新增：用于显示的折扣文本
    // 计算结果
    originalPrice: 0,        // 原价
    discountPrice: 0,        // 特惠价
    dailyPrice: 0,           // 每账套每天价格
    giftProduct: '',         // 赠品产品
    progressPercent: '0%',   // 满减进度条百分比

    // 好会计各版本功能特点
    packageFeatures: {
      '标准版': [
        '智能记账',
        '凭证管理',
        '财务报表',
        '发票管理',
        '资产管理',
        '工资管理',
        '资金管理',
        '单辅助核算'
      ],
      '专业版': [
        '包含标准版所有功能',
        '凭证审核',
        '进销台账',
        '自制资产负债表',
        '自制利润表',
        '全面税务管理'
      ],
      '旗舰版': [
        '包含专业版所有功能',
        '全自动库存核算',
        '产成品核算',
        '定制财务报表',
        '报销管理',
        '在线开票',
        '多辅助核算及分类管理'
      ],
      '普及版': [
        '智能记账',
        '凭证管理',
        '财务报表',
        '无辅助核算'
      ]
    },
    // 当前版本的功能列表
    hkjFeatures: [],
  },

  onLoad: function () {
    // 获取促销信息
    let promotionInfo = app.globalData.promotionInfo || {};

    // 格式化折扣规则，添加用于显示的 discountText
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      promotionInfo.rules = promotionInfo.rules.map(rule => {
        return {
          ...rule,
          discountText: parseFloat((rule.discount * 10).toFixed(1))
        };
      });
    }

    // 格式化活动时间范围
    const startTime = promotionInfo.startTime || '';
    const endTime = promotionInfo.endTime || '';
    const activityTimeRange = startTime.replace(/\//g, '-').substring(0, 10) +
                             ' 至 ' +
                             endTime.replace(/\//g, '-').substring(0, 10);

    // 获取默认折扣（1年折扣）
    let defaultDiscount = 0.85;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      const rule = promotionInfo.rules.find(item => item.period === 1);
      if (rule) {
        defaultDiscount = rule.discount;
      }
    }

    // 初始化默认版本的功能特点
    const defaultFeatures = this.data.packageFeatures['标准版'] || [];

    this.setData({
      promotionInfo: promotionInfo,
      activityTimeRange: activityTimeRange,
      selectedDiscount: defaultDiscount,
      selectedDiscountText: parseFloat((defaultDiscount * 10).toFixed(1)), // 设置初始折扣文本
      hkjFeatures: defaultFeatures
    });

    // 计算初始价格
    this.calculatePrice();
  },

  switchTab(e) {
    this.setData({
      activeTab: parseInt(e.currentTarget.dataset.index, 10)
    });
  },

  /**
   * 选择账套数
   */
  selectPackage: function(e) {
    const packageSize = e.currentTarget.dataset.package;

    // 获取选择版本的功能特点
    const features = this.data.packageFeatures[packageSize] || [];

    this.setData({
      selectedPackage: packageSize,
      hkjFeatures: features
    });
    this.calculatePrice();
  },

  /**
   * 选择时长
   */
  selectDuration: function(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);

    // 从促销规则中找出对应时长的折扣
    let discount = 1;
    if (this.data.promotionInfo.rules && this.data.promotionInfo.rules.length > 0) {
      const rule = this.data.promotionInfo.rules.find(item => item.period === duration);
      if (rule) {
        discount = rule.discount;
      }
    }

    this.setData({
      selectedDuration: duration,
      selectedDiscount: discount,
      selectedDiscountText: parseFloat((discount * 10).toFixed(1)) // 更新折扣文本
    });
    this.calculatePrice();
  },

  /**
   * 计算满减进度百分比
   */
  calculateProgressPercent: function(price) {
    const gifts = this.data.promotionInfo.gifts || [];
    if (!gifts || gifts.length < 3) {
      return '0%';
    }

    try {
      if (price >= gifts[2].threshold) {
        return '100%';
      } else if (price >= gifts[1].threshold) {
        return '66%';
      } else if (price >= gifts[0].threshold) {
        return '33%';
      } else {
        return '0%';
      }
    } catch (e) {
      console.error('计算满减进度出错:', e);
      return '0%';
    }
  },

  formatDate(timestamp) {
    const date = new Date(timestamp);
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 计算价格和赠品
   */
  calculatePrice: function() {
    const { selectedPackage, selectedDuration, packageBasePrice, promotionInfo } = this.data;

    // 1. 计算原价
    const basePrice = packageBasePrice[selectedPackage];
    const originalPrice = basePrice * selectedDuration;

    // 2. 获取对应年限的折扣
    let discount = 1;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      // 从促销规则中查找对应年限的折扣
      const rule = promotionInfo.rules.find(item => item.period === selectedDuration);
      if (rule) {
        discount = rule.discount;
      }
    }

    // 3. 计算特惠价
    const discountPrice = (originalPrice * discount).toFixed(1);

    // 4. 计算每版本每天价格
    const dailyPrice = (discountPrice / (selectedDuration * 365) ).toFixed(2);

    // 5. 根据特惠价确定赠品
    let giftProduct = '';
    const price = parseFloat(discountPrice);
    const gifts = promotionInfo.gifts || [];

    // 从大到小查找适用的赠品区间
    for (let i = gifts.length - 1; i >= 0; i--) {
      if (price >= gifts[i].threshold) {
        giftProduct = gifts[i].name;
        break;
      }
    }

    // 6. 计算满减进度条百分比
    const progressPercent = this.calculateProgressPercent(price);

    this.setData({
      originalPrice,
      discountPrice,
      dailyPrice,
      giftProduct,
      progressPercent
    });
  },

  /**
   * 显示产品详情
   */
  showProductDetail: function() {
    navService.navigateToProduct('hkj');
  },

  /**
   * 用户分享
   */
  onShareAppMessage: function () {
    return {
      title: '好会计618超级特惠，力度空前，一年仅此一次！',
      path: '/pages/promotion/promotion',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '好会计618超级特惠，力度空前，一年仅此一次！',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  }
})