import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        requiresAuth: false,
        noTab: true // 登录页不显示标签页
      }
    },
    {
      path: '/',
      redirect: '/dashboard',
      meta: {
        requiresAuth: true,
        noTab: true // 重定向路由不显示标签页
      }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/Dashboard/DashboardView.vue'),
      meta: {
        title: '首页',
        requiresAuth: true,
        keepAlive: true,
        closable: false // 首页不允许关闭
      }
    },
    {
      path: '/enterprises',
      name: 'enterprises',
      component: () => import('../views/Enterprise/pages/EnterpriseList.vue'),
      meta: {
        title: '企业管理',
        keepAlive: true
      }
    },
    {
      path: '/enterprises/:id',
      name: 'enterprise-detail',
      component: () => import('../views/Enterprise/pages/EnterpriseDetail.vue'),
      meta: {
        title: '企业详情',
        keepAlive: false
      }
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('../views/ProductView.vue'),
      meta: {
        title: '产品管理',
        keepAlive: true
      }
    },
    {
      path: '/features',
      name: 'features',
      component: () => import('../views/FeatureView.vue'),
      meta: {
        title: '功能管理',
        keepAlive: true
      }
    },
    {
      path: '/assets',
      name: 'asset-management',
      meta: { title: '资产管理', icon: 'Box', noTab: true },
      redirect: '/assets/list', // 默认重定向到列表页
      children: [
        {
          path: 'list',
          name: 'asset-list',
          component: () => import('@/views/Asset/pages/AssetList.vue'),
          meta: { title: '资产列表', keepAlive: true }
        },
        {
          path: 'form/:id?',
          name: 'asset-form',
          component: () => import('@/views/Asset/pages/AssetForm.vue'),
          meta: { title: '资产表单', keepAlive: false }
        },
        {
          path: 'detail/:id',
          name: 'asset-detail',
          component: () => import('@/views/Asset/pages/AssetDetail.vue'),
          meta: { title: '资产详情', keepAlive: true }
        },
        {
          path: 'change/create/:id',
          name: 'asset-change-create',
          component: () => import('@/views/Asset/pages/AssetChangeCreate.vue'),
          meta: { title: '创建变更', keepAlive: false }
        },
        {
          path: 'change/detail/:id',
          name: 'asset-change-detail',
          component: () => import('@/views/Asset/pages/AssetChangeDetail.vue'),
          meta: { title: '变更详情', keepAlive: true }
        },
        {
          path: 'change/list',
          name: 'asset-change-list',
          component: () => import('@/views/Asset/pages/AssetChangeList.vue'),
          meta: { title: '变更列表', keepAlive: true }
        }
      ]
    },
    {
      path: '/orders',
      name: 'order-management',
      meta: { title: '订单管理', icon: 'Document', noTab: true },
      redirect: '/orders/review', // 默认重定向到订单审核页面
      children: [
        {
          path: 'review',
          name: 'OrderReviewList',
          component: () => import('@/views/order/pages/OrderReviewList.vue'),
          meta: { title: '订单审核', keepAlive: true }
        },
        {
          path: 'product',
          name: 'ProductOrderList',
          component: () => import('@/views/order/pages/ProductOrderList.vue'),
          meta: { title: '产品订单', keepAlive: true }
        },
        {
          path: 'product/form',
          name: 'ProductOrderCreate',
          component: () => import('@/views/order/pages/ProductOrderForm.vue'),
          meta: { title: '新增产品订单', keepAlive: false }
        },
        {
          path: 'product/form/:id',
          name: 'ProductOrderForm',
          component: () => import('@/views/order/pages/ProductOrderForm.vue'),
          meta: { title: '产品订单表单', keepAlive: false }
        },
        {
          path: 'service',
          name: 'ServiceOrderList',
          component: () => import('@/views/order/pages/ServiceOrderList.vue'),
          meta: { title: '服务订单', keepAlive: true }
        },
        {
          path: 'service/form',
          name: 'ServiceOrderCreate',
          component: () => import('@/views/order/pages/ServiceOrderForm.vue'),
          meta: { title: '新增服务订单', keepAlive: false }
        },
        {
          path: 'service/form/:id',
          name: 'ServiceOrderForm',
          component: () => import('@/views/order/pages/ServiceOrderForm.vue'),
          meta: { title: '服务订单表单', keepAlive: false }
        }
      ]
    },
    {
      path: '/employees',
      name: 'employees',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/EmployeeView.vue'),
      meta: {
        title: '员工管理',
        keepAlive: true
      }
    },
    {
      path: '/users',
      name: 'users',
      component: () => import('../views/User/pages/UserList.vue'),
      meta: {
        title: '用户管理',
        keepAlive: true
      }
    },
    {
      path: '/users/:id',
      name: 'user-detail',
      component: () => import('../views/User/pages/UserDetail.vue'),
      meta: {
        title: '用户详情',
        keepAlive: false
      }
    },


  ],
})

//检查所有页面的访问权限，自动将未登录的用户引导至登录页。
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('authToken')

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)

  if (requiresAuth && !token) {
    next({ name: 'login' })
  } else if (to.name === 'login' && token) {
    next({ path: '/' })
  } else {
    next()
  }
})

export default router
