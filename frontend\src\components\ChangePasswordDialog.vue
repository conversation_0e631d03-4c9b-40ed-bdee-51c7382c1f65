<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改密码"
    width="500px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      status-icon
    >
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="form.oldPassword" type="password" show-password />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" type="password" show-password />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" show-password />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认修改
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineExpose } from 'vue';
import { changePassword } from '@/api/auth.js';
import { ElMessage } from 'element-plus';

const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref(null);

const getInitialForm = () => ({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});
const form = ref(getInitialForm());

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'));
  } else if (value !== form.value.newPassword) {
    callback(new Error("两次输入的新密码不一致!"));
  } else {
    callback();
  }
};

const rules = {
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
};

const openDialog = () => {
  dialogVisible.value = true;
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.value = getInitialForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        await changePassword({
          oldPassword: form.value.oldPassword,
          newPassword: form.value.newPassword,
        });
        ElMessage.success('密码修改成功，请重新登录');
        dialogVisible.value = false;
        // 在实际应用中，通常会让用户重新登录
        // 这里可以调用 logout 方法
      } catch (error) {
        // 错误提示已在 request_extra.js 中全局处理
      } finally {
        loading.value = false;
      }
    }
  });
};

// 通过 defineExpose 暴露 openDialog 方法给父组件
defineExpose({
  openDialog,
});
</script> 