import request from '@/utils/request_extra'; // [!重要] 引入封装好的axios实例

// 根据用户ID获取认证信息
export function getUserAuthentication(userId) {
  return request({
    url: `/user-authentications/${userId}`,
    method: 'get'
  });
}

// 创建或更新用户认证信息
// 我们使用 upsert 逻辑，所以用 post 方法，后端来判断是创建还是更新
export function upsertUserAuthentication(userId, data) {
  return request({
    url: `/user-authentications/${userId}`,
    method: 'post',
    data
  });
} 