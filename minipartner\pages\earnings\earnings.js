/**
 * 合伙人小程序收益页面
 * 显示合伙人的订单列表和收益明细
 * 提供订单筛选、搜索等功能
 */

// 引入API模块
const api = require('../../utils/api');

// 合伙人收益页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 收益统计数据
    pending_earnings: '0.00',  // 待发放收益金额
    paid_earnings: '0.00',     // 已发放收益金额
    total_earnings: '0.00',    // 累计总收益金额

    // 订单相关数据
    orderList: [],             // 当前显示的订单列表
    loading: false,            // 页面加载状态
    earningsData: null,        // 收益详细数据

    // 订单数量统计
    orderCounts: {
      total: 0,                // 总订单数
      paid: 0,              // 已结算订单数
      pending: 0               // 待结算订单数
    },

    // 筛选和搜索状态
    currentTab: 0,             // 当前状态标签（0-全部，1-待结算，2-已结算）
    allOrders: [],             // 所有订单缓存（用于筛选）
    filteredOrders: [],        // 经过筛选的订单
    searchKeyword: '',         // 搜索关键词

    // 时间筛选相关
    showFilterPopup: false,    // 是否显示筛选弹窗
    timeFilterType: '',        // 时间筛选类型（recent1m, recent3m等）
    startDate: '',             // 筛选开始日期
    endDate: '',               // 筛选结束日期
    hasTimeFilter: false,      // 是否应用了时间筛选条件
    appliedTimeFilter: {       // 已应用的时间筛选条件
      type: '',
      startDate: '',
      endDate: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      return;
    }

    await this.loadAllOrders();
    await this.loadEarningsData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态和合伙人权限
    if (!this.checkLoginStatus()) {
      return;
    }
  },

  /**
   * 检查登录状态和合伙人权限
   */
  checkLoginStatus() {
    return api.checkPartnerAuth();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function() {
    console.log('触发下拉刷新');

    // 重新加载数据
    await this.loadAllOrders();
    await this.loadEarningsData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();

    // 显示刷新成功提示
    wx.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 加载合伙人相关的所有订单
   */
  async loadAllOrders() {
    try {
      this.setData({ loading: true });

      console.log('开始加载合伙人订单数据');

      // 获取合伙人相关的订单数据
      const res = await api.getPartnerOrders({
        page: 1,
        pageSize: 100
      });

      console.log('订单API响应:', res);

      if (res && res.data) {
        const orders = res.data.orders || res.data.list || [];

        console.log(`获取到${orders.length}个订单`);

        // 格式化每个订单的时间和数据
        const formattedOrders = orders.map(order => {
          // 处理时间格式
          if (order.created_at || order.createTime) {
            order.createTime = this.formatDateTime(order.created_at || order.createTime);
          }

          // 统一订单字段名称，使用正确的数据库字段
          const standardAmount = parseFloat(order.standard_amount || 0);
          const actualAmount = parseFloat(order.actual_amount || 0);
          const commissionAmount = parseFloat(order.commission_amount || 0);
          const commissionBase = parseFloat(order.commission_base || 0);
          const commissionExtra = parseFloat(order.commission_extra || 0);

          // 使用数据库中保存的佣金比例，而不是重新计算
          const totalCommissionRate = commissionBase + commissionExtra;
          const commissionRate = (totalCommissionRate * 100).toFixed(2);

          // 处理产品/服务信息显示
          let displayProductInfo = '';
          let productDetails = [];

          if (order.order_category === '产品订单') {
            // 产品订单：显示产品-版本信息
            displayProductInfo = order.product_name || '未知产品';
            if (order.productItems && order.productItems.length > 0) {
              const productItem = order.productItems[0];
              if (productItem.product) {
                const productName = productItem.product.product_name || '未知产品';
                const version = productItem.product.version_name || '';
                displayProductInfo = version ? `${productName}-${version}` : productName;
              }
            }
            productDetails = [{
              type: 'product',
              name: displayProductInfo
            }];
          } else if (order.order_category === '服务订单') {
            // 服务订单：显示所有服务项目
            if (order.serviceItems && order.serviceItems.length > 0) {
              productDetails = order.serviceItems.map(service => ({
                type: 'service',
                name: service.service_name,
                standardPrice: service.standard_price,
                actualPrice: service.actual_price
              }));
              displayProductInfo = order.serviceItems.map(service => service.service_name).join('、');
            } else {
              displayProductInfo = order.product_name || '未知服务';
              productDetails = [{
                type: 'service',
                name: displayProductInfo
              }];
            }
          } else {
            // 兜底处理
            displayProductInfo = order.product_name || order.productName || '未知产品';
            productDetails = [{
              type: 'unknown',
              name: displayProductInfo
            }];
          }

          return {
            orderId: order.order_id || order.orderId,
            orderCategory: order.order_category || '未知订单',
            productName: displayProductInfo,
            productDetails: productDetails, // 新增：详细的产品/服务信息
            companyName: order.company_name || order.companyName || '未知公司',
            amount: actualAmount.toFixed(2),
            standardPrice: standardAmount.toFixed(2),
            actualPrice: actualAmount.toFixed(2),
            commission: commissionAmount.toFixed(2),
            commissionRate: commissionRate,
            commissionStatus: order.commission_status || '未发放',  // 使用正确的字段
            paymentStatus: order.payment_status || '未支付',       // 使用正确的字段
            createTime: order.createTime,
            ...order
          };
        });

        // 按订单ID去重
        const uniqueOrders = this.removeDuplicates(formattedOrders, 'orderId');

        console.log(`去重后${uniqueOrders.length}个订单`);

        // 设置订单数量统计，使用正确的字段
        const total = uniqueOrders.length;
        const paid = uniqueOrders.filter(order => order.commissionStatus === '已发放').length;
        const pending = total - paid;

        // 设置订单数据
        this.setData({
          allOrders: uniqueOrders,
          filteredOrders: uniqueOrders,
          orderList: uniqueOrders,
          orderCounts: { total, paid, pending }
        });

        // 应用已有的筛选条件
        this.applyAllFilters();
      } else {
        // 没有订单数据
        this.setData({
          allOrders: [],
          filteredOrders: [],
          orderList: [],
          orderCounts: { total: 0, paid: 0, pending: 0 }
        });
      }
    } catch (error) {
      console.error('加载合伙人订单失败:', error);

      // 显示详细错误信息
      let errorMessage = '获取订单失败';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.errMsg) {
        errorMessage = error.errMsg;
      }

      console.error('详细错误信息:', errorMessage);

      wx.showToast({
        title: errorMessage.length > 20 ? '获取订单失败' : errorMessage,
        icon: 'none',
        duration: 3000
      });

      // 设置空数据
      this.setData({
        allOrders: [],
        filteredOrders: [],
        orderList: [],
        orderCounts: { total: 0, paid: 0, pending: 0 }
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载收益概览数据
   * 从后端API获取真实的收益统计数据
   */
  async loadEarningsData() {
    try {
      console.log('加载收益概览数据');

      // 调用后端API获取合伙人收益数据
      const result = await api.getPartnerEarnings();

      if (result.success) {
        const {
          pending_earnings,
          paid_earnings,
          total_earnings
        } = result.data;

        this.setData({
          pending_earnings: pending_earnings || '0.00',
          paid_earnings: paid_earnings || '0.00',
          total_earnings: total_earnings || '0.00'
        });

        console.log('收益概览数据加载完成:', {
          pending_earnings,
          paid_earnings,
          total_earnings
        });
      } else {
        console.error('获取收益数据失败:', result.message);
        // 使用默认数据
        this.setData({
          pending_earnings: '0.00',
          paid_earnings: '0.00',
          total_earnings: '0.00'
        });
      }

    } catch (error) {
      console.error('获取收益数据失败:', error);
      // 使用默认数据
      this.setData({
        pending_earnings: '0.00',
        paid_earnings: '0.00',
        total_earnings: '0.00'
      });
    }
  },

  /**
   * 切换状态标签筛选
   */
  switchTab(e) {
    const tabIndex = parseInt(e.currentTarget.dataset.index);
    
    if (tabIndex === this.data.currentTab) return;
    
    this.setData({ currentTab: tabIndex });
    
    // 应用所有筛选条件
    this.applyAllFilters();
  },

  /**
   * 输入搜索关键词
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 确认搜索
   */
  onSearchConfirm() {
    this.applyAllFilters();
  },

  /**
   * 切换筛选弹窗
   */
  toggleFilterPopup() {
    this.setData({
      showFilterPopup: !this.data.showFilterPopup
    });
  },

  /**
   * 选择时间筛选类型
   */
  selectTimeFilterType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      timeFilterType: type
    });
    
    // 根据选择的类型自动设置日期范围
    this.setAutoDateRange(type);
  },

  /**
   * 根据选择的时间类型自动设置日期范围
   */
  setAutoDateRange(type) {
    const today = new Date();
    let startDate = '';
    let endDate = this.formatDate(today);
    
    if (type === 'recent1m') {
      // 近1个月
      const lastMonth = new Date(today);
      lastMonth.setMonth(today.getMonth() - 1);
      startDate = this.formatDate(lastMonth);
    } else if (type === 'recent3m') {
      // 近3个月
      const last3Month = new Date(today);
      last3Month.setMonth(today.getMonth() - 3);
      startDate = this.formatDate(last3Month);
    } else if (type === 'recent6m') {
      // 近6个月
      const last6Month = new Date(today);
      last6Month.setMonth(today.getMonth() - 6);
      startDate = this.formatDate(last6Month);
    }
    
    this.setData({
      startDate,
      endDate
    });
  },

  /**
   * 开始日期变更
   */
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value,
      timeFilterType: 'custom' // 如果选择了自定义日期，取消其他时间筛选类型
    });
  },

  /**
   * 结束日期变更
   */
  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value,
      timeFilterType: 'custom' // 如果选择了自定义日期，取消其他时间筛选类型
    });
  },

  /**
   * 重置所有筛选条件
   */
  resetFilters() {
    this.setData({
      timeFilterType: '',
      startDate: '',
      endDate: '',
      searchKeyword: '',
      hasTimeFilter: false,
      appliedTimeFilter: {
        type: '',
        startDate: '',
        endDate: ''
      }
    });
    
    // 重新应用筛选器
    this.applyAllFilters();
    
    // 关闭弹窗
    this.toggleFilterPopup();
  },

  /**
   * 应用筛选条件
   */
  applyFilters() {
    const { timeFilterType, startDate, endDate } = this.data;
    
    // 检查是否有有效的时间筛选
    const hasValidTimeFilter = timeFilterType && 
      ((timeFilterType !== 'custom' && timeFilterType) || 
      (timeFilterType === 'custom' && startDate && endDate));
    
    this.setData({
      hasTimeFilter: hasValidTimeFilter,
      appliedTimeFilter: {
        type: timeFilterType,
        startDate,
        endDate
      }
    });
    
    // 应用所有筛选条件
    this.applyAllFilters();
    
    // 关闭弹窗
    this.toggleFilterPopup();
  },

  /**
   * 应用所有筛选条件
   */
  applyAllFilters() {
    let filteredOrders = [...this.data.allOrders];
    
    // 1. 应用时间筛选
    filteredOrders = this.applyTimeFilter(filteredOrders);
    
    // 2. 应用搜索筛选
    filteredOrders = this.applySearchFilter(filteredOrders);
    
    // 更新过滤后的订单总数统计，使用正确的字段
    const total = filteredOrders.length;
    const paid = filteredOrders.filter(order => order.commissionStatus === '已发放').length;
    const pending = total - paid;
    
    this.setData({
      filteredOrders,
      orderCounts: { total, paid, pending }
    });
    
    // 3. 应用状态筛选
    const displayOrders = this.applyStatusFilter(filteredOrders);
    
    // 最终更新显示的订单列表
    this.setData({
      orderList: displayOrders
    });
  },

  /**
   * 应用时间筛选
   */
  applyTimeFilter(orders) {
    const { hasTimeFilter, appliedTimeFilter } = this.data;
    
    if (!hasTimeFilter) {
      return orders;
    }
    
    // 根据筛选类型和日期范围筛选订单
    return orders.filter(order => {
      // 使用正则表达式直接从订单日期字符串中提取日期部分进行比较，避免时区问题
      const orderDateStr = order.createTime || '';
      const dateMatch = orderDateStr.match(/^(\d{4}-\d{2}-\d{2})/);
      
      if (!dateMatch) {
        return true; // 如果无法提取日期，默认包含该订单
      }
      
      const orderDateOnly = dateMatch[1]; // 只取日期部分，例如 "2025-04-02"
      
      // 获取开始日期和结束日期的字符串，仅用于日期比较
      const startDateStr = appliedTimeFilter.startDate || '';
      const endDateStr = appliedTimeFilter.endDate || '';
      
      // 使用字符串比较，避免时区问题
      return (
        (!startDateStr || orderDateOnly >= startDateStr) && 
        (!endDateStr || orderDateOnly <= endDateStr)
      );
    });
  },

  /**
   * 应用状态筛选
   */
  applyStatusFilter(orders) {
    const { currentTab } = this.data;

    // 根据当前选中的状态标签筛选订单，使用正确的字段
    if (currentTab === 0) {
      // 全部
      return orders;
    } else if (currentTab === 1) {
      // 待结算
      return orders.filter(order => order.commissionStatus !== '已发放');
    } else if (currentTab === 2) {
      // 已结算
      return orders.filter(order => order.commissionStatus === '已发放');
    }

    return orders;
  },

  /**
   * 应用搜索筛选
   */
  applySearchFilter(orders) {
    const { searchKeyword } = this.data;
    
    if (!searchKeyword) {
      return orders;
    }
    
    // 简单的关键词匹配
    const keyword = searchKeyword.toLowerCase();
    return orders.filter(order => {
      return (
        (order.orderId && order.orderId.toLowerCase().includes(keyword)) ||
        (order.productName && order.productName.toLowerCase().includes(keyword)) ||
        (order.companyName && order.companyName.toLowerCase().includes(keyword))
      );
    });
  },

  /**
   * 日期格式化
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 解析订单日期
   */
  parseOrderDate(dateStr) {
    if (!dateStr) return null;
    
    try {
      // 处理不同格式的日期字符串
      // 提取日期部分
      const dateMatch = dateStr.match(/^(\d{4}-\d{2}-\d{2})/);
      if (dateMatch) {
        const dateOnly = dateMatch[1]; // 例如 "2025-04-02"
        // 创建UTC日期对象，避免时区问题
        return new Date(`${dateOnly}T00:00:00Z`);
      }
    } catch (e) {
      console.error('日期解析错误:', e);
    }
    
    return null;
  },

  /**
   * 根据指定的key去重数组对象
   */
  removeDuplicates(array, key) {
    return array.filter((item, index, self) => 
      index === self.findIndex((t) => (
        t[key] === item[key]
      ))
    );
  },

  /**
   * 格式化日期时间字符串，并处理时区转换
   */
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    try {
      // 创建Date对象（会自动根据本地时区解析）
      const date = new Date(dateTimeStr);
      
      // 检查是否有效日期
      if (isNaN(date.getTime())) {
        return dateTimeStr; // 无效日期，返回原始字符串
      }
      
      // 格式化为本地时间
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
      console.error('日期解析错误:', e);
      return dateTimeStr;
    }
  }
});