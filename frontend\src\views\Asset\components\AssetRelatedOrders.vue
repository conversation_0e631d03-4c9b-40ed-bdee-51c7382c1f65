<template>
  <div class="asset-related-orders">
    <!-- 关联订单组件 - 处理关联订单列表，支持关联多个订单和联查功能 -->
    
    <!-- 新增资产时的关联订单按钮 -->
    <div v-if="mode === 'create'" class="create-mode">
      <div class="form-section">
        <h4 class="section-title">关联订单</h4>
        <div class="order-link-container">
          <el-button 
            type="primary" 
            @click="showOrderSelector = true"
            :disabled="readonly || !enterpriseId"
          >
            <el-icon><Link /></el-icon>
            关联订单
          </el-button>
          
          <div v-if="!enterpriseId" class="tip-text">
            请先选择企业后再关联订单
          </div>
          
          <!-- 已关联的订单显示 - 使用表格形式 -->
          <div v-if="selectedOrders.length > 0" class="selected-orders">
            <OrderTable
              :orders="selectedOrders"
              :show-actions="true"
              action-type="remove"
              empty-text="暂无关联订单"
              @remove-order="removeOrder"
              style="margin-top: 16px"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 查看模式时的关联订单列表 -->
    <div v-else class="view-mode">
      <div class="form-section">
        <h4 class="section-title">关联订单</h4>

        <!-- 订单列表表格 -->
        <OrderTable
          :orders="relatedOrders"
          :loading="loading"
          :show-actions="true"
          action-type="view"
          empty-text="暂无关联订单"
        />
      </div>
    </div>

    <!-- 订单选择对话框 -->
    <el-dialog
      v-model="showOrderSelector"
      title="选择关联订单"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="order-selector">
        <!-- 搜索过滤 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单ID或备注"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <!-- 可选订单列表 -->
        <OrderTable
          :orders="filteredAvailableOrders"
          :loading="loadingOrders"
          :selectable="true"
          empty-text="暂无可关联的订单"
          @selection-change="handleOrderSelection"
          style="margin-top: 16px"
        />
      </div>
      
      <template #footer>
        <el-button @click="showOrderSelector = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmOrderSelection"
          :disabled="tempSelectedOrders.length === 0"
        >
          确定关联 ({{ tempSelectedOrders.length }})
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Search } from '@element-plus/icons-vue'
import { getOrdersByEnterprise, getOrdersByAsset } from '@/api/order.js'
import OrderTable from './OrderTable.vue'

// Props定义
const props = defineProps({
  // 模式：create-新增资产时关联订单，view-查看已关联订单
  mode: {
    type: String,
    default: 'view',
    validator: (value) => ['create', 'view'].includes(value)
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  // 企业ID（用于过滤订单）
  enterpriseId: {
    type: [Number, String],
    default: null
  },
  // 资产ID（用于过滤订单）
  assetId: {
    type: [Number, String],
    default: null
  },
  // 过滤类型：enterprise-按企业过滤未绑定资产的订单，asset-按资产过滤所有订单
  filterType: {
    type: String,
    default: 'enterprise',
    validator: (value) => ['enterprise', 'asset'].includes(value)
  },
  // 已关联的订单列表（查看模式时使用）
  relatedOrders: {
    type: Array,
    default: () => []
  },
  // 已选择的订单ID列表（新增模式时使用）
  selectedOrderIds: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['orders-change'])



// 状态数据
const loading = ref(false)
const loadingOrders = ref(false)
const showOrderSelector = ref(false)
const searchKeyword = ref('')
const availableOrders = ref([])
const tempSelectedOrders = ref([])
const selectedOrdersCache = ref([]) // 缓存已选择的订单对象

// 计算属性：已选择的订单对象列表
const selectedOrders = computed(() => {
  if (props.mode === 'create') {
    // 从缓存中获取已选择的订单，确保显示的连续性
    return selectedOrdersCache.value.filter(order =>
      props.selectedOrderIds.includes(order.id)
    )
  }
  return []
})

// 计算属性：过滤后的可选订单
const filteredAvailableOrders = computed(() => {
  if (!searchKeyword.value) return availableOrders.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return availableOrders.value.filter(order => 
    order.order_id.toLowerCase().includes(keyword) ||
    (order.remark && order.remark.toLowerCase().includes(keyword))
  )
})

// 加载可关联的订单列表
const loadAvailableOrders = async () => {
  loadingOrders.value = true
  try {
    let orders = []

    if (props.filterType === 'enterprise') {
      // 按企业过滤：获取该企业下未绑定资产的订单
      if (!props.enterpriseId) return
      orders = await getOrdersByEnterprise(props.enterpriseId, {
        unbound: true // 只获取未绑定资产的订单
      })
    } else if (props.filterType === 'asset') {
      // 按资产过滤：获取该资产下的所有订单
      if (!props.assetId) return
      orders = await getOrdersByAsset(props.assetId)
    }

    // 将已选择的订单添加到缓存中（如果还没有的话）
    const selectedOrders = (orders || []).filter(order =>
      props.selectedOrderIds.includes(order.id)
    )
    selectedOrders.forEach(order => {
      if (!selectedOrdersCache.value.some(cached => cached.id === order.id)) {
        selectedOrdersCache.value.push(order)
      }
    })

    // 过滤掉已经选择的订单
    const filteredOrders = (orders || []).filter(order =>
      !props.selectedOrderIds.includes(order.id)
    )

    availableOrders.value = filteredOrders
  } catch (error) {
    console.error('加载可关联订单失败:', error)
    ElMessage.error('加载可关联订单失败')
  } finally {
    loadingOrders.value = false
  }
}



// 移除订单关联
const removeOrder = (orderId) => {
  const newOrderIds = props.selectedOrderIds.filter(id => id !== orderId)
  // 从缓存中移除订单
  selectedOrdersCache.value = selectedOrdersCache.value.filter(order => order.id !== orderId)
  emit('orders-change', newOrderIds)
}

// 处理订单选择
const handleOrderSelection = (selection) => {
  tempSelectedOrders.value = selection
}

// 确认订单选择
const confirmOrderSelection = () => {
  const newOrderIds = tempSelectedOrders.value.map(order => order.id)
  const allOrderIds = [...props.selectedOrderIds, ...newOrderIds]

  // 将新选择的订单添加到缓存中
  const newOrders = tempSelectedOrders.value.filter(order =>
    !selectedOrdersCache.value.some(cached => cached.id === order.id)
  )
  selectedOrdersCache.value = [...selectedOrdersCache.value, ...newOrders]

  emit('orders-change', allOrderIds)
  showOrderSelector.value = false
  tempSelectedOrders.value = []
  searchKeyword.value = ''

  ElMessage.success(`成功关联 ${newOrderIds.length} 个订单`)
}

// 监听企业ID变化
watch(() => props.enterpriseId, (newEnterpriseId) => {
  if (newEnterpriseId && props.mode === 'create') {
    loadAvailableOrders()
  }
}, { immediate: true })

// 监听显示订单选择器
watch(showOrderSelector, (show) => {
  if (show && props.mode === 'create') {
    loadAvailableOrders()
  }
})

// 生命周期
onMounted(() => {
  if (props.mode === 'create' && props.enterpriseId) {
    loadAvailableOrders()
  }
})
</script>

<style scoped>
.asset-related-orders {
  padding: 20px;
}

.form-section {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.order-link-container {
  text-align: center;
  padding: 20px;
}

.tip-text {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.selected-orders {
  margin-top: 20px;
}

.order-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.amount-text {
  font-weight: 600;
  color: #f56c6c;
}

.search-bar {
  margin-bottom: 16px;
}

.order-selector {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
  }
  
  .search-bar .el-input {
    width: 100% !important;
  }
}
</style>
