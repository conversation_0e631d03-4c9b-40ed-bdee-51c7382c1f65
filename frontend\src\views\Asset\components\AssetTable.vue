<template>
  <div class="asset-table">
    <!-- 资产表格 -->
    <el-table 
      :data="assets" 
      :loading="loading"
      border 
      style="width: 100%"
      :empty-text="emptyText"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列（可选） -->
      <el-table-column 
        v-if="selectable" 
        type="selection" 
        width="55" 
      />
      
      <!-- 资产ID列 -->
      <el-table-column prop="asset_id" label="资产ID" width="180">
        <template #default="{ row }">
          <el-button 
            link 
            type="primary" 
            @click="viewAssetDetail(row.id)"
          >
            {{ row.asset_id }}
          </el-button>
        </template>
      </el-table-column>
      
      <!-- 产品名称列 -->
      <el-table-column 
        prop="product.name" 
        label="产品名称" 
        width="200" 
        show-overflow-tooltip
      />

      <!-- 产品版本列 -->
      <el-table-column 
        prop="product_version" 
        label="产品版本" 
        width="120" 
      />

      <!-- 用户数量列 -->
      <el-table-column 
        prop="user_count" 
        label="用户数量" 
        width="100" 
        align="right"
      />

      <!-- 到期时间列 -->
      <el-table-column 
        prop="expire_time" 
        label="到期时间" 
        width="160"
      >
        <template #default="{ row }">
          <span :class="getExpireTimeClass(row.expire_time)">
            {{ formatDateTime(row.expire_time) }}
          </span>
        </template>
      </el-table-column>

      <!-- 状态列 -->
      <el-table-column 
        prop="status" 
        label="状态" 
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      
      <!-- 创建时间列 -->
      <el-table-column 
        prop="createdAt" 
        label="创建时间" 
        width="160"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      
      <!-- 备注列 -->
      <el-table-column 
        prop="remark" 
        label="备注" 
        show-overflow-tooltip 
      />
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="showActions" 
        label="操作" 
        width="120" 
        fixed="right"
      >
        <template #default="{ row }">
          <el-button 
            v-if="actionType === 'view'"
            size="small" 
            type="primary"
            @click="viewAssetDetail(row.id)"
          >
            查看
          </el-button>
          <el-button 
            v-if="actionType === 'remove'"
            size="small" 
            type="danger"
            @click="handleRemoveAsset(row.id)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination" v-if="showPagination && total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/format.js'

// Props
const props = defineProps({
  assets: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  selectable: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: false
  },
  actionType: {
    type: String,
    default: 'view', // 'view' | 'remove'
    validator: (value) => ['view', 'remove'].includes(value)
  },
  showPagination: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['selection-change', 'asset-removed', 'size-change', 'current-change'])

const router = useRouter()

// 状态
const currentPage = ref(1)
const pageSize = ref(20)

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '正常': 'success',
    '即将到期': 'warning',
    '已到期': 'danger',
    '已停用': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取到期时间样式
const getExpireTimeClass = (expireTime) => {
  if (!expireTime) return ''
  
  const now = new Date()
  const expire = new Date(expireTime)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired-time'
  if (diffDays <= 30) return 'warning-time'
  return ''
}

// 查看资产详情
const viewAssetDetail = (assetId) => {
  router.push({ name: 'asset-detail', params: { id: assetId } })
}

// 移除资产
const handleRemoveAsset = async (assetId) => {
  try {
    await ElMessageBox.confirm('确定要移除这个资产吗？', '警告', {
      type: 'warning'
    })
    emit('asset-removed', assetId)
  } catch (error) {
    // 用户取消
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('current-change', page)
}
</script>

<style scoped>
.asset-table {
  width: 100%;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.expired-time {
  color: #f56c6c;
  font-weight: bold;
}

.warning-time {
  color: #e6a23c;
  font-weight: bold;
}
</style>
