// 引入 Express 框架以创建路由
const express = require('express');
// 从我们刚刚创建的控制器文件中，引入获取所有员工的函数
const {
  createEmployee,
  getAllEmployees,
  updateEmployee,
  deleteEmployee,
  getNextEmployeeNumber
} = require('../controllers/employee.controller');
const { employee } = require('../middleware/auth'); // [!] 1. 引入员工认证中间件

// 创建一个新的路由实例
const router = express.Router();

/**
 * 定义员工相关的路由
 * 当有 GET 请求访问根路径 ('/') 时，调用 getAllEmployees 函数处理。
 * 注意：这里的 '/' 实际上是 '/api/employees'，
 * 因为我们稍后会在主文件 (index.js) 中为这个路由设置一个 '/api/employees' 的前缀。
 */
// [!] 2. 为员工管理相关的敏感操作加上权限校验
// 使用新的员工认证中间件，提供更清晰的权限控制
router.post('/', employee.verifyAdmin, createEmployee);
// [!] 3. 修改：GET请求只需要登录即可，不需要管理员权限，以便其他模块（如企业负责人下拉框）能获取员工列表
router.get('/', employee.verifyEmployee, getAllEmployees);

// [新增] 获取下一个可用ID
router.get('/next-id', employee.verifyAdmin, getNextEmployeeNumber);

router.put('/:id', employee.verifyAdmin, updateEmployee);
router.delete('/:id', employee.verifyAdmin, deleteEmployee);

// 导出这个路由，以便在主文件中使用
module.exports = router; 