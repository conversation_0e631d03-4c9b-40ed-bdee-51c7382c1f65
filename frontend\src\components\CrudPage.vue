<script setup>
import { ref, defineEmits } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// -- [新增] 定义组件可以发出的事件 --
const emit = defineEmits(['selection-change']);

// -- 1. Props: 定义组件接收的外部属性 --
const props = defineProps({
  // 页面标题
  title: { type: String, required: true },
  // 表格的列定义
  columns: { type: Array, required: true },
  // 获取列表数据的API方法
  apiList: { type: Function, required: true },
  // 创建数据的API方法
  apiCreate: { type: Function, required: true },
  // 更新数据的API方法
  apiUpdate: { type: Function, required: true },
  // 删除数据的API方法
  apiDelete: { type: Function, required: true },
  // [新增] 是否隐藏行内操作列
  hideRowActions: { type: Boolean, default: false },
  // [新增] 行内操作列的宽度
  rowActionsWidth: { type: Number, default: 220 },
});

// -- 2. 内部响应式状态 --
const tableData = ref([]);
const loading = ref(true);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const selectedItems = ref([]);

// -- 3. 表单状态 --
// 表单数据将通过插槽传入，这里只是一个概念
const form = ref({}); 

// -- 4. 核心逻辑方法 --
const loadData = async () => {
  loading.value = true;
  try {
    const data = await props.apiList();
    tableData.value = data;
    return data; // [修改] 返回获取到的新数据
  } catch (error) {
    ElMessage.error(`获取${props.title}列表失败`);
    return []; // [修改] 失败时返回空数组
  } finally {
    loading.value = false;
  }
};

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
  // [修改] 将 selection-change 事件和选中的数据发射出去
  emit('selection-change', selection);
};

const handleCreate = () => {
  isEditMode.value = false;
  // 重置表单的逻辑需要在父组件中处理或通过事件暴露
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  isEditMode.value = true;
  // 填充表单的逻辑需要在父组件中处理
  form.value = { ...row }; // 这是一个简化的示例
  dialogVisible.value = true;
};

const handleDelete = (id) => {
   ElMessageBox.confirm(`您确定要删除这个${props.title}吗？`, '警告', {
    type: 'warning',
  }).then(async () => {
    try {
      await props.apiDelete(id);
      ElMessage.success('删除成功');
      loadData();
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

/**
 * [新增] 批量删除方法
 * @param {Array} items - 要删除的项的数组
 * @param {String} nameKey - 用于在提示信息中显示的名字的键名
 */
const batchDelete = async (items, nameKey = 'name') => {
  if (!items || items.length === 0) {
    ElMessage.warning('请至少选择一个要删除的项目。');
    return;
  }

  const names = items.map(item => item[nameKey]).join(', ');
  try {
    await ElMessageBox.confirm(`您确定要删除以下${props.title}吗？<br><strong>${names}</strong>`, '批量删除确认', {
      type: 'warning',
      dangerouslyUseHTMLString: true, // 允许在消息中使用HTML
    });
    
    // 创建一个Promise数组，用于并发执行所有删除操作
    const deletePromises = items.map(item => props.apiDelete(item.id));
    
    // 等待所有删除操作完成
    await Promise.all(deletePromises);
    
    ElMessage.success('批量删除成功');
    loadData(); // 重新加载数据
  } catch (error) {
    // 如果用户点击了"取消"，或者API调用失败
    if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除操作失败，部分项目可能未被删除。');
    }
  }
};

// -- 5. 生命周期钩子 --
import { onMounted } from 'vue';
onMounted(loadData);

// -- 6. 暴露方法给父组件 --
defineExpose({
  loadData,
  batchDelete, // 将批量删除方法暴露出去
});

</script>

<template>
  <div class="page-container">
    <div class="action-bar">
      <!-- 插槽允许父组件插入自定义按钮 -->
      <slot name="actions" :selection="selectedItems">
        <el-button type="primary" @click="handleCreate">新增{{ title }}</el-button>
      </slot>
    </div>

    <el-table 
      :data="tableData" 
      v-loading="loading" 
      border 
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" fixed="left" />
      
      <!-- 根据传入的columns动态渲染列 -->
      <el-table-column 
        v-for="col in columns" 
        :key="col.prop"
        :prop="col.prop" 
        :label="col.label" 
        :width="col.width"
        :show-overflow-tooltip="col.showOverflowTooltip"
      >
        <!-- [新增] 检查列是否需要自定义插槽 -->
        <template v-if="col.isSlot" #default="scope">
          <slot :name="`col-${col.prop}`" :row="scope.row"></slot>
        </template>
      </el-table-column>

      <el-table-column v-if="!hideRowActions" label="操作" :width="rowActionsWidth" fixed="right">
        <template #default="scope">
           <!-- 如果父组件提供了 row-actions 插槽，则使用它 -->
           <slot v-if="$slots['row-actions']" name="row-actions" :row="scope.row"></slot>
           <!-- 否则，显示默认的编辑和删除按钮 -->
           <template v-else>
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 弹窗部分也使用插槽，让父组件定义内容和行为 -->
    <slot name="dialog" :visible="dialogVisible" :is-edit="isEditMode" :form-data="form">
        <!-- 默认弹窗内容（可以被覆盖） -->
        <el-dialog v-model="dialogVisible" :title="isEditMode ? `编辑${title}`: `新增${title}`" width="500px">
            <p>请在父组件中定义弹窗内容</p>
             <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary">提交</el-button>
            </template>
        </el-dialog>
    </slot>
  </div>
</template>

<style scoped>
.page-container {
  padding: 20px;
}
.action-bar {
  margin-bottom: 20px;
}
</style> 