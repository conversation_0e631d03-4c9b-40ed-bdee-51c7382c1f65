<!--合伙人认证信息页面-->
<view class="auth-container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="title">认证信息</view>
    <view class="subtitle">完善认证信息，确保佣金正常结算</view>
  </view>

  <!-- 认证状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-icon">🛡️</text>
      <text class="status-title">认证状态</text>
    </view>
    
    <view class="status-content">
      <view class="status-badge status-{{authInfo.status}}">
        <text wx:if="{{authInfo.status === 'pending'}}">待审核</text>
        <text wx:elif="{{authInfo.status === 'approved'}}">已通过</text>
        <text wx:elif="{{authInfo.status === 'rejected'}}">已拒绝</text>
        <text wx:else>未知</text>
      </view>
      
      <view class="status-desc" wx:if="{{authInfo.status === 'pending'}}">
        您的认证信息正在审核中，请耐心等待
      </view>
      <view class="status-desc" wx:elif="{{authInfo.status === 'approved'}}">
        认证已通过，可以正常结算佣金
      </view>
      <view class="status-desc" wx:elif="{{authInfo.status === 'rejected'}}">
        认证被拒绝，请重新提交正确的信息
      </view>
    </view>
  </view>

  <!-- 认证信息表单 -->
  <view class="form-container">
    <!-- 查看模式 -->
    <view wx:if="{{!isEditing}}" class="view-mode">
      <!-- 身份证信息 -->
      <view class="info-section">
        <view class="section-title">身份证信息</view>
        
        <view class="info-item">
          <view class="info-label">身份证号</view>
          <view class="info-value" wx:if="{{authInfo.idCard}}">
            <text>{{authInfo.idCard}}</text>
            <view class="copy-btn" data-text="{{authInfo.idCard}}" bindtap="copyToClipboard">
              复制
            </view>
          </view>
          <view class="info-empty" wx:else>未填写</view>
        </view>
        
        <view class="info-item">
          <view class="info-label">真实姓名</view>
          <view class="info-value" wx:if="{{authInfo.realName}}">
            {{authInfo.realName}}
          </view>
          <view class="info-empty" wx:else>未填写</view>
        </view>
      </view>

      <!-- 银行卡信息 -->
      <view class="info-section">
        <view class="section-title">银行卡信息</view>
        
        <view class="info-item">
          <view class="info-label">银行卡号</view>
          <view class="info-value" wx:if="{{authInfo.bankCard}}">
            <text>{{authInfo.bankCard}}</text>
            <view class="copy-btn" data-text="{{authInfo.bankCard}}" bindtap="copyToClipboard">
              复制
            </view>
          </view>
          <view class="info-empty" wx:else>未填写</view>
        </view>
        
        <view class="info-item">
          <view class="info-label">银行名称</view>
          <view class="info-value" wx:if="{{authInfo.bankName}}">
            {{authInfo.bankName}}
          </view>
          <view class="info-empty" wx:else>未填写</view>
        </view>
      </view>

      <!-- 编辑按钮 -->
      <view class="button-section">
        <button class="edit-btn" bindtap="startEdit">
          编辑认证信息
        </button>
      </view>
    </view>

    <!-- 编辑模式 -->
    <view wx:else class="edit-mode">
      <!-- 身份证信息 -->
      <view class="form-section">
        <view class="section-title">身份证信息</view>
        
        <view class="form-item">
          <view class="label">身份证号 *</view>
          <input 
            class="input" 
            placeholder="请输入身份证号"
            value="{{formData.idCard}}"
            data-field="idCard"
            bindinput="handleInput"
            maxlength="18"
          />
        </view>
      </view>

      <!-- 银行卡信息 -->
      <view class="form-section">
        <view class="section-title">银行卡信息</view>
        
        <view class="form-item">
          <view class="label">银行卡号 *</view>
          <input 
            class="input" 
            placeholder="请输入银行卡号"
            value="{{formData.bankCard}}"
            data-field="bankCard"
            bindinput="handleInput"
            type="number"
            maxlength="19"
          />
        </view>
        
        <view class="form-item">
          <view class="label">银行名称 *</view>
          <input 
            class="input" 
            placeholder="请输入银行名称"
            value="{{formData.bankName}}"
            data-field="bankName"
            bindinput="handleInput"
            maxlength="50"
          />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="button-section">
        <button 
          class="save-btn {{submitting ? 'disabled' : ''}}"
          bindtap="saveAuthInfo"
          disabled="{{submitting}}"
        >
          {{submitting ? '保存中...' : '保存信息'}}
        </button>
        
        <button class="cancel-btn" bindtap="cancelEdit">
          取消
        </button>
      </view>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-card">
    <view class="tips-header">
      <text class="tips-icon">💡</text>
      <text class="tips-title">温馨提示</text>
    </view>
    
    <view class="tips-content">
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">请确保身份证号和银行卡号信息准确无误</text>
      </view>
      
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">银行卡必须是本人实名认证的储蓄卡</text>
      </view>
      
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">信息提交后需要管理员审核，请耐心等待</text>
      </view>
      
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">如有疑问，请联系客服协助处理</text>
      </view>
    </view>
  </view>
</view>
