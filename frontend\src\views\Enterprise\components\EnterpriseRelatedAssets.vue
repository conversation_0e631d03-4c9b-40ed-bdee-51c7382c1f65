<template>
  <div class="related-assets">
    <!-- 资产表格 -->
    <AssetTable
      :assets="relatedAssets"
      :loading="loading"
      :emptyText="emptyText"
      :selectable="false"
      :showActions="true"
      :actionType="'view'"
    />
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue'
import AssetTable from '@/views/Asset/components/AssetTable.vue'

// Props
const props = defineProps({
  enterpriseId: {
    type: [Number, String],
    default: null
  },
  relatedAssets: {
    type: Array,
    default: () => []
  }
})

// 状态
const loading = false

// 计算属性
const emptyText = computed(() => {
  return props.enterpriseId ? '该企业暂无关联资产' : '请先选择企业'
})
</script>

<style scoped>
.related-assets {
  padding: 20px;
}
</style>
