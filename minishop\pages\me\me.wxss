/* pages/me/me.wxss */
@import '/static/fonts/iconfont.wxss';

:host {
  --primary-color: #4A90E2 !important;
  --card-bg: #ffffff;
  --text-primary: #333333;
  --text-secondary: #888888;
  --text-light: #B2B2B2;
  --bg-color: #f7f8fa;
  --border-color: #eeeeee;
}

.page-container {
  background-color: var(--bg-color);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部导航栏预留空间 */
  overflow-x: hidden;
}

/* 顶部头部 */
.header-section {
  position: relative;
  padding: 40rpx 30rpx;
  padding-top: calc(88rpx + 40rpx); /* 适配状态栏高度 */
  box-sizing: border-box;
  color: white;
  z-index: 1;
  margin-bottom: 50rpx; /* 让卡片和头部有重叠感 */
}

.header-bg {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 420rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, #5cb8e6 100%);
  z-index: -1;
  transform: scaleY(1.1);
  transform-origin: top;
}

.user-info-container {
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 60rpx;
}

.avatar-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
}

.avatar-icon {
  font-size: 60rpx;
  color: #fff;
}

.avatar {
  width: 100%;
  height: 100%;
}

.info-text {
  margin-left: 30rpx;
  flex: 1;
}

.user-nickname {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 26rpx;
  opacity: 0.8;
  display: flex;
  align-items: center;
}

.copy-icon {
  font-size: 28rpx;
  margin-left: 16rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.copy-icon:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.settings-icon .iconfont {
  font-size: 44rpx;
  color: white;
  opacity: 0.9;
}

/* 通用卡片样式 - 使用更具体的选择器确保优先级 */
.page-container .card {
  background: white;
  border-radius: 20rpx;
  margin: 24rpx 32rpx !important;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  width: auto !important; /* 覆盖全局样式的width: 100% */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
  padding-top: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.card-more {
  font-size: 26rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.card-more .iconfont {
  font-size: 24rpx;
  margin-left: 4rpx;
}

/* 企业选择器 */
.page-container .enterprise-selector {
  margin: 24rpx 32rpx !important;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

.selector-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
}

.selector-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.selector-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
  color: #1976d2;
}

.selector-info {
  flex: 1;
}

.selector-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 6rpx;
}

.current-enterprise {
  font-size: 30rpx;
  color: white;
  font-weight: 600;
}

.selector-arrow {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 页签容器 */
.page-container .tab-container {
  margin: 24rpx 32rpx !important;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.tab-header {
  display: flex;
  background: #f8f9fa;
  border-radius: 20rpx;
  margin: 8rpx;
  padding: 6rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 16rpx;
  margin: 2rpx;
}

.tab-item.active {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.4);
}

.tab-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 26rpx;
  font-weight: 500;
}

.tab-badge {
  position: absolute;
  top: 8rpx;
  right: 12rpx;
  background: #ff4757;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
  line-height: 1;
}

.tab-item.active .tab-badge {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 内容区域 - assets-section 和 order-section 继承card样式 */

/* 资产筛选 */
.asset-filter {
  display: flex;
  padding: 0 32rpx 24rpx;
  gap: 16rpx;
}

.assets-list {
  padding: 0 32rpx 32rpx;
}

.asset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.asset-item:last-child {
  border-bottom: none;
}

.asset-main {
  flex: 1;
  overflow: hidden;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.asset-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  background-color: #e8f5e9;
  color: #4caf50;
}

.asset-status.active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.asset-status.expired {
  background-color: #ffebee;
  color: #f44336;
}

.asset-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.asset-id, .asset-version {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-right: 20rpx;
}

.asset-details {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.detail-item {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-right: 30rpx;
  margin-bottom: 8rpx;
}

.detail-label {
  color: var(--text-light);
}

.asset-dates {
  display: flex;
  flex-wrap: wrap;
}

.date-item {
  font-size: 26rpx;
  margin-right: 30rpx;
  margin-bottom: 8rpx;
}

.date-label {
  color: var(--text-light);
}

.date-value {
  color: var(--text-secondary);
}

.date-value.normal {
  color: var(--text-secondary);
}

.date-value.warning {
  color: #ff9800;
}

.date-value.expired {
  color: #f44336;
}

.asset-actions {
  padding: 0 10rpx;
}

.asset-actions .iconfont {
  font-size: 32rpx;
  color: var(--text-light);
}

/* 空资产状态 */
.empty-assets {
  text-align: center;
  padding: 60rpx 0;
}

/* 订单中心 - 移除特殊样式，使用统一的card样式，继承card样式，保持一致的边距 */

.order-status-tabs {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 10rpx 0; /* 增加一点上下内边距 */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
  width: 150rpx; /* 给定一个宽度让间距更可控 */
  position: relative; /* 为订单数量添加相对定位 */
}

.tab-item:active {
  transform: scale(0.95);
}

/* 统一处理 iconfont 和原生 icon 的样式 */
.tab-item .iconfont, .tab-item icon {
  font-size: 56rpx;
  margin-bottom: 12rpx;
  color: var(--text-primary);
  line-height: 1; /* 确保原生icon和iconfont垂直对齐 */
}

/* 订单数量标记 */
.order-count {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  background-color: #f44336;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 16rpx;
  padding: 0 6rpx;
  box-sizing: border-box;
}

/* 订单预览 */
.orders-preview {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid var(--border-color);
}

.order-preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.order-type {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.order-summary {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 空订单状态 */
.empty-orders {
  text-align: center;
  padding: 40rpx 0;
}

/* 通用空状态样式 */
.empty-icon-wrapper {
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: var(--text-primary);
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 26rpx;
  color: var(--text-light);
}

/* 功能列表 */
.menu-section, .about-section {
  margin-top: 30rpx; /* 不再与头部重叠，恢复正常边距 */
  padding: 10rpx 30rpx;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
    background-color: #fafafa;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-left .iconfont {
  font-size: 40rpx;
  color: var(--primary-color);
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.item-name {
  font-size: 30rpx;
  color: var(--text-primary);
}

.item-right .iconfont {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 订单筛选 */
.order-filter {
  display: flex;
  padding: 0 32rpx 24rpx;
  gap: 12rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
}

.filter-text {
  font-size: 24rpx;
  font-weight: 500;
}

.filter-badge {
  position: absolute;
  top: 6rpx;
  right: 8rpx;
  background: #ff4757;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
  line-height: 1;
}

.filter-item.active .filter-badge {
  background: rgba(255, 255, 255, 0.3);
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 0 32rpx 32rpx;
}

.order-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 6rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  background: #f0f1f2;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-number {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.order-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.order-status.paid {
  background: #e8f5e8;
  color: #52c41a;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-product {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.order-amount {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 700;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-date {
  font-size: 24rpx;
  color: #999;
}

.order-actions {
  color: #999;
  font-size: 24rpx;
}



/* 企业选择弹窗 */
.enterprise-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 通用模态框样式 */
.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #999;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: #e0e0e0;
}

.enterprise-list {
  max-height: 60vh;
  overflow-y: auto;
}

.enterprise-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: background 0.3s ease;
}

.enterprise-option:last-child {
  border-bottom: none;
}

.enterprise-option:active {
  background: #f8f9fa;
}

.enterprise-option.selected {
  background: #f0f8ff;
}

.enterprise-info {
  flex: 1;
}

.enterprise-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.enterprise-desc {
  font-size: 24rpx;
  color: #999;
}

.enterprise-check {
  color: #667eea;
  font-size: 32rpx;
}




/* 用户昵称容器样式 */
.user-nickname-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.edit-icon {
  margin-left: 16rpx;
  font-size: 28rpx;
  opacity: 0.8;
  padding: 8rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.edit-icon:active {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 用户编辑弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-body {
  padding: 40rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: white;
}

.modal-footer {
  display: flex;
  padding: 20rpx 40rpx 40rpx;
  gap: 20rpx;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-cancel:active {
  background-color: #e0e0e0;
}

.btn-confirm {
  background-color: var(--primary-color);
  color: white;
}

.btn-confirm:active {
  background-color: #3a7bc8;
}

