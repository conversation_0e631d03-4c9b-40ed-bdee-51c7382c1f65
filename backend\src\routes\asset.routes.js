const express = require('express');
const router = express.Router();
const assetController = require('../controllers/asset.controller.js');
const { employee } = require('../middleware/auth');
const { verifyToken, requireUserType } = require('../middleware/auth/base');

// --- 定义资产相关的路由 ---

// GET /api/assets - 获取所有资产列表（员工和用户都可以访问）
router.get('/', [verifyToken, requireUserType(['employee', 'user'])], assetController.getAllAssets);

// [新增] 获取下一个可用ID
router.get('/next-id', employee.verifyEmployee, assetController.getNextAssetId);



// [新增] 获取下一个资产变更ID
router.get('/changes/next-id', employee.verifyEmployee, assetController.getNextAssetChangeId);

// [新增] 获取所有资产变更记录列表
router.get('/changes', employee.verifyEmployee, assetController.getAllAssetChanges);

// [新增] 获取单个变更记录详情
router.get('/changes/:changeId', employee.verifyEmployee, assetController.getAssetChangeById);

// [新增] 创建资产变更记录
router.post('/changes', employee.verifyEmployee, assetController.createAssetChange);

// POST /api/assets - 创建一个新资产
router.post('/', employee.verifyEmployee, assetController.createAsset);

// GET /api/assets/:id - 获取单个资产的详细信息（员工和用户都可以访问）
router.get('/:id', [verifyToken, requireUserType(['employee', 'user'])], assetController.getAssetById);

// [新增] GET /api/assets/:assetId/changes - 获取单个资产的变更记录
router.get('/:assetId/changes', employee.verifyEmployee, assetController.getChangesByAssetId);

// [新增] GET /api/assets/:assetId/orders - 获取单个资产的关联订单
router.get('/:assetId/orders', employee.verifyEmployee, assetController.getOrdersByAssetId);

// PUT /api/assets/:id - "修改"资产 (普通更新)
router.put('/:id', employee.verifyEmployee, assetController.updateAsset);

// DELETE /api/assets/changes/:changeId - "回滚"资产变更
router.delete('/changes/:changeId', employee.verifyEmployee, assetController.revertAssetChange);

// DELETE /api/assets/:id - 删除一个资产
router.delete('/:id', employee.verifyAdmin, assetController.deleteAsset);


module.exports = router; 