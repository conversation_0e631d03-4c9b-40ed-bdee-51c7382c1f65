<view class="promo-component-container" wx:if="{{promoInfo && renderData}}">
  <!-- 悬浮式促销卡片 - 现代设计 -->
  <view class="promo-card-container" 
        wx:if="{{displayMode === 'normal'}}">
    <view class="promo-card-float {{isLoaded ? 'enter' : ''}}"
          bindtap="handleJoinPromo"
          style="background: {{themeStyle}}">
      <!-- 背景装饰元素 -->
      <view class="card-decoration-circle circle-1"></view>
      <view class="card-decoration-circle circle-2"></view>
      <view class="card-decoration-dots"></view>
      <view class="card-decoration-line line-1"></view>
      <view class="card-decoration-line line-2"></view>
      
      <!-- 礼物图标 -->
      <view class="gift-icon-wrapper">
        <view class="gift-icon-glow"></view>
        <view class="gift-icon">🎁</view>
      </view>
      
      <!-- 促销信息 -->
      <view class="promo-content">
        <view class="promo-title">
          <view class="promo-label">{{customTitle || '限时特惠'}}</view>
          <view class="highlight-text">{{renderData.discountText}}<text class="discount-unit">折</text></view>
        </view>
        <view class="promo-discount">
          <text class="price-original">原价¥{{renderData.originalPrice}}</text>
          <text class="price-now">现价¥{{renderData.currentPrice}}</text>
        </view>
      </view>
      
      <!-- 箭头指示 -->
      <view class="arrow-indicator">
        <text class="arrow-icon">→</text>
      </view>
    </view>
  </view>
</view> 