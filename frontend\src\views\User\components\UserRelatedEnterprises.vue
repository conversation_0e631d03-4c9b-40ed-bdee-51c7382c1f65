<template>
  <div class="related-enterprises">
    <!-- 企业表格 -->
    <EnterpriseTable
      :enterprises="relatedEnterprises"
      :loading="loading"
      :emptyText="emptyText"
      :selectable="false"
      :showActions="true"
      :actionType="'view'"
    />
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue'
import EnterpriseTable from '@/views/Enterprise/components/EnterpriseTable.vue'

// Props
const props = defineProps({
  userId: {
    type: [Number, String],
    default: null
  },
  relatedEnterprises: {
    type: Array,
    default: () => []
  }
})

// 状态
const loading = false

// 计算属性
const emptyText = computed(() => {
  return props.userId ? '该用户暂无关联企业' : '请先选择用户'
})
</script>

<style scoped>
.related-enterprises {
  padding: 20px;
}
</style>
