<template>
  <div class="tabs-view-container">
    <!-- 标签页导航栏 -->
    <div class="tabs-nav" v-if="tabsState.tabs.length > 0">
      <el-tabs
        v-model="tabsState.activeTab"
        type="card"
        class="tabs-nav-tabs"
        @tab-click="handleTabClick"
        @tab-remove="handleTabRemove"
      >
        <el-tab-pane
          v-for="tab in tabsState.tabs"
          :key="tab.name"
          :label="tab.title"
          :name="tab.name"
          :closable="tab.closable"
        >
          <!-- 标签页内容在这里不渲染，由下面的router-view处理 -->
        </el-tab-pane>
      </el-tabs>

      <!-- 标签页操作按钮组 -->
      <div class="tabs-actions">
        <el-tooltip content="关闭其他" placement="bottom">
          <el-button
            type="primary"
            size="small"
            :icon="Close"
            circle
            @click="closeOtherTabs(tabsState.activeTab)"
          />
        </el-tooltip>
        <el-tooltip content="关闭所有" placement="bottom">
          <el-button
            type="danger"
            size="small"
            :icon="CircleClose"
            circle
            @click="closeAllTabs"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 页面内容区域 -->
    <div class="tabs-content">
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="tabsState.cachedViews">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup>
import { watch, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useTabs } from '@/store/tabs';
import {
  Close,
  CircleClose
} from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();
const {
  state: tabsState,
  addTab,
  closeTab,
  closeOtherTabs,
  closeAllTabs,
  setActiveTab,
  initTabs,
  autoSave
} = useTabs();

// 处理标签页点击
function handleTabClick(tab) {
  const targetTab = tabsState.tabs.find(t => t.name === tab.paneName);
  if (targetTab) {
    setActiveTab(targetTab.name);
    router.push(targetTab.fullPath);
  }
}

// 处理标签页关闭
function handleTabRemove(tabName) {
  closeTab(tabName);
}



// 监听路由变化，自动添加标签页
watch(
  () => route.fullPath,
  () => {
    // 排除登录页等不需要标签页的页面
    if (route.path === '/login' || route.path === '/') {
      return;
    }

    // 检查当前路由的meta，而不是父路由的meta
    const currentRouteRecord = route.matched[route.matched.length - 1];

    if (currentRouteRecord?.meta?.noTab) {
      return;
    }

    // 检查是否是重定向路由（没有组件的路由）
    if (route.matched.length === 0 || !route.matched.some(match => match.components)) {
      return;
    }

    // 检查路由是否有名称或标题
    if (!route.name && !currentRouteRecord?.meta?.title) {
      return;
    }

    addTab(route);
    autoSave(); // 自动保存状态
  },
  { immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  initTabs();
  
  // 如果当前路由不在标签页中，添加它
  if (route.meta?.noTab !== true && route.path !== '/login') {
    addTab(route);
  }
});

// 组件卸载时保存状态
onUnmounted(() => {
  autoSave();
});
</script>

<style scoped>
.tabs-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs-nav {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  min-height: 40px;
  position: relative;
}

.tabs-nav-tabs {
  flex: 1;
  margin-right: 80px; /* 为两个圆形图标按钮预留空间 */
  overflow: hidden; /* 防止标签页溢出 */
}

.tabs-nav-tabs :deep(.el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

.tabs-nav-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 16px; /* 左侧对齐内容区域 */
  overflow: hidden; /* 确保不会溢出到操作按钮区域 */
}

.tabs-nav-tabs :deep(.el-tabs__nav-scroll) {
  overflow: hidden; /* Element Plus 会自动处理滚动 */
}

.tabs-nav-tabs :deep(.el-tabs__item) {
  height: 36px;
  line-height: 36px;
  margin-right: 4px;
  border: 1px solid #d9d9d9;
  border-radius: 4px 4px 0 0;
  padding: 0 16px;
  font-size: 14px; /* 增大字体 */
  color: #666;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.tabs-nav-tabs :deep(.el-tabs__item.is-active) {
  background: #fff;
  color: #409eff;
  border-bottom: 1px solid #fff;
  border-top: 2px solid #409eff;
  font-weight: 500; /* 活跃标签页字体加粗 */
}

.tabs-nav-tabs :deep(.el-tabs__item:hover) {
  color: #409eff;
}

.tabs-nav-tabs :deep(.el-tabs__nav-next),
.tabs-nav-tabs :deep(.el-tabs__nav-prev) {
  line-height: 36px;
  color: #909399;
}

.tabs-actions {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 6px; /* 圆形按钮之间的间距 */
  background: #fff;
  z-index: 10;
}

/* 圆形图标按钮样式调整 */
.tabs-actions :deep(.el-button.is-circle) {
  width: 28px;
  height: 28px;
  padding: 0;
  font-size: 14px;
}

.tabs-actions :deep(.el-button--small.is-circle) {
  width: 28px;
  height: 28px;
  padding: 0;
  font-size: 14px;
}

.tabs-content {
  flex: 1;
  overflow: auto; /* 允许内容区域滚动 */
  background: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-nav-tabs {
    margin-right: 70px; /* 小屏幕上为两个圆形按钮预留空间 */
  }

  .tabs-nav-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 8px;
  }

  .tabs-nav-tabs :deep(.el-tabs__item) {
    padding: 0 8px;
    font-size: 12px;
  }

  .tabs-actions {
    right: 8px;
  }

  .tabs-actions :deep(.el-button.is-circle) {
    width: 26px;
    height: 26px;
    font-size: 12px;
  }

  .tabs-actions :deep(.el-button--small.is-circle) {
    width: 26px;
    height: 26px;
    font-size: 12px;
  }
}
</style>
