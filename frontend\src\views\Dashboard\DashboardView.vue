<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据面板</h1>
      <p class="page-subtitle">系统综合数据概览</p>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <!-- 企业信息卡片 -->
      <el-card class="stat-card enterprise-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><OfficeBuilding /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ dashboardData.enterprises.total }}</div>
            <div class="stat-label">企业总数</div>
            <div class="stat-detail">
              <span class="detail-item">活跃: {{ dashboardData.enterprises.active }}</span>
              <span class="detail-item">待审核: {{ dashboardData.enterprises.pending }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 用户信息卡片 -->
      <el-card class="stat-card user-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ dashboardData.users.total }}</div>
            <div class="stat-label">用户总数</div>
            <div class="stat-detail">
              <span class="detail-item">本月新增: {{ dashboardData.users.monthlyNew }}</span>
              <span class="detail-item">活跃用户: {{ dashboardData.users.active }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 合伙人信息卡片 -->
      <el-card class="stat-card partner-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><Avatar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ dashboardData.partners.total }}</div>
            <div class="stat-label">合伙人总数</div>
            <div class="stat-detail">
              <span class="detail-item">认证通过: {{ dashboardData.partners.verified }}</span>
              <span class="detail-item">待认证: {{ dashboardData.partners.pending }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 资产信息卡片 -->
      <el-card class="stat-card asset-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><Box /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ dashboardData.assets.total }}</div>
            <div class="stat-label">资产总数</div>
            <div class="stat-detail">
              <span class="detail-item">总价值: ¥{{ formatNumber(dashboardData.assets.totalValue) }}</span>
              <span class="detail-item">本月变更: {{ dashboardData.assets.monthlyChanges }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 订单信息卡片 -->
      <el-card class="stat-card order-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ dashboardData.orders.total }}</div>
            <div class="stat-label">订单总数</div>
            <div class="stat-detail">
              <span class="detail-item">本月订单: {{ dashboardData.orders.monthly }}</span>
              <span class="detail-item">待审核: {{ dashboardData.orders.pending }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 佣金信息卡片 -->
      <el-card class="stat-card commission-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ formatNumber(dashboardData.commissions.total) }}</div>
            <div class="stat-label">佣金总额</div>
            <div class="stat-detail">
              <span class="detail-item">本月佣金: ¥{{ formatNumber(dashboardData.commissions.monthly) }}</span>
              <span class="detail-item">待结算: ¥{{ formatNumber(dashboardData.commissions.pending) }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 趋势图表 -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="chart-header">
                <span>用户增长趋势</span>
                <el-select v-model="trendPeriod" size="small" style="width: 120px">
                  <el-option label="最近7天" value="7days" />
                  <el-option label="最近30天" value="30days" />
                  <el-option label="最近3个月" value="3months" />
                </el-select>
              </div>
            </template>
            <div ref="trendChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 分布图表 -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <span>订单状态分布</span>
            </template>
            <div ref="pieChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="table-card" shadow="hover">
            <template #header>
              <div class="table-header">
                <span>最近活动</span>
                <el-button type="primary" link @click="viewAllActivities">查看全部</el-button>
              </div>
            </template>
            <el-table :data="recentActivities" style="width: 100%">
              <el-table-column prop="type" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getActivityTagType(row.type)">{{ row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="user" label="操作人" width="120" />
              <el-table-column prop="time" label="时间" width="180" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  OfficeBuilding,
  User,
  Avatar,
  Box,
  Document,
  Money
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getDashboardStats,
  getUserGrowthTrend,
  getOrderStatusDistribution,
  getRecentActivities
} from '@/api/dashboard'

// 响应式数据
const dashboardData = ref({
  enterprises: {
    total: 0,
    active: 0,
    pending: 0
  },
  users: {
    total: 0,
    monthlyNew: 0,
    active: 0
  },
  partners: {
    total: 0,
    verified: 0,
    pending: 0
  },
  assets: {
    total: 0,
    totalValue: 0,
    monthlyChanges: 0
  },
  orders: {
    total: 0,
    monthly: 0,
    pending: 0
  },
  commissions: {
    total: 0,
    monthly: 0,
    pending: 0
  }
})

const recentActivities = ref([])
const trendPeriod = ref('30days')

// 图表引用
const trendChartRef = ref(null)
const pieChartRef = ref(null)
let trendChart = null
let pieChart = null

// 数字格式化
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

// 获取活动标签类型
const getActivityTagType = (type) => {
  const typeMap = {
    '新增企业': 'success',
    '新增用户': 'primary',
    '订单审核': 'warning',
    '资产变更': 'info',
    '佣金结算': 'success'
  }
  return typeMap[type] || 'info'
}

// 查看全部活动
const viewAllActivities = () => {
  // 这里可以跳转到活动日志页面
  console.log('查看全部活动')
}

// 初始化趋势图表
const initTrendChart = async () => {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)

  try {
    // 获取真实的趋势数据
    const response = await getUserGrowthTrend(trendPeriod.value)
    if (response.data.success) {
      const data = response.data.data

      // 处理日期和数据
      const dates = []
      const userCounts = []
      const enterpriseCounts = []
      const orderCounts = []

      // 创建日期映射
      const dateMap = new Map()

      // 处理用户数据
      data.users.forEach(item => {
        dateMap.set(item.date, { users: parseInt(item.user_count), enterprises: 0, orders: 0 })
      })

      // 处理企业数据
      data.enterprises.forEach(item => {
        if (dateMap.has(item.date)) {
          dateMap.get(item.date).enterprises = parseInt(item.enterprise_count)
        } else {
          dateMap.set(item.date, { users: 0, enterprises: parseInt(item.enterprise_count), orders: 0 })
        }
      })

      // 处理订单数据
      data.orders.forEach(item => {
        if (dateMap.has(item.date)) {
          dateMap.get(item.date).orders = parseInt(item.order_count)
        } else {
          dateMap.set(item.date, { users: 0, enterprises: 0, orders: parseInt(item.order_count) })
        }
      })

      // 排序并提取数据
      const sortedDates = Array.from(dateMap.keys()).sort()
      sortedDates.forEach(date => {
        const item = dateMap.get(date)
        dates.push(date)
        userCounts.push(item.users)
        enterpriseCounts.push(item.enterprises)
        orderCounts.push(item.orders)
      })

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['用户注册', '企业注册', '订单数量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '用户注册',
            type: 'line',
            data: userCounts
          },
          {
            name: '企业注册',
            type: 'line',
            data: enterpriseCounts
          },
          {
            name: '订单数量',
            type: 'line',
            data: orderCounts
          }
        ]
      }

      trendChart.setOption(option)
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    // 如果API失败，使用默认图表
    const defaultOption = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['用户注册', '企业注册', '订单数量'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', boundaryGap: false, data: ['暂无数据'] },
      yAxis: { type: 'value' },
      series: [
        { name: '用户注册', type: 'line', data: [0] },
        { name: '企业注册', type: 'line', data: [0] },
        { name: '订单数量', type: 'line', data: [0] }
      ]
    }
    trendChart.setOption(defaultOption)
  }
}

// 初始化饼图
const initPieChart = async () => {
  if (!pieChartRef.value) return

  pieChart = echarts.init(pieChartRef.value)

  try {
    // 获取真实的订单状态分布数据
    const response = await getOrderStatusDistribution()
    if (response.data.success) {
      const data = response.data.data

      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '订单状态',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      pieChart.setOption(option)
    }
  } catch (error) {
    console.error('获取订单状态分布失败:', error)
    // 如果API失败，使用默认图表
    const defaultOption = {
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [{
        name: '订单状态',
        type: 'pie',
        radius: '50%',
        data: [{ value: 1, name: '暂无数据' }],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    pieChart.setOption(defaultOption)
  }
}

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    // 调用真实的API获取统计数据
    const statsResponse = await getDashboardStats()

    // 由于axios响应拦截器返回了response.data，所以statsResponse就是后端返回的数据
    if (statsResponse && statsResponse.success) {
      const data = statsResponse.data

      dashboardData.value = {
        enterprises: {
          total: data.enterprises?.total || 0,
          active: data.enterprises?.active || 0,
          pending: data.enterprises?.pending || 0
        },
        users: {
          total: data.users?.total || 0,
          monthlyNew: data.users?.monthly_new || 0,
          active: data.users?.active || 0
        },
        partners: {
          total: data.partners?.total || 0,
          verified: data.partners?.verified || 0,
          pending: data.partners?.pending || 0
        },
        assets: {
          total: data.assets?.total || 0,
          totalValue: data.assets?.total_value || 0,
          monthlyChanges: data.assets?.monthly_changes || 0
        },
        orders: {
          total: data.orders?.total || 0,
          monthly: data.orders?.monthly || 0,
          pending: data.orders?.pending || 0
        },
        commissions: {
          total: data.commissions?.total || 0,
          monthly: data.commissions?.monthly || 0,
          pending: data.commissions?.pending || 0
        }
      }
    } else {
      console.error('API返回失败:', statsResponse)
      ElMessage.error('获取统计数据失败')
    }

    // 获取最近活动数据
    const activitiesResponse = await getRecentActivities(5)
    if (activitiesResponse.data.success) {
      recentActivities.value = activitiesResponse.data.data.map(activity => ({
        type: activity.type,
        description: activity.description,
        user: activity.user,
        time: new Date(activity.time).toLocaleString('zh-CN')
      }))
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    ElMessage.error('获取仪表板数据失败')
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (trendChart) {
    trendChart.resize()
  }
  if (pieChart) {
    pieChart.resize()
  }
}

// 监听趋势周期变化
watch(trendPeriod, () => {
  if (trendChart) {
    initTrendChart()
  }
})

// 组件挂载
onMounted(async () => {
  await fetchDashboardData()

  nextTick(() => {
    initTrendChart()
    initPieChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载时清理
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
  }
  if (pieChart) {
    pieChart.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
}

.enterprise-card .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-card .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.partner-card .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.asset-card .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.order-card .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.commission-card .stat-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-detail {
  display: flex;
  gap: 16px;
}

.detail-item {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.charts-section {
  margin-top: 30px;
}

.chart-card,
.table-card {
  border-radius: 12px;
  border: none;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .stat-detail {
    justify-content: center;
  }
}
</style>
