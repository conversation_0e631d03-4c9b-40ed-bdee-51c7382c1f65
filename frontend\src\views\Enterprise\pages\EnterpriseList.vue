<script setup>
import { ref, onMounted, computed } from 'vue';
import CrudPage from '@/components/CrudPage.vue';
import EnterpriseFormHeader from '../components/EnterpriseFormHeader.vue';
import { getEnterprises, createEnterprise, updateEnterprise, deleteEnterprise, getNextEnterpriseId } from '@/api/enterprise.js';
import { getEmployees } from '@/api/employee.js';
import { getUsers } from '@/api/user.js';
import { useAuth } from '@/store/auth.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDateTime } from '@/utils/format.js';
import service from '@/utils/request_extra.js';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 1. CrudPage所需配置
const crudPageRef = ref(null);
const api = {
  list: getEnterprises,
  create: createEnterprise,
  update: updateEnterprise,
  delete: deleteEnterprise,
};

const columns = ref([
  { prop: 'id', label: '自增ID', width: 80 },
  { prop: 'enterprise_id', label: '企业ID', width: 120, sortable: true },
  { prop: 'name', label: '企业名称', width: 250, showOverflowTooltip: true, sortable: true, isSlot: true },
  { prop: 'invoice_info', label: '开票信息', width: 200, isSlot: true },
  { prop: 'license_image', label: '营业执照', width: 100, isSlot: true },
  { prop: 'address', label: '地址', width: 220, showOverflowTooltip: true },
  { prop: 'contact_person', label: '联系人', width: 120 },
  { prop: 'contact_phone', label: '联系电话', width: 150 },
  { prop: 'employee.name', label: '负责人', width: 120 },
  { prop: 'user.name', label: '关联用户', width: 120 },
  { prop: 'createdAt', label: '创建时间', width: 180, isSlot: true },
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
]);

// 2. 页面和弹窗状态
const { state: authState } = useAuth();
const isAdmin = computed(() => authState.user?.role === 'admin');
const selectedItems = ref([]);
const employeesList = ref([]);
const usersList = ref([]);
const dialogVisible = ref(false);
const dialogMode = ref('create'); // 'create' | 'edit'
const isSubmitting = ref(false);
const uploadRef = ref(null);
const licenseFile = ref(null);
const formHeaderRef = ref(null);

const getInitialForm = () => ({
  enterprise_id: '', name: '', tax_number: '', bank_name: '', bank_account: '',
  invoice_type: '普票', address: '', contact_person: '', contact_phone: '',
  employee_id: null, user_id: null, remark: '', license_image: ''
});
const form = ref(getInitialForm());

// 3. 计算属性 (用于控制按钮禁用状态)
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);
const isViewDisabled = computed(() => selectedItems.value.length !== 1);
const ownerNameForDisplay = computed(() => {
    if (isAdmin.value || !form.value.employee_id) return '';
    const owner = employeesList.value.find(emp => emp.id === form.value.employee_id);
    return owner ? owner.name : '未知员工';
});

// 4. 方法
const loadDependencies = async () => {
  try {
    const [employeesRes, usersRes] = await Promise.all([getEmployees(), getUsers()]);
    employeesList.value = employeesRes;
    usersList.value = usersRes;
  } catch (error) {
    ElMessage.error('获取员工或用户列表失败: ' + (error.response?.data?.message || error.message));
  }
};

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const openDialog = async (mode, data = null) => {
  dialogMode.value = mode;
  licenseFile.value = null;
  uploadRef.value?.clearFiles();

  // 确保在打开对话框前加载依赖数据
  if (employeesList.value.length === 0 || usersList.value.length === 0) {
    await loadDependencies();
  }

  if (mode === 'create' || mode === 'copy') {
    form.value = data || getInitialForm();
    if (mode === 'create' && !isAdmin.value) {
      form.value.employee_id = authState.user.id;
    }
    // For both create and copy, we get a new ID.
    (async () => {
      try {
        const res = await getNextEnterpriseId();
        form.value.enterprise_id = res.next_id;
        if (mode === 'copy') {
          // If copying, we need to ensure original data is preserved but with new ID
          const originalData = { ...data };
          delete originalData.id;
          delete originalData.createdAt;
          delete originalData.updatedAt;
          form.value = { ...originalData, enterprise_id: res.next_id };
        }
      } catch (error) {
        ElMessage.error('获取新企业ID失败，请手动输入一个唯一的ID。');
      }
    })();
  } else { // 'edit' mode
    form.value = { ...data, employee_id: data.employee?.id, user_id: data.user?.id };
  }
  dialogVisible.value = true;
};

// 新增复制处理方法
const handleCopy = async () => {
    if(isCopyDisabled.value) return;
    const original = selectedItems.value[0];
    openDialog('copy', original);
};

const handleBatchDelete = () => {
    crudPageRef.value?.batchDelete(selectedItems.value, 'name');
};

// 查看企业详情
const handleView = () => {
  if (isViewDisabled.value) return;
  const enterprise = selectedItems.value[0];
  router.push({ name: 'enterprise-detail', params: { id: enterprise.id } });
};

const handleSubmit = async () => {
  // 表单验证
  const isValid = await formHeaderRef.value?.validate();
  if (!isValid) return;

  isSubmitting.value = true;
  try {
    if (!isAdmin.value) {
      form.value.employee_id = authState.user.id;
    }

    const formData = new FormData();
    for (const key in form.value) {
      if (form.value[key] !== null && form.value[key] !== undefined) {
        formData.append(key, form.value[key]);
      }
    }
    if (licenseFile.value) {
      formData.append('license_image', licenseFile.value);
    }

    if (dialogMode.value === 'edit') {
      await api.update(form.value.id, formData);
      ElMessage.success('更新成功');
    } else {
      await api.create(formData);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message}`);
  } finally {
    isSubmitting.value = false;
  }
};

const handleLicenseFileChange = (file) => {
  // 修复：直接使用file.raw，如果不存在则使用file本身
  licenseFile.value = file.raw || file;
};

// 处理营业执照下载的方法
const handleDownloadLicense = (row) => {
  if (!row.id || !row.license_image) {
    ElMessage.warning('没有可供下载的营业执照');
    return;
  }
  const baseURL = service.defaults.baseURL || '';
  const token = localStorage.getItem('authToken');
  const downloadUrl = `${baseURL}/enterprises/${row.id}/license/download?token=${token}`;
  window.open(downloadUrl, '_blank');
};

onMounted(() => {
  loadDependencies();
});
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="企业"
    :api-list="api.list"
    :api-delete="api.delete"
    :api-create="api.create"
    :api-update="api.update"
    :columns="columns"
    :hide-row-actions="true" 
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="openDialog('create')">新增企业</el-button>
      <el-button type="success" @click="handleView" :disabled="isViewDisabled">查看详情</el-button>
      <el-button @click="openDialog('edit', selectedItems[0])" :disabled="isEditDisabled">修改</el-button>
      <el-button type="info" @click="handleCopy" :disabled="isCopyDisabled">复制</el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="isDeleteDisabled">删除</el-button>
    </template>
    
    <!-- 2. 自定义列渲染 -->
    <template #col-name="{ row }">
      <el-link type="primary" @click="router.push({ name: 'enterprise-detail', params: { id: row.id } })">{{ row.name }}</el-link>
    </template>
    <template #col-invoice_info="{ row }">
      <el-popover placement="top-start" title="开票信息详情" :width="250" trigger="hover">
        <template #reference><div style="cursor: pointer;">税号: {{ row.tax_number || 'N/A' }}</div></template>
        <div>
          <p><strong>税号:</strong> {{ row.tax_number || '未提供' }}</p>
          <p><strong>开户行:</strong> {{ row.bank_name || '未提供' }}</p>
          <p><strong>银行账户:</strong> {{ row.bank_account || '未提供' }}</p>
          <p><strong>开票类型:</strong> {{ row.invoice_type || '未提供' }}</p>
        </div>
      </el-popover>
    </template>
    <!-- 营业执照列的渲染 -->
    <template #col-license_image="{ row }">
        <el-button
          v-if="row.license_image"
          type="primary"
          link
          @click="handleDownloadLicense(row)"
        >
          下载
        </el-button>
        <span v-else>未上传</span>
    </template>
    <template #col-createdAt="{ row }">{{ formatDateTime(row.createdAt) }}</template>

    <!-- 3. 对话框 (使用#dialog插槽) -->
    <template #dialog>
      <el-dialog
        :model-value="dialogVisible"
        :title="dialogMode === 'edit' ? '修改企业' : (dialogMode === 'copy' ? '复制企业' : '新增企业')"
        width="800px"
        @close="dialogVisible = false"
      >
        <EnterpriseFormHeader
          :formData="form"
          :readonly="false"
          :isEditMode="dialogMode === 'edit'"
          :employeeOptions="employeesList"
          :userOptions="usersList"
          :isAdmin="isAdmin"
          :ownerNameForDisplay="ownerNameForDisplay"
          @license-change="handleLicenseFileChange"
          ref="formHeaderRef"
        />
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">确定</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>
</template>

<style scoped>
/* 可以保留或删除原有样式 */
</style>
