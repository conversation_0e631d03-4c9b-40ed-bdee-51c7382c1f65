<template>
  <el-dialog
    :model-value="visible"
    :title="`设置产品 '${product.product_name}' 的用户数阶梯价格`"
    width="700px"
    :close-on-click-modal="false"
    @update:modelValue="$emit('update:visible', $event)"
    @closed="onDialogClosed"
  >
    <div class="tier-dialog-content">
      <el-alert
        title="规则说明"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      >
        <p>
          -
          阶梯区间遵循“包含起始，包含结束”原则。例如：3-10人，即增购人数在3至10人之间时，按此单价收费。
        </p>
        <p>- 结束人数留空，代表“及以上”。</p>
        <p>
          -
          系统会根据您设置的阶梯，在创建订单时自动计算增购费用。例如：基础3人，增购28人，会按(10-3)*单价A
          + (20-10)*单价B + (28-20)*单价C
          的方式分段计算总价。
        </p>
      </el-alert>

      <el-table :data="tiers" border style="width: 100%">
        <el-table-column label="起始人数" width="160">
          <template #default="{ row }">
            <el-input-number
              v-model="row.min_users"
              :min="1"
              controls-position="right"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="结束人数" width="160">
          <template #default="{ row }">
            <el-input-number
              v-model="row.max_users"
              :min="row.min_users || 1"
              placeholder="及以上"
              controls-position="right"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="每人单价 (元)">
          <template #default="{ row }">
            <el-input-number
              v-model="row.price_per_user"
              :min="0"
              :precision="2"
              controls-position="right"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center">
          <template #default="{ $index }">
            <el-button
              type="danger"
              link
              @click="removeTier($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-button class="mt-4" style="width: 100%" plain @click="addTier">
        <el-icon><Plus /></el-icon>
        添加一个阶梯
      </el-button>
    </div>
    <template #footer>
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :loading="isSaving"
      >
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { getUserAddonTiers, saveUserAddonTiers } from '@/api/product.js';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { onMounted } from 'vue'; // [新增] 引入 onMounted

// --- 组件通信 ---
const props = defineProps({
  visible: Boolean,
  product: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(['update:visible', 'tiers-updated']);

// --- 状态管理 ---
const tiers = ref([]);
const isSaving = ref(false);
const originalTiers = ref(''); // 用于检测是否有改动

// --- 核心逻辑 ---

// [修复] 使用 onMounted 钩子在组件创建时加载数据，这比 watch 更可靠
onMounted(() => {
  if (props.visible) {
    loadTiers();
  }
});

// 从后端加载阶梯价格数据
const loadTiers = async () => {
  try {
    // [诊断] 添加日志以帮助调试
    console.log(`[诊断] 正在为产品ID ${props.product.id} 加载阶梯价格...`);
    const data = await getUserAddonTiers(props.product.id);
    console.log('[诊断] 从API收到的数据:', JSON.parse(JSON.stringify(data)));

    tiers.value = data.map(tier => ({...tier})); // 创建副本
    originalTiers.value = JSON.stringify(tiers.value); // 存储初始状态
  } catch (error) {
    // [诊断] 添加错误日志
    console.error('[诊断] 加载阶梯价格时发生错误:', error);
    ElMessage.error('加载阶梯价格失败');
    emit('update:visible', false);
  }
};

// 添加一个新的阶梯
const addTier = () => {
    const lastTier = tiers.value[tiers.value.length - 1];
    const newMinUsers = lastTier && lastTier.max_users ? lastTier.max_users + 1 : 1;
    
    tiers.value.push({
        min_users: newMinUsers,
        max_users: null,
        price_per_user: 0,
    });
};

// 删除一个阶梯
const removeTier = (index) => {
  tiers.value.splice(index, 1);
};

// 保存阶梯价格
const handleSave = async () => {
  if (!validateTiers()) return;
  
  isSaving.value = true;
  try {
    await saveUserAddonTiers(props.product.id, tiers.value);
    ElMessage.success('阶梯价格保存成功！');
    emit('tiers-updated');
    emit('update:visible', false);
  } catch (error) {
    ElMessage.error(`保存失败: ${error.response?.data?.message || error.message}`);
  } finally {
    isSaving.value = false;
  }
};

// 数据校验
const validateTiers = () => {
  for (let i = 0; i < tiers.value.length; i++) {
    const tier = tiers.value[i];
    if (tier.max_users !== null && tier.min_users > tier.max_users) {
      ElMessage.error(`第 ${i + 1} 行错误：起始人数不能大于结束人数。`);
      return false;
    }
    if (i > 0) {
      const prevTier = tiers.value[i - 1];
      if (!prevTier.max_users) {
        ElMessage.error(`第 ${i} 行错误：上一阶梯的结束人数未填写，无法开始新阶梯。`);
        return false;
      }
      if (tier.min_users <= prevTier.max_users) {
        ElMessage.error(`第 ${i + 1} 行错误：起始人数必须大于上一阶梯的结束人数。`);
        return false;
      }
    }
  }
  return true;
};

// 对话框关闭时重置状态
const onDialogClosed = () => {
    tiers.value = [];
    isSaving.value = false;
}
</script>

<style scoped>
.tier-dialog-content {
  padding: 0 10px;
}
.mt-4 {
  margin-top: 1rem;
}
</style> 