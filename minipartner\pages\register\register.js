/**
 * 合伙人注册页面
 * 用户可以申请成为合伙人，等待管理员审核
 */
const api = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 注册表单数据
    formData: {
      name: '',           // 真实姓名
      mobile: '',         // 手机号
      email: '',          // 邮箱
      password: '',       // 密码
      confirmPassword: '', // 确认密码
      idCard: '',         // 身份证号
      bankCard: '',       // 银行卡号
      remark: ''          // 申请说明
    },
    
    // 页面状态
    submitting: false,    // 是否正在提交
    agreedProtocol: false, // 是否同意协议
    showPassword: false,   // 是否显示密码
    showConfirmPassword: false // 是否显示确认密码
  },

  /**
   * 页面加载
   */
  onLoad() {
    wx.setNavigationBarTitle({
      title: '申请成为合伙人'
    });
  },

  /**
   * 处理表单输入
   */
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 切换密码显示状态
   */
  togglePasswordVisibility(e) {
    const { type } = e.currentTarget.dataset;
    if (type === 'password') {
      this.setData({
        showPassword: !this.data.showPassword
      });
    } else if (type === 'confirm') {
      this.setData({
        showConfirmPassword: !this.data.showConfirmPassword
      });
    }
  },

  /**
   * 切换协议同意状态
   */
  toggleProtocol() {
    this.setData({
      agreedProtocol: !this.data.agreedProtocol
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, agreedProtocol } = this.data;
    
    // 检查必填字段
    if (!formData.name.trim()) {
      wx.showToast({ title: '请输入真实姓名', icon: 'none' });
      return false;
    }
    
    if (!formData.mobile.trim()) {
      wx.showToast({ title: '请输入手机号', icon: 'none' });
      return false;
    }
    
    // 手机号格式验证
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (!mobileRegex.test(formData.mobile)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return false;
    }
    
    if (!formData.password.trim()) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return false;
    }
    
    // 密码长度验证
    if (formData.password.length < 6) {
      wx.showToast({ title: '密码至少6位', icon: 'none' });
      return false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      wx.showToast({ title: '两次密码输入不一致', icon: 'none' });
      return false;
    }
    
    if (!formData.idCard.trim()) {
      wx.showToast({ title: '请输入身份证号', icon: 'none' });
      return false;
    }
    
    // 身份证号格式验证
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(formData.idCard)) {
      wx.showToast({ title: '请输入正确的身份证号', icon: 'none' });
      return false;
    }
    
    if (!formData.bankCard.trim()) {
      wx.showToast({ title: '请输入银行卡号', icon: 'none' });
      return false;
    }
    
    // 银行卡号格式验证
    const bankCardRegex = /^\d{16,19}$/;
    if (!bankCardRegex.test(formData.bankCard)) {
      wx.showToast({ title: '请输入正确的银行卡号', icon: 'none' });
      return false;
    }
    
    if (!agreedProtocol) {
      wx.showToast({ title: '请先同意合伙人协议', icon: 'none' });
      return false;
    }
    
    return true;
  },

  /**
   * 提交注册申请
   */
  async handleSubmit() {
    if (!this.validateForm()) {
      return;
    }
    
    if (this.data.submitting) {
      return;
    }
    
    this.setData({ submitting: true });
    
    try {
      const { formData } = this.data;
      
      // 调用注册API
      const result = await api.registerPartner({
        name: formData.name.trim(),
        mobile: formData.mobile.trim(),
        email: formData.email.trim(),
        password: formData.password,
        idCard: formData.idCard.trim(),
        bankCard: formData.bankCard.trim(),
        remark: formData.remark.trim()
      });
      
      console.log('注册申请提交结果:', result);
      
      if (result.success) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的合伙人申请已提交，请等待管理员审核。审核通过后您将收到通知。',
          showCancel: false,
          success: () => {
            // 跳转到登录页面
            wx.redirectTo({
              url: '/pages/login/login'
            });
          }
        });
      } else {
        wx.showToast({
          title: result.message || '申请提交失败',
          icon: 'none',
          duration: 3000
        });
      }
      
    } catch (error) {
      console.error('注册申请失败:', error);
      wx.showToast({
        title: error.message || '申请提交失败，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 返回登录页面
   */
  goToLogin() {
    wx.navigateBack();
  },

  /**
   * 查看合伙人协议
   */
  viewProtocol() {
    wx.showModal({
      title: '合伙人协议',
      content: '这里是合伙人协议的内容...',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
});
