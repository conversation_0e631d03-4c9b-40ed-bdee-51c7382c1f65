module.exports = {
  apps: [
    {
      name: "customer-backend",
      script: "index.js",
      cwd: "./backend",
      interpreter: "/usr/bin/node",
      instances: 1,
      exec_mode: "fork",
      watch: false, // 生产环境不建议开启watch
      max_memory_restart: "512M", // 降低内存限制，更早发现内存泄漏
      max_restarts: 10, // 最大重启次数
      min_uptime: "10s", // 最小运行时间
      restart_delay: 4000, // 重启延迟
      // 监控配置
      monitoring: true,
      pmx: true,
      // 环境变量
      env: {
        PORT: 3002,
        NODE_ENV: "production"
      },
      env_production: {
        PORT: 3002,
        NODE_ENV: "production"
      },
      // 日志配置
      log_file: "./logs/customer-backend.log",
      error_file: "./logs/customer-backend-error.log",
      out_file: "./logs/customer-backend-out.log",
      log_date_format: "YYYY-MM-DD HH:mm:ss Z",
      merge_logs: true,
      // 进程管理
      kill_timeout: 5000, // 强制杀死进程的超时时间
      listen_timeout: 3000, // 监听超时时间
      // 自动重启配置
      autorestart: true,
      // 异常重启配置
      exp_backoff_restart_delay: 100,
    }
  ]
};
