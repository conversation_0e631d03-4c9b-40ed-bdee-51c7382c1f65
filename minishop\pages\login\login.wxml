<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 标题区域 -->
  <view class="header-section">
    <!-- 几何动态背景 -->
    <view class="geometric-bg">
      <view class="geo-circle geo-1"></view>
      <view class="geo-circle geo-2"></view>
      <view class="geo-square geo-3"></view>
      <view class="geo-triangle geo-4"></view>
      <view class="geo-circle geo-5"></view>
      <view class="geo-square geo-6"></view>
    </view>

    <!-- 标题内容 -->
    <view class="title-content">
      <text class="main-title">欢迎使用</text>
      <text class="sub-title">立即登录，开启全新世界</text>
    </view>
  </view>

  <!-- 登录按钮区域 -->
  <view class="login-methods">
    <!-- 微信授权手机号登录 -->
    <!-- 未同意协议时显示的按钮 -->
    <button
      wx:if="{{!agreedProtocol}}"
      class="wechat-login-btn"
      bindtap="handleWechatLogin">
      <text class="iconfont icon-weixin"></text>
      <text class="btn-text">微信授权手机号登录</text>
    </button>

    <!-- 已同意协议时显示的授权按钮 -->
    <button
      wx:else
      class="wechat-login-btn"
      open-type="getPhoneNumber"
      bindgetphonenumber="onGetPhoneNumber"
      phone-number-no-quota-toast="{{false}}">
      <text class="iconfont icon-weixin"></text>
      <text class="btn-text">微信授权手机号登录</text>
    </button>

    <!-- 使用账号密码登录 -->
    <button class="password-login-btn" bindtap="showPasswordLogin">
      <text class="btn-text">使用账号密码登录</text>
    </button>
  </view>



  <!-- 底部协议区域 -->
  <view class="bottom-protocol">
    <view class="protocol-checkbox" bindtap="toggleProtocol">
      <view class="checkbox {{agreedProtocol ? 'checked' : ''}}">
        <text class="check-mark" wx:if="{{agreedProtocol}}">✓</text>
      </view>
      <text class="protocol-text">
        我已阅读并同意《服务协议》与《隐私保护指引》
        以及个人信息保护规则
      </text>
    </view>
  </view>

  <!-- 账号密码登录弹窗 -->
  <view class="password-modal {{showPasswordModal ? 'show' : ''}}" bindtap="hidePasswordLogin">
    <view class="modal-content" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">账号密码登录</text>
        <view class="close-btn" bindtap="hidePasswordLogin">×</view>
      </view>

      <view class="form-container">
        <view class="input-group">
          <input
            class="form-input"
            type="text"
            placeholder="请输入手机号/姓名/昵称"
            value="{{loginForm.username}}"
            bindinput="onUsernameInput"
            maxlength="50"
          />
        </view>

        <view class="input-group">
          <input
            class="form-input"
            type="password"
            placeholder="请输入密码"
            value="{{loginForm.password}}"
            bindinput="onPasswordInput"
            maxlength="50"
          />
        </view>

        <button class="login-btn" bindtap="handlePasswordLogin">
          登录
        </button>

        <view class="register-link">
          <text bindtap="goToRegister">还没有账号？立即注册</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="modal-mask {{showPasswordModal ? 'show' : ''}}" bindtap="hidePasswordLogin" catchtouchmove="preventTouchMove"></view>
</view>
