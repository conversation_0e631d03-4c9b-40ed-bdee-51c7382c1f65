/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 标题区域 */
.header-section {
  padding: 550rpx 40rpx 120rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.main-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.sub-title {
  font-size: 28rpx;
  color: #999999;
  font-weight: 400;
  display: block;
}

/* 几何动态背景 */
.geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.title-content {
  position: relative;
  z-index: 2;
}

/* 几何图形基础样式 */
.geo-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
}

.geo-square {
  position: absolute;
  opacity: 0.08;
}

.geo-triangle {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0.06;
}

/* 具体几何图形 */
.geo-1 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(45deg, #4285f4, #6fa8f5);
  top: 100rpx;
  left: 50rpx;
  animation: float1 6s ease-in-out infinite;
}

.geo-2 {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #5a9df7, #4285f4);
  top: 200rpx;
  right: 80rpx;
  animation: float2 8s ease-in-out infinite;
}

.geo-3 {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #6fa8f5, #4285f4);
  top: 150rpx;
  right: 200rpx;
  transform: rotate(45deg);
  animation: rotate1 10s linear infinite;
}

.geo-4 {
  border-left: 40rpx solid transparent;
  border-right: 40rpx solid transparent;
  border-bottom: 70rpx solid rgba(66, 133, 244, 0.15);
  top: 80rpx;
  left: 200rpx;
  animation: float3 7s ease-in-out infinite;
}

.geo-5 {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(225deg, #4285f4, #7bb3f7);
  top: 300rpx;
  left: 100rpx;
  animation: float4 9s ease-in-out infinite;
}

.geo-6 {
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(180deg, #5a9df7, #4285f4);
  top: 250rpx;
  right: 120rpx;
  transform: rotate(30deg);
  animation: rotate2 12s linear infinite;
}

/* 动画关键帧 */
@keyframes float1 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-20rpx) translateX(10rpx); }
  50% { transform: translateY(-10rpx) translateX(-15rpx); }
  75% { transform: translateY(-25rpx) translateX(5rpx); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
  33% { transform: translateY(-15rpx) translateX(-10rpx) scale(1.1); }
  66% { transform: translateY(-5rpx) translateX(15rpx) scale(0.9); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30rpx) rotate(10deg); }
}

@keyframes float4 {
  0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
  25% { transform: translateY(-10rpx) translateX(-8rpx) scale(1.2); }
  50% { transform: translateY(-20rpx) translateX(12rpx) scale(0.8); }
  75% { transform: translateY(-5rpx) translateX(-5rpx) scale(1.1); }
}

@keyframes rotate1 {
  0% { transform: rotate(45deg); }
  100% { transform: rotate(405deg); }
}

@keyframes rotate2 {
  0% { transform: rotate(30deg); }
  100% { transform: rotate(390deg); }
}

/* 登录按钮区域 */
.login-methods {
  padding: 0 50rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 60rpx;
}

/* 微信登录按钮 */
button.wechat-login-btn {
  width: 100%;
  height: 96rpx;
  background: #4285f4;
  border-radius: 48rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin: 0;
  padding: 0;
}

.wechat-login-btn::after {
  border: none;
}

.icon-weixin {
  font-size: 40rpx;
  color: #ffffff;
}

.wechat-login-btn .btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 账号密码登录按钮 - 使用更高特异性的选择器 */
button.password-login-btn {
  width: 100%;
  height: 96rpx;
  background: #e0e9f8;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  margin-left: 0;
  margin-right: 0;
  box-sizing: border-box;
}

.password-login-btn::after {
  border: none;
}

.password-login-btn .btn-text {
  font-size: 32rpx;
  color: #4285f4;
  font-weight: 400;
}



/* 底部协议区域 */
.bottom-protocol {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f5f5f5;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.protocol-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  justify-content: flex-start;
  max-width: 600rpx;
  margin: 0 auto;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #cccccc;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 35rpx;
  background: white;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #4285f4;
  border-color: #4285f4;
}

.check-mark {
  font-size: 18rpx;
  color: white;
  font-weight: bold;
}

.protocol-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
  text-align: left;
  flex: 1;
  margin-top: 0;
}

.protocol-link {
  color: #4285f4;
  text-decoration: none;
}



/* 账号密码登录弹窗 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.password-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  width: 85%;
  max-width: 520rpx;
  padding: 48rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  transform: translateY(60rpx) scale(0.9);
  transition: all 0.3s ease;
}

.password-modal.show .modal-content {
  transform: translateY(0) scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  font-size: 48rpx;
  color: #666666;
  line-height: 1;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.form-container {
  display: flex;
  flex-direction: column;
}

.input-group {
  margin-bottom: 32rpx;
}

.form-input {
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  background: #ffffff;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4285f4;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(66, 133, 244, 0.1);
}

.login-btn {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: #4285f4;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 24rpx;
  transition: all 0.3s ease;
  
}

.login-btn::after {
  border: none;
}

.login-btn:active {
  transform: translateY(2rpx) scale(0.98);
}

.register-link {
  text-align: center;
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #4285f4;
  font-weight: 500;
}

/* 遮罩层 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 协议弹窗 */
.protocol-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.modal-content {
  position: relative;
  width: 85%;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48rpx 48rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.modal-close {
  font-size: 48rpx;
  color: #666;
  line-height: 1;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.modal-body {
  max-height: 60vh;
  padding: 24rpx 48rpx 48rpx;
  overflow-y: auto;
}

.protocol-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
}
