<template>
  <el-table 
    :data="orders" 
    :loading="loading"
    border 
    style="width: 100%"
    :empty-text="emptyText"
    @selection-change="handleSelectionChange"
  >
    <!-- 选择列（仅在选择模式下显示） -->
    <el-table-column 
      v-if="selectable" 
      type="selection" 
      width="55" 
    />
    
    <!-- 订单ID列 -->
    <el-table-column prop="order_id" label="订单ID" width="180">
      <template #default="{ row }">
        <el-button 
          link 
          type="primary" 
          @click="viewOrderDetail(row.id)"
        >
          {{ row.order_id }}
        </el-button>
      </template>
    </el-table-column>
    
    <!-- 订单大类列 -->
    <el-table-column 
      prop="order_category" 
      label="订单大类" 
      width="100" 
    />

    <!-- 订单类型列 -->
    <el-table-column 
      prop="order_type" 
      label="订单类型" 
      width="120" 
    />

    <!-- 订单金额列 -->
    <el-table-column 
      prop="actual_amount" 
      label="订单金额" 
      width="120" 
      align="right"
    >
      <template #default="{ row }">
        <span class="amount-text">¥{{ formatAmount(row.actual_amount) }}</span>
      </template>
    </el-table-column>

    <!-- 支付状态列 -->
    <el-table-column 
      prop="payment_status" 
      label="支付状态" 
      width="100"
    >
      <template #default="{ row }">
        <el-tag :type="getPaymentStatusType(row.payment_status)" size="small">
          {{ row.payment_status }}
        </el-tag>
      </template>
    </el-table-column>

    <!-- 审核状态列 -->
    <el-table-column 
      prop="audit_status" 
      label="审核状态" 
      width="100"
    >
      <template #default="{ row }">
        <el-tag :type="getAuditStatusType(row.audit_status)" size="small">
          {{ row.audit_status }}
        </el-tag>
      </template>
    </el-table-column>
    
    <!-- 创建时间列 -->
    <el-table-column 
      prop="createdAt" 
      label="创建时间" 
      width="160"
    >
      <template #default="{ row }">
        {{ formatDateTime(row.createdAt) }}
      </template>
    </el-table-column>
    
    <!-- 备注列 -->
    <el-table-column 
      prop="remark" 
      label="备注" 
      show-overflow-tooltip 
    />
    
    <!-- 操作列 -->
    <el-table-column 
      v-if="showActions" 
      label="操作" 
      width="120" 
      fixed="right"
    >
      <template #default="{ row }">
        <el-button 
          v-if="actionType === 'remove'"
          size="small" 
          type="danger"
          @click="$emit('remove-order', row.id)"
        >
          移除
        </el-button>
        <el-button 
          v-else
          size="small" 
          @click="viewOrderDetail(row.id)"
        >
          查看详情
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { formatDateTime } from '@/utils/format.js'

// Props 定义
const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '暂无订单数据'
  },
  selectable: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: false
  },
  actionType: {
    type: String,
    default: 'view', // 'view' | 'remove'
    validator: (value) => ['view', 'remove'].includes(value)
  }
})

// Emits 定义
const emit = defineEmits(['selection-change', 'remove-order'])

const router = useRouter()

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

// 获取支付状态类型
const getPaymentStatusType = (status) => {
  const statusMap = {
    '待支付': 'warning',
    '已支付': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const statusMap = {
    '待审核': 'warning',
    '已审核': 'success',
    '已拒绝': 'danger'
  }
  return statusMap[status] || 'info'
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push(`/orders/${orderId}`)
}
</script>

<style scoped>
.amount-text {
  font-weight: 600;
  color: #f56c6c;
}
</style>
