// 引入我们刚刚创建的 Employee 模型
const db = require('../models');
const { generateEmployeeNumber } = require('../utils/id_helper');
const { Op } = require('sequelize');
const Employee = db.Employee;
const bcrypt = require('bcryptjs'); // 引入 bcryptjs 用于密码加密

/**
 * [新增] 获取下一个可用的员工工号
 */
exports.getNextEmployeeNumber = async (req, res) => {
  try {
    const nextId = await generateEmployeeNumber();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个员工工号时出错:', error);
    res.status(500).json({ message: '生成员工工号失败', error: error.message });
  }
};

/**
 * 定义一个异步函数来获取所有员工信息
 * @param {object} req - Express 的请求对象，包含了请求的所有信息
 * @param {object} res - Express 的响应对象，用于向客户端发送响应
 */
exports.getAllEmployees = async (req, res) => {
  try {
    // [!] 修改点：永远不要在API中返回密码哈希
    const employees = await Employee.findAll({
      attributes: { exclude: ['password'] }
    });

    // 如果查询成功，返回状态码 200 (OK) 和 JSON 格式的员工列表
    res.status(200).json(employees);
  } catch (error) {
    // 如果在查询过程中发生任何错误（比如数据库连接问题）
    // 打印错误到控制台，方便我们调试
    console.error('获取员工列表时出错:', error);
    // 向客户端返回状态码 500 (Internal Server Error) 和一个错误消息
    res.status(500).json({ message: '获取员工列表失败', error: error.message });
  }
};

// 创建新员工
exports.createEmployee = async (req, res) => {
  try {
    const { password, ...employeeData } = req.body;

    // [新增] 自动生成或验证 employee_number
    if (!employeeData.employee_number) {
      employeeData.employee_number = await generateEmployeeNumber();
    } else {
      const existing = await Employee.findOne({ where: { employee_number: employeeData.employee_number } });
      if (existing) {
        return res.status(409).json({ message: `员工工号 '${employeeData.employee_number}' 已存在。` });
      }
    }

    // [!] 新增：校验密码是否存在
    if (!password) {
      return res.status(400).json({ message: '创建员工时必须提供密码' });
    }

    // [!] 新增：对密码进行加密
    const hashedPassword = await bcrypt.hash(password, 8); // 8是加密成本，数字越大越安全但越慢

    // [!] 修改点：将加密后的密码和员工数据一起创建
    const employee = await Employee.create({
      ...employeeData,
      password: hashedPassword
    });

    // 从返回结果中也排除密码
    const employeeJson = employee.toJSON();
    delete employeeJson.password;

    res.status(201).json(employeeJson);
  } catch (error) {
    // [增强] 处理 Sequelize 唯一约束错误等
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({ message: '手机号或工号已存在。', fields: error.fields });
    }
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '创建员工失败', error: error.message });
  }
};

// 更新员工信息
exports.updateEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const { password, ...employeeData } = req.body;

    // [新增] 验证 employee_number 的唯一性
    if (employeeData.employee_number) {
      const existing = await Employee.findOne({
        where: {
          employee_number: employeeData.employee_number,
          id: { [Op.ne]: id }
        }
      });
      if (existing) {
        return res.status(409).json({ message: `员工工号 '${employeeData.employee_number}' 已被其他员工占用。` });
      }
    }

    // [!] 新增：如果请求体中包含密码，则加密
    if (password) {
      employeeData.password = await bcrypt.hash(password, 8);
    }

    const [updated] = await Employee.update(employeeData, { where: { id: id } });
    
    if (updated) {
      const updatedEmployee = await Employee.findByPk(id, {
        attributes: { exclude: ['password'] } // [!] 修改点：查询更新后的员工信息时也排除密码
      });
      res.status(200).json(updatedEmployee);
    } else {
      res.status(404).json({ message: '未找到指定ID的员工' });
    }
  } catch (error) {
    // [增强]
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({ message: '手机号或工号已存在。', fields: error.fields });
    }
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新员工失败', error: error.message });
  }
};

// 删除员工
exports.deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await Employee.destroy({ where: { id: id } });
    if (deleted) {
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定ID的员工' });
    }
  } catch (error) {
    res.status(500).json({ message: '删除员工失败', error: error.message });
  }
}; 