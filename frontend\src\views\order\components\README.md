# 订单管理组件

这个目录包含订单管理的可复用组件：

## 组件说明

### OrderHeader.vue - 订单通用表头组件
- 展示和编辑订单的公共表头信息（企业ID、资产ID、用户ID、创建方式、订单类型等）
- 产品订单和服务订单都会使用这个组件
- 支持企业ID/资产ID/用户ID联动选择，自动填充关联信息
- 根据订单类型显示/隐藏特定字段（如税额、发票类型等）

### ProductInfo.vue - 产品订单信息组件
- 用于产品订单的表体，严格按照用户提供的图片布局实现
- 包含产品选择（带版本信息）、使用人数、账套数、购买时长等
- 支持功能模块选择（带价格）、价格自动计算
- 显示基础人数和基础账套数信息

### ServiceInfo.vue - 服务订单信息组件
- 用于服务订单的表体
- 管理服务明细行（服务名、金额、关联产品订单号、备注）的增删改
- 支持服务类型选择（实施/售后/SPS）
- 手动关联产品订单功能

### OrderAttachment.vue - 附件管理组件
- 管理订单的附件（可多行），包括上传、查看、删除附件
- 产品订单和服务订单都会使用
- 文件类型分类（合同/发票/其他）
- 附件支持预览和下载

### ReviewActions.vue - 审核页面订单操作组件
- 提供审核相关的操作按钮
- 包含审核通过、拒绝审核、补全信息、删除订单等按钮
- 只在审核模式下显示
