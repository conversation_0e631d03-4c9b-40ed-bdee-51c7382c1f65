// 引入 Sequelize 库的基础模块和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 ProductUserAddonTier 模型，它代表了 'product_user_addon_tier' 数据表。
 *              这张表专门用于存储产品在增购用户数时的阶梯价格策略。
 */
const ProductUserAddonTier = sequelize.define(
  // 模型名称，在代码中我们用 ProductUserAddonTier 来称呼它
  'ProductUserAddonTier',
  {
    // --- 字段定义 ---
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true, // 这是主键
      autoIncrement: true, // ID自增
      comment: '自增主键ID',
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false, // 不允许为空
      comment: '关联的 product.id，表明此阶梯价属于哪个产品',
      // 定义外键引用，这在代码层面提供了更强的关联性
      references: {
        model: 'product', // 关联到 'product' 表
        key: 'id',      // 关联 'product' 表的 'id' 字段
      },
    },
    min_users: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '阶梯起始人数（包含此数值）',
    },
    max_users: {
      type: DataTypes.INTEGER,
      allowNull: true, // 允许为空，null 值代表“无限大”或“以上”
      comment: '阶梯结束人数（包含此数值），null表示无上限',
    },
    price_per_user: {
      type: DataTypes.DECIMAL(10, 2), // 10位总长，2位小数，用于精确表示金额
      allowNull: false,
      comment: '在此阶梯区间内，每增加一个用户的单价',
    },
    remark: {
      type: DataTypes.TEXT, // 使用TEXT类型以存储较长的描述文本
      allowNull: true, // 允许为空
      comment: '备注（可选），用于业务人员记录特殊说明',
    },
    // createdAt 和 updatedAt 字段由 Sequelize 自动处理，无需在此定义
  },
  {
    // --- 模型配置 ---
    tableName: 'product_user_addon_tier', // 强制指定表名为这个，而不是Sequelize自动生成的复数形式
    timestamps: true, // 启用 Sequelize 的时间戳功能，它会自动创建和管理 createdAt 和 updatedAt 字段
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
    comment: '产品用户数增购阶梯价格表', // 对整个表的注释
  }
);

// 导出我们定义好的模型，以便在项目的其他地方使用它
module.exports = ProductUserAddonTier; 