<template>
  <el-card shadow="never" class="header-card">
    <template #header>
      <span class="card-header">企业基本信息</span>
    </template>

    <el-form 
      :model="formData" 
      label-width="120px" 
      :disabled="readonly"
      ref="formRef"
      :rules="rules"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="企业ID" prop="enterprise_id">
            <el-input 
              v-model="formData.enterprise_id" 
              placeholder="请输入企业ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业名称" prop="name">
            <el-input 
              v-model="formData.name" 
              placeholder="请输入企业名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业税号" prop="tax_number">
            <el-input 
              v-model="formData.tax_number" 
              placeholder="请输入企业税号"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="开户行" prop="bank_name">
            <el-input 
              v-model="formData.bank_name" 
              placeholder="请输入开户行"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="银行账户" prop="bank_account">
            <el-input 
              v-model="formData.bank_account" 
              placeholder="请输入银行账户"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开票类型" prop="invoice_type">
            <el-select 
              v-model="formData.invoice_type" 
              placeholder="请选择开票类型"
              style="width: 100%"
            >
              <el-option label="普票" value="普票"></el-option>
              <el-option label="专票" value="专票"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="联系人" prop="contact_person">
            <el-input 
              v-model="formData.contact_person" 
              placeholder="请输入联系人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话" prop="contact_phone">
            <el-input 
              v-model="formData.contact_phone" 
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责人" prop="employee_id">
            <el-select 
              v-if="isAdmin" 
              v-model="formData.employee_id" 
              placeholder="请选择负责人" 
              filterable 
              clearable
              style="width: 100%"
              @change="handleEmployeeChange"
            >
              <el-option 
                v-for="item in employeeOptions" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id"
              />
            </el-select>
            <el-input 
              v-else 
              :value="getEmployeeName()" 
              disabled 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="关联用户" prop="user_id">
            <el-select 
              v-model="formData.user_id" 
              placeholder="请选择关联用户" 
              filterable 
              clearable
              style="width: 100%"
              @change="handleUserChange"
            >
              <el-option 
                v-for="item in userOptions" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="地址" prop="address">
            <el-input 
              v-model="formData.address" 
              placeholder="请输入地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <!-- 营业执照字段 - 移出表单范围，不受表单状态限制 -->
    <div style="margin-top: 20px; padding: 0 20px;">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="license-section">
            <label class="license-label">营业执照</label>
            <div class="license-content">
              <!-- 下载按钮 - 有文件时显示 -->
              <div v-if="formData.license_image" style="margin-bottom: 8px;">
                <el-button
                  type="primary"
                  link
                  @click="handleDownloadLicense"
                >
                  下载营业执照
                </el-button>
              </div>
              <!-- 上传组件 - 非只读模式显示 -->
              <el-upload
                v-if="!readonly"
                ref="uploadRef"
                action="#"
                :auto-upload="false"
                :on-change="handleLicenseFileChange"
                :limit="1"
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    <p v-if="formData.license_image && !licenseFile">
                      当前文件: {{ formData.license_image.split('/').pop() }}
                    </p>
                    只允许上传一张图片，新文件将覆盖旧文件。
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/store/auth.js'
import service from '@/utils/request_extra.js'

// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  employeeOptions: {
    type: Array,
    default: () => []
  },
  userOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['employee-change', 'user-change', 'license-change'])

// 认证状态
const { state: authState } = useAuth()
const isAdmin = computed(() => authState.user?.role === 'admin')

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)
const licenseFile = ref(null)

// 表单验证规则
const rules = {
  enterprise_id: [
    { required: true, message: '请输入企业ID', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' }
  ]
}

// 获取员工名称
const getEmployeeName = () => {
  if (!props.formData.employee_id) return ''
  const employee = props.employeeOptions.find(emp => emp.id === props.formData.employee_id)
  return employee ? employee.name : '未知员工'
}

// 处理员工变更
const handleEmployeeChange = (employeeId) => {
  emit('employee-change', employeeId)
}

// 处理用户变更
const handleUserChange = (userId) => {
  emit('user-change', userId)
}

// 处理营业执照文件变更
const handleLicenseFileChange = (file) => {
  // 修复：直接使用file.raw，如果不存在则使用file本身
  const fileToUse = file.raw || file;
  licenseFile.value = fileToUse;
  emit('license-change', fileToUse);
}

// 下载营业执照
const handleDownloadLicense = () => {
  if (!props.formData.id || !props.formData.license_image) {
    ElMessage.warning('没有可供下载的营业执照')
    return
  }
  const baseURL = service.defaults.baseURL || ''
  const token = localStorage.getItem('authToken')
  const downloadUrl = `${baseURL}/enterprises/${props.formData.id}/license/download?token=${token}`
  window.open(downloadUrl, '_blank')
}

// 表单验证方法
const validate = async () => {
  return await formRef.value?.validate()
}

// 暴露方法给父组件
defineExpose({
  validate
})
</script>

<style scoped>
.header-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 营业执照字段样式 - 模拟表单项样式 */
.license-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;
}

.license-label {
  flex: 0 0 auto;
  width: 80px;
  text-align: right;
  padding-right: 12px;
  font-size: 14px;
  color: #606266;
  line-height: 32px;
  box-sizing: border-box;
}

.license-content {
  flex: 1;
  min-width: 0;
}
</style>
