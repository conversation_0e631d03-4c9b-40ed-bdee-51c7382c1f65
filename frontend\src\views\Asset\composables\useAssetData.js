// 资产数据管理 Composable
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getAssetById,
  createAsset,
  updateAsset,
  deleteAsset,
  getAssets,
  getNextAssetId,
  rollbackAsset
} from '@/api/asset.js'
import { useAuth } from '@/store/auth.js'

export function useAssetData() {
  // 获取认证信息
  const { state: authState } = useAuth()

  // 状态管理
  const loading = ref(false)
  const saving = ref(false)
  const assetData = ref(null)
  const originalData = ref(null)
  
  // 计算属性
  const hasChanges = computed(() => {
    if (!assetData.value || !originalData.value) return false
    return JSON.stringify(assetData.value) !== JSON.stringify(originalData.value)
  })

  // 获取单个资产
  const fetchAsset = async (id) => {
    if (!id) return null
    
    loading.value = true
    try {
      const data = await getAssetById(id)
      assetData.value = data
      originalData.value = JSON.parse(JSON.stringify(data)) // 深拷贝
      return data
    } catch (error) {
      ElMessage.error('获取资产信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取资产列表（支持分页和搜索）
  const fetchAssetList = async (params = {}) => {
    loading.value = true
    try {
      const data = await getAssets(params)
      return data
    } catch (error) {
      ElMessage.error('获取资产列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建新资产
  const createNewAsset = async (data) => {
    saving.value = true
    try {
      const result = await createAsset(data)
      ElMessage.success('资产创建成功')
      return result
    } catch (error) {
      ElMessage.error(`创建失败: ${error.message || '未知错误'}`)
      throw error
    } finally {
      saving.value = false
    }
  }

  // 更新资产
  const updateExistingAsset = async (id, data) => {
    saving.value = true
    try {
      await updateAsset(id, data)
      ElMessage.success('资产更新成功')
      // 重新获取完整数据（包含关联信息）
      const fullData = await getAssetById(id)
      assetData.value = fullData
      originalData.value = JSON.parse(JSON.stringify(fullData))
      return fullData
    } catch (error) {
      ElMessage.error(`更新失败: ${error.message || '未知错误'}`)
      throw error
    } finally {
      saving.value = false
    }
  }

  // 删除资产
  const deleteExistingAsset = async (id) => {
    try {
      await deleteAsset(id)
      ElMessage.success('资产删除成功')
      return true
    } catch (error) {
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
      throw error
    }
  }

  // 初始化新资产数据
  const initializeNewAsset = async () => {
    try {
      const response = await getNextAssetId()
      const nextId = response.next_id || response
      const newAsset = {
        asset_id: nextId,
        enterprise_id: null,
        user_id: null,
        product_id: null,
        user_count: 1,
        account_count: 1,
        duration_months: 12,
        selected_features: [],
        purchase_date: new Date(),
        product_expiry_date: null,
        sps_expiry_date: null,
        after_sales_expiry_date: null,
        product_standard_price: 0,
        sps_annual_fee: 0,
        after_sales_service_fee: 0,
        implementation_fee: 0,
        order_id: null,
        activation_code: '',
        activation_phone: '',
        activation_password: '',
        remark: '',
        status: '在线',
        creator: authState.user || null, // 设置当前登录用户为制单人
        createdAt: new Date()
      }
      
      assetData.value = newAsset
      originalData.value = JSON.parse(JSON.stringify(newAsset))
      return newAsset
    } catch (error) {
      ElMessage.error('初始化资产数据失败')
      throw error
    }
  }

  // 重置数据
  const resetData = () => {
    if (originalData.value) {
      assetData.value = JSON.parse(JSON.stringify(originalData.value))
    }
  }

  // 清空数据
  const clearData = () => {
    assetData.value = null
    originalData.value = null
  }



  // 回滚状态管理（用于需要详细对话框的场景）
  const rollbackLoading = ref(false)
  const showRollbackDialog = ref(false)
  const rollbackRecord = ref(null)

  // 显示回滚确认对话框
  const showRollbackConfirm = (record) => {
    rollbackRecord.value = record
    showRollbackDialog.value = true
  }

  // 执行回滚操作（带详细对话框）
  const executeRollback = async (assetId, onSuccess) => {
    if (!rollbackRecord.value) return false

    rollbackLoading.value = true
    try {
      await rollbackAsset(assetId, rollbackRecord.value.id)

      ElMessage.success('回滚操作成功')
      showRollbackDialog.value = false
      rollbackRecord.value = null

      // 执行成功回调
      if (onSuccess && typeof onSuccess === 'function') {
        await onSuccess()
      }

      return true
    } catch (error) {
      console.error('回滚操作失败:', error)
      ElMessage.error(`回滚操作失败: ${error.message || '未知错误'}`)
      return false
    } finally {
      rollbackLoading.value = false
    }
  }

  // 取消回滚
  const cancelRollback = () => {
    showRollbackDialog.value = false
    rollbackRecord.value = null
  }

  return {
    // 状态
    loading,
    saving,
    assetData,
    originalData,
    hasChanges,

    // 回滚相关状态
    rollbackLoading,
    showRollbackDialog,
    rollbackRecord,

    // 方法
    fetchAsset,
    fetchAssetList,
    createNewAsset,
    updateExistingAsset,
    deleteExistingAsset,
    initializeNewAsset,
    resetData,
    clearData,

    // 回滚相关方法
    showRollbackConfirm,
    executeRollback,
    cancelRollback
  }
}
