/*
 * @pdtonline.wxss
 * 重构和重新排序以匹配 pdtonline.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
@import "/static/fonts/iconfont.wxss";

page {
  margin: 0;
  padding: 0;
  border: none;
  background-color: transparent;
}

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
}

.full-width-nav {
  width: 100%;
}

/* 全局元素边框消除 */
view,
scroll-view,
text,
image,
button {
  box-sizing: border-box;
  border: none;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100%;
  height: 66vh;
  position: relative;
  overflow: hidden;
  color: #fff;
  border: none;
  margin: 0;
  padding: 0;
}

/* --- 背景动效 --- */
.poster-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: visible;
}

.geo-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.geo-small {
  width: 60rpx;
  height: 60rpx;
  top: 20%;
  left: 15%;
  animation: float 20s infinite ease-in-out;
}

.geo-medium {
  width: 120rpx;
  height: 120rpx;
  top: 60%;
  left: 70%;
  animation: float 25s infinite ease-in-out reverse;
}

.geo-large {
  width: 180rpx;
  height: 180rpx;
  top: 30%;
  left: 80%;
  animation: float 30s infinite ease-in-out;
}

.geo-square {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  top: 70%;
  left: 20%;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: rotate(45deg);
  animation: rotate 30s infinite linear;
}

.geo-triangle {
  position: absolute;
  width: 0;
  height: 0;
  top: 40%;
  left: 40%;
  border-left: 60rpx solid transparent;
  border-right: 60rpx solid transparent;
  border-bottom: 120rpx solid rgba(255, 255, 255, 0.15);
  animation: float 22s infinite ease-in-out;
}

.geo-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform-origin: center center;
}

.line-1 {
  width: 30%;
  top: 35%;
  left: 20%;
  transform: rotate(15deg);
}

.line-2 {
  width: 40%;
  top: 65%;
  left: 45%;
  transform: rotate(-20deg);
}

.hologram-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
  z-index: 0;
  opacity: 0.5;
  animation: hologramPulse 8s infinite ease-in-out;
}

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 100rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 20%, #7dd3fc 80%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 6rpx 24rpx rgba(0, 64, 255, 0.18);
  margin-bottom: 18rpx;
  animation: fadeInUp 1s;
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #38bdf8 0%, #fff 100%);
  border-radius: 4rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(56, 189, 248, 0.5);
  animation: fadeIn 1.5s;
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  animation: fadeInUp 1.2s;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
  animation: fadeInUp 1.4s;
}

/* --- 促销卡片 --- */
.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  z-index: 10;
  filter: drop-shadow(0 6rpx 16rpx rgba(0, 0, 0, 0.1));
}

/* --- 底部羽化 --- */
.poster-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 85%, #ffffff 100%);
  z-index: 2;
  pointer-events: none;
}

/* ==================================
   详情内容区域
   ================================== */
.detail-content {
  padding: 30rpx 30rpx 0;
  background: #ffffff;
  position: relative;
  z-index: 20;
  margin-top: -80rpx;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
}

/* --- 通用区块头 --- */
.section-header {
  text-align: center;
  margin: 30rpx 0 40rpx;
  position: relative;
}

.section-title {
  font-size: 42rpx;
  font-weight: 700;
  color: #0e74e9;
  position: relative;
  display: inline-block;
  padding-bottom: 20rpx;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #0284c7, #38bdf8);
  border-radius: 2rpx;
}

.subtitle-title {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

/* --- 企业常见问题 --- */
.problem-section {
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 0;
}

.problem-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.problem-card {
  width: 48%;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #f2f7ff, #e9f2ff);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s;
}

.problem-icon {
  font-size: 44rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: rgba(60, 152, 255, 0.1);
  margin: 0 auto 15rpx;
}

.problem-icon .iconfont {
  font-size: 36rpx;
  color: #3c98ff;
}

.problem-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.problem-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* --- 云财税服务 --- */
.finance-comparison-container {
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.finance-card-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.finance-card {
  width: 48%;
  background: linear-gradient(135deg, #f2f7ff, #e9f2ff);
  border-radius: 16rpx;
  padding: 30rpx 10rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.finance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #3c98ff, #6aacff);
}

.finance-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #3c98ff;
  margin-bottom: 10rpx;
  text-align: center;
}

.finance-card-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
}

.finance-card-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  text-align: center;
}

/* --- 创新企业价值 --- */
.innovation-section {
  margin: 30rpx 0;
  background: linear-gradient(135deg, #f2f7ff, #e9f2ff);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
}

.innovation-slogan {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.innovation-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.innovation-dot {
  font-size: 36rpx;
  color: #3c98ff;
  margin: 0 15rpx;
}

.innovation-desc {
  font-size: 28rpx;
  color: #3c98ff;
}

/* --- 功能对比 --- */
.t-function-section {
  margin: 30rpx 0rpx;
  padding: 40rpx 0rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.t-functions {
  margin: 25rpx 0;
  animation: fadeIn 0.5s forwards;
}

.t-func-row {
  display: flex;
  margin-bottom: 15rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out forwards;
}

.t-func-col {
  padding: 16rpx;
  display: flex;
}

.t-func-col.blue {
  flex: 1;
  background: linear-gradient(135deg, #3c98ff, #38bdf8);
  color: #ffffff;
  align-items: center;
}

.t-func-icon {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx;
}

.t-func-icon .iconfont {
  font-size: 24rpx;
  color: #ffffff;
}

.t-func-content {
  display: flex;
  flex-direction: column;
}

.t-func-name {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.t-func-col.gray {
  flex: 1;
  background-color: #f7f8fa;
  color: #666666;
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
}

.t-small {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* --- 核心亮点 --- */
.core-highlights-section {
  margin: 30rpx 0rpx;
  padding: 30rpx 20rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.highlight-list {
  margin-top: 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.highlight-item {
  width: calc(50% - 10rpx);
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.highlight-item.blue {
  background: linear-gradient(135deg, #3c98ff, #6aacff);
}

.highlight-item.gray {
  background: #f5f5f7;
  color: #333333;
}

.highlight-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.highlight-icon-wrapper {
  width: 100%;
  height: 160rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 20rpx 0;
}

.highlight-icon-wrapper .iconfont {
  font-size: 80rpx;
}

.highlight-item.blue .highlight-icon-wrapper .iconfont {
  color: #ffffff;
}

.highlight-item.gray .highlight-icon-wrapper .iconfont {
  color: #3c98ff;
}

.highlight-info {
  width: 100%;
  padding: 16rpx 20rpx 20rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.highlight-item.blue .highlight-info {
  color: #ffffff;
}

.highlight-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.highlight-desc {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.6;
  text-align: center;
}

/* --- 软件特色 --- */
.features-section {
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.feature-list {
  margin-top: 20rpx;
}

.feature-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.feature-row:last-child {
  margin-bottom: 0;
}

.feature-item {
  width: 48%;
  background: linear-gradient(135deg, #f2f7ff, #e9f2ff);
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-out forwards;
}

.feature-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
}

.feature-icon .iconfont {
  font-size: 50rpx;
  color: #3c98ff;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
}

/* --- 服务承诺 --- */
.service-section {
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.service-promise {
  margin: 30rpx 0;
}

.promise-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.promise-row:last-child {
  margin-bottom: 0;
}

.promise-item {
  display: flex;
  align-items: center;
}

.promise-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #3c98ff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  font-size: 24rpx;
}

.promise-icon .iconfont {
  font-size: 20rpx;
  color: #fff;
}

.promise-text {
  font-size: 28rpx;
  color: #333;
}

/* --- 了解更多 --- */
.more-section {
  margin: 30rpx 0;
  background: linear-gradient(135deg, #3c98ff, #6aacff);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  color: #fff;
  box-shadow: 0 10rpx 30rpx rgba(60, 152, 255, 0.3);
}

.more-title {
  font-size: 32rpx;
  font-weight: 600;
}

/* ==================================
   动画
   ================================== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-20rpx) translateX(10rpx); }
  50% { transform: translateY(0) translateX(20rpx); }
  75% { transform: translateY(20rpx) translateX(10rpx); }
}

@keyframes rotate {
  from { transform: rotate(45deg); }
  to { transform: rotate(405deg); }
}

@keyframes hologramPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.5; }
}

/* ==================================
   媒体查询
   ================================== */
@media screen and (max-width: 375px) {
  .highlight-list { flex-direction: column; }
  .highlight-item { width: 100%; }
}

@media screen and (min-width: 400px) {
  .highlight-icon-wrapper { height: 180rpx; }
} 