{"appid": "wx88557a555230893f", "compileType": "miniprogram", "libVersion": "3.7.8", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmManually": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}