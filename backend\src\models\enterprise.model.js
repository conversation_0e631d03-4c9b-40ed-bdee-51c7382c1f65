// 引入Sequelize库和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 定义 'Enterprise' 模型
 * 这个模型对应我们数据库中的 'enterprise' 表。
 */
const Enterprise = sequelize.define('Enterprise', {
  // 定义 id 字段
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键ID'
  },
  // 定义 enterprise_id 字段
  enterprise_id: {
    type: DataTypes.STRING(20),
    unique: true,
    allowNull: false,
    comment: '企业ID（如EN20250001）'
  },
  // 定义 name 字段
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '企业名称'
  },
  // 定义 tax_number 字段
  tax_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '企业税号'
  },
  // 定义 bank_name 字段
  bank_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '开户行'
  },
  // 定义 bank_account 字段
  bank_account: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '银行账户'
  },
  // 定义 invoice_type 字段
  invoice_type: {
    type: DataTypes.ENUM('普票', '专票'),
    allowNull: true,
    defaultValue: '普票',
    comment: '开票类型'
  },
  // 定义 contact_person 字段
  contact_person: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人'
  },
  // 定义 contact_phone 字段
  contact_phone: {
    type: DataTypes.STRING(15),
    allowNull: true,
    comment: '联系电话'
  },
  // 定义 address 字段
  address: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '地址'
  },
  // 定义 license_image 字段
  license_image: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '营业执照路径'
  },
  // 定义 employee_id 字段
  employee_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '负责员工ID'
  },
  // 定义 user_id 字段
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联user.id'
  },
  // 定义 remark 字段
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  // 模型配置
  tableName: 'enterprise',
  timestamps: true, // 核心修复：明确启用时间戳
  charset: 'utf8mb4',
  collate: 'utf8mb4_0900_ai_ci'
});

// 在这里，我们可以定义模型间的关联关系
// (稍后我们会回来处理)

// 导出 Enterprise 模型
module.exports = Enterprise; 