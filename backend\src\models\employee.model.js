// 引入Sequelize库和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 定义 'Employee' 模型
 * 这个模型对应我们数据库中的 'employee' 表。
 * Sequelize会自动将模型名 'Employee' 转换为表名 'employees'。
 * 为了精确匹配我们已有的 'employee' 表，我们在模型定义中会明确指定表名。
 */
const Employee = sequelize.define('Employee', {
  // 定义 id 字段
  id: {
    type: DataTypes.INTEGER,       // 类型：整数
    autoIncrement: true,           // 自动增长
    primaryKey: true,              // 主键
    comment: '自增主键'            // 字段注释，同数据库
  },
  // 定义 employee_number 字段
  employee_number: {
    type: DataTypes.STRING(20),    // 类型：字符串，最大长度20
    allowNull: false,              // 不允许为空
    unique: true,                  // 值必须唯一
    comment: '员工工号（如SW008）'
  },
  // 定义 name 字段
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '员工姓名'
  },
  // 定义 mobile 字段
  mobile: {
    type: DataTypes.STRING(15),
    allowNull: false,
    unique: true,
    comment: '手机号'
  },
  // 定义 department 字段
  department: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '部门'
  },
  // 定义 password 字段
  password: {
    type: DataTypes.STRING(128),
    allowNull: false,
    comment: '加密密码'
  },
  // [!] 新增：定义 role 字段
  role: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'employee', // 设置默认值为 'employee'
    comment: '用户角色: admin, employee'
  },
  // 定义 remark 字段
  remark: {
    type: DataTypes.TEXT,          // 类型：长文本
    allowNull: true,               // 允许为空
    comment: '备注'
  }
}, {
  // 模型配置
  tableName: 'employee',           // 强制指定表名为 'employee'，而不是 'employees'
  timestamps: false,               // 禁止Sequelize自动添加 createdAt 和 updatedAt 字段
  charset: 'utf8mb4',              // 字符集
  collate: 'utf8mb4_0900_ai_ci'    // 排序规则
});

// 导出 Employee 模型，以便在其他文件中使用
module.exports = Employee; 