<template>
  <div class="enterprise-detail-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>

        <div class="header-actions">
          <el-button type="primary" @click="handleEdit" v-if="!isEditMode">
            <el-icon><Edit /></el-icon>
            修改
          </el-button>
          <el-button type="warning" @click="handleFollowup" v-if="!isEditMode">
            <el-icon><ChatLineRound /></el-icon>
            跟进
          </el-button>
          <el-button type="danger" @click="handleDelete" v-if="!isEditMode">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>

          <!-- 编辑模式下的操作按钮 -->
          <el-button type="primary" @click="handleSave" :loading="saving" v-if="isEditMode">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="handleCancel" v-if="isEditMode">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div class="error-content" v-if="loadError && !loading">
      <el-result icon="error" :title="loadError">
        <template #extra>
          <el-button type="primary" @click="loadEnterpriseData">重新加载</el-button>
          <el-button @click="handleBack">返回列表</el-button>
        </template>
      </el-result>
    </div>

    <!-- 主要内容 -->
    <div class="enterprise-detail-content" v-else-if="enterpriseData">
      <!-- 企业表单表头 -->
      <EnterpriseFormHeader
        :formData="enterpriseData"
        :readonly="!isEditMode"
        :employeeOptions="employeeOptions"
        :userOptions="userOptions"
        @employee-change="handleEmployeeChange"
        @user-change="handleUserChange"
        @license-change="handleLicenseChange"
        ref="headerFormRef"
      />

      <!-- 标签页内容 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="detail-tabs" type="border-card">
          <!-- 企业跟进信息 -->
          <el-tab-pane name="followup">
            <template #label>
              <span class="tab-label">
                <el-icon><ChatLineRound /></el-icon>
                企业跟进信息
              </span>
            </template>
            <EnterpriseFollowupInfo
              v-if="enterpriseData"
              :enterpriseId="enterpriseData.id"
              :readonly="false"
            />
          </el-tab-pane>

          <!-- 关联资产表 -->
          <el-tab-pane name="assets">
            <template #label>
              <span class="tab-label">
                <el-icon><Box /></el-icon>
                关联资产表
              </span>
            </template>
            <EnterpriseRelatedAssets
              v-if="enterpriseData"
              :enterpriseId="enterpriseData.id"
              :readonly="false"
              :relatedAssets="enterpriseData.assets || []"
              @asset-linked="handleAssetLinked"
              @asset-removed="handleAssetRemoved"
            />
          </el-tab-pane>

          <!-- 关联订单表 -->
          <el-tab-pane name="orders">
            <template #label>
              <span class="tab-label">
                <el-icon><Document /></el-icon>
                关联订单表
              </span>
            </template>
            <div class="orders-tab-content">
              <OrderTable
                v-if="enterpriseData"
                :orders="enterpriseData.orders || []"
                :loading="ordersLoading"
                :emptyText="'该企业暂无关联订单'"
                :selectable="false"
                :showActions="true"
                :actionType="'view'"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 表尾信息 -->
      <div class="footer-section">
        <el-card shadow="never" class="footer-card">
          <template #header>
            <span class="card-header">其他信息</span>
          </template>

          <el-form :model="enterpriseData" label-width="120px" :disabled="!isEditMode">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="备注">
                  <el-input
                    v-model="enterpriseData.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单人">
                  <el-input :value="getCreatorName()" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单时间">
                  <el-input :value="formatDateTime(enterpriseData.createdAt)" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, Delete, Check, Close,
  Box, Document, ChatLineRound
} from '@element-plus/icons-vue'

// 组件导入
import EnterpriseFormHeader from '../components/EnterpriseFormHeader.vue'
import EnterpriseRelatedAssets from '../components/EnterpriseRelatedAssets.vue'
import EnterpriseFollowupInfo from '../components/EnterpriseFollowupInfo.vue'

import OrderTable from '@/views/Asset/components/OrderTable.vue'

// 工具函数
import { formatDateTime } from '@/utils/format.js'

// API
import { getEnterpriseById, updateEnterprise, deleteEnterprise } from '@/api/enterprise.js'
import { getEmployees } from '@/api/employee.js'
import { getUsers } from '@/api/user.js'

// 认证状态
import { useAuth } from '@/store/auth.js'

// 路由
const route = useRoute()
const router = useRouter()

// 认证状态
const { state: authState } = useAuth()

// 状态
const loading = ref(false)
const saving = ref(false)
const activeTab = ref('followup')
const isEditMode = ref(false)

const ordersLoading = ref(false)

// 数据
const enterpriseData = ref(null)
const originalData = ref(null) // 保存原始数据用于取消操作
const loadError = ref(null) // 加载错误信息
const employeeOptions = ref([])
const userOptions = ref([])
const licenseFile = ref(null)

// 表单引用
const headerFormRef = ref(null)

// 计算属性
const enterpriseId = computed(() => route.params.id)
const pageTitle = computed(() => {
  if (isEditMode.value) return '修改企业'
  if (enterpriseData.value) {
    return `企业详情 - ${enterpriseData.value.name || ''}`
  }
  return '企业详情'
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const [employees, users] = await Promise.all([
      getEmployees(),
      getUsers()
    ])

    employeeOptions.value = employees
    userOptions.value = users
  } catch (error) {
    ElMessage.error('加载选项数据失败')
  }
}

// 加载企业数据
const loadEnterpriseData = async () => {
  if (!enterpriseId.value) return

  loading.value = true
  loadError.value = null
  try {
    const response = await getEnterpriseById(enterpriseId.value)
    enterpriseData.value = response
    originalData.value = JSON.parse(JSON.stringify(response)) // 深拷贝
  } catch (error) {
    console.error('加载企业数据失败:', error)
    if (error.response?.status === 404) {
      loadError.value = `企业ID ${enterpriseId.value} 不存在`
      ElMessage.error(loadError.value)
    } else {
      loadError.value = '加载企业数据失败: ' + (error.response?.data?.message || error.message)
      ElMessage.error('加载企业数据失败')
    }
    // 不自动跳转，让用户看到错误信息
    // router.push({ name: 'enterprise-list' })
  } finally {
    loading.value = false
  }
}

// 初始化页面
const initializePage = async () => {
  await Promise.all([
    loadOptions(),
    loadEnterpriseData()
  ])
}

// 工具函数
const getCreatorName = () => {
  return enterpriseData.value?.creator?.name || ''
}

// 事件处理
const handleEdit = () => {
  isEditMode.value = true
}

const handleCancel = () => {
  // 恢复原始数据
  enterpriseData.value = JSON.parse(JSON.stringify(originalData.value))
  isEditMode.value = false
  licenseFile.value = null
  ElMessage.info('已取消操作')
}

const handleSave = async () => {
  try {
    // 表单验证
    await headerFormRef.value?.validate()

    saving.value = true

    const formData = new FormData()
    
    // 添加表单数据
    for (const key in enterpriseData.value) {
      if (enterpriseData.value[key] !== null && enterpriseData.value[key] !== undefined) {
        // 跳过关联对象
        if (key !== 'employee' && key !== 'user' && key !== 'assets' && key !== 'orders') {
          formData.append(key, enterpriseData.value[key])
        }
      }
    }
    
    // 添加文件
    if (licenseFile.value) {
      formData.append('license_image', licenseFile.value)
    }

    await updateEnterprise(enterpriseId.value, formData)
    
    // 重新加载数据
    await loadEnterpriseData()
    
    isEditMode.value = false
    licenseFile.value = null
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleDelete = async () => {
  if (!enterpriseId.value) return

  try {
    await ElMessageBox.confirm(
      `确定要删除企业 [${enterpriseData.value.name}] 吗？`,
      '警告',
      { type: 'warning' }
    )

    await deleteEnterprise(enterpriseId.value)
    ElMessage.success('删除成功')
    router.push({ name: 'enterprise-list' })
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleFollowup = () => {
  // 跟进记录功能已集成在 EnterpriseFollowupInfo 组件中
  // 用户可以直接在跟进记录页签中添加和修改跟进记录
  activeTab.value = 'followup'
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 处理员工选择变化
const handleEmployeeChange = (employeeId) => {
  if (enterpriseData.value) {
    enterpriseData.value.employee_id = employeeId
  }
}

// 处理用户选择变化
const handleUserChange = (userId) => {
  if (enterpriseData.value) {
    enterpriseData.value.user_id = userId
  }
}

// 处理营业执照文件变化
const handleLicenseChange = (file) => {
  licenseFile.value = file
}

// 处理资产关联
const handleAssetLinked = (assets) => {
  // 这里应该调用API来关联资产
  ElMessage.success('资产关联成功')
  // 重新加载企业数据
  loadEnterpriseData()
}

// 处理资产移除
const handleAssetRemoved = (assetIds) => {
  // 这里应该调用API来移除资产关联
  ElMessage.success('资产移除成功')
  // 重新加载企业数据
  loadEnterpriseData()
}

// 生命周期
onMounted(() => {
  initializePage()
})
</script>

<style scoped>
.enterprise-detail-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 主要内容 */
.enterprise-detail-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标签页 */
.tabs-section {
  margin: 20px 0;
}

.detail-tabs {
  background: white;
}

.detail-tabs :deep(.el-tabs__content) {
  padding-top: 20px;
}

.detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.detail-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.orders-tab-content {
  padding: 20px;
}

/* 卡片样式 */
.footer-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.footer-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .enterprise-detail-page {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>
