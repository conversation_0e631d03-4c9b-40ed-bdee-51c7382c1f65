<view class="container">
  <!-- 使用顶部导航栏组件，添加full-width-nav类 -->
  <nav-bar activeTab="{{activeTab}}" bindtabchange="handleTabChange" class="full-width-nav"></nav-bar>

  <!-- 整个内容区域使用scroll-view包裹，实现整体滚动 -->
  <scroll-view 
    scroll-y="true" 
    class="scroll-container">
    
    <!-- 产品海报区域 -->
    <view class="poster" 
          style="background: linear-gradient(135deg, #1e88e5 0%, #5e35b1 60%, #3949ab 100%);"
          bindtouchstart="onTouchStart"
          bindtouchmove="onTouchMove"
          bindtouchend="onTouchEnd">
      
      <!-- 背景元素 -->
      <view class="poster-background">
        <!-- 粒子元素 -->
        <view class="particle p-1"></view>
        <view class="particle p-2"></view>
        <view class="particle p-3"></view>
        <view class="particle p-4"></view>
        <view class="particle p-5"></view>
        <view class="particle p-6"></view>
        <view class="particle p-7"></view>
        <view class="particle p-8"></view>
        <view class="particle p-9"></view>
        <view class="particle p-10"></view>
        <view class="particle p-11"></view>
        <view class="particle p-12"></view>
        <view class="particle p-13"></view>
        <view class="particle p-14"></view>
        <view class="particle p-15"></view>
        <view class="particle p-16"></view>
        <view class="particle p-17"></view>
        <view class="particle p-18"></view>
        <view class="particle p-19"></view>
        <view class="particle p-20"></view>
        <view class="particle p-21"></view>
        <view class="particle p-22"></view>
        <view class="particle p-23"></view>
        <view class="particle p-24"></view>
        <view class="particle p-25"></view>
        
        <!-- 连接线 -->
        <view class="connection-line line-1"></view>
        <view class="connection-line line-2"></view>
        <view class="connection-line line-3"></view>
        <view class="connection-line line-4"></view>
        <view class="connection-line line-5"></view>
        
        <!-- 网格背景 -->
        <view class="grid-background"></view>
        
        <!-- 光效 -->
        <view class="glow-effect"></view>
      </view>
      
      <!-- 产品标题 -->
      <view class="product-title">
        <view class="title-main">{{posterTitle}}</view>
        <view class="title-divider"></view>
        <view class="title-sub">{{posterSubtitle}}</view>
        <view class="slogan">{{posterSlogan}}</view>
      </view>
      
      <!-- 促销卡片 -->
      <view class="promo-card-position">
        <promo-card initialShow="{{true}}" pageKey="{{productKey}}" bindjoinpromo="handleJoinPromo"></promo-card>
      </view>
      
      <!-- 底部羽化效果 -->
      <view class="poster-bottom-fade"></view>
    </view>

    <!-- 详情内容部分放在海报区域后面，确保滚动时的正确位置 -->
    <view class="detail-content">
      <!-- 主标题区域 -->
      <view class="hero-banner">
        <view class="hero-content">
          <view class="hero-title">好生意数字化解决方案</view>
          <view class="hero-subtitle">针对多种行业、多种场景提供定制化解决方案，为行业创造创新价值</view>
          <view class="divider"></view>
        </view>
      </view>
      
      <!-- 企业痛点与解决方案 -->
      <view class="pain-points-section">
        <view class="section-header">
          <view class="main-title">批零企业<text class="highlight">三大困难</text></view>
          <view class="sub-title">有困难更有解决方案</view>
        </view>
        
        <view class="pain-cards">
          <view class="pain-card">
            <view class="pain-image">
              <image src="https://mshop.bogoo.net/hsy_pain1.jpg" mode="aspectFill"></image>
            </view>
            <view class="pain-content">
              <view class="pain-title">手工做账，效率低下</view>
              <view class="pain-desc">
                <text>商品多，仓库乱，查货挑货效率太慢</text>
                <text>客户不同价格不同，往来对账太麻烦</text>
                <text>人在外，不能查库存、查价格，做梦都想手机能管店</text>
              </view>
            </view>
          </view>
          
          <view class="pain-card">
            <view class="pain-image">
              <image src="https://mshop.bogoo.net/hsy_pain2.jpg" mode="aspectFill"></image>
            </view>
            <view class="pain-content">
              <view class="pain-title">不做推广，业绩难涨</view>
              <view class="pain-desc">
                <text>分销、拼团、优惠券......有想法没软件</text>
                <text>抖音、快手、淘宝...多个平台如何打通</text>
                <text>商品、会员、线上线下如何统一数据</text>
              </view>
            </view>
          </view>
          
          <view class="pain-card">
            <view class="pain-image">
              <image src="https://mshop.bogoo.net/hsy_pain3.jpg" mode="aspectFill"></image>
            </view>
            <view class="pain-content">
              <view class="pain-title">管理混乱，成本太高</view>
              <view class="pain-desc">
                <text>库存损耗没预警，账期超了没提醒</text>
                <text>利润太低没警示，经营数据没报表</text>
                <text>数据分析没图表，管理乱成本居高不下</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 全面解决方案 -->
      <view class="solution-section">
        <view class="section-header">
          <view class="main-title"><text class="highlight">管理+营销</text>解决企业困难</view>
        </view>
        
        <view class="solution-overview">
          <view class="solution-item">管销售</view>
          <view class="solution-item">管仓库</view>
          <view class="solution-item">管商城</view>
          <view class="solution-item">管营销</view>
          <view class="solution-item">管经营</view>
        </view>
      </view>
      
      <!-- 销售管理功能 -->
      <view class="feature-block sales-feature">
        <view class="feature-header">
          <view class="feature-title">【销售无处不在】</view>
          <view class="feature-subtitle">随时随地销售开单、商品出库，让客户满意，让老板放心，让销售员轻松！</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-right">
            <image src="https://mshop.bogoo.net/hsy_feature1.png" mode="widthFix" lazy-load="true"></image>
          </view>
          <view class="feature-list">
            <view class="feature-item">
              <view class="feature-icon sales-icon-1">
                <text class="iconfont icon-tubiaozhutu"></text>
              </view>
              <view class="feature-text">支持手机、电脑、PDA快速开单、远程开单/打印，实时响应出库</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon sales-icon-2">
                <text class="iconfont icon-hetong"></text>
              </view>
              <view class="feature-text">支持车销访销，路线规划、拜访记录，随时随地销售商品</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon sales-icon-3">
                <text class="iconfont icon-renminbi"></text>
              </view>
              <view class="feature-text">支持销售提成，按金额、数量、回款、利润设计</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon sales-icon-4">
                <text class="iconfont icon-goods_hot_fill"></text>
              </view>
              <view class="feature-text">实现在线查商品、调价格、管库存、做促销、发物流</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 仓库管理功能 -->
      <view class="feature-block warehouse-feature">
        <view class="feature-header">
          <view class="feature-title">【仓库智能管理】</view>
          <view class="feature-subtitle">多仓/异地管理、智能补货，让库管高效、让损耗降低，让利润起飞！</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-left">
            <image src="https://mshop.bogoo.net/hsy_feature2.png" mode="widthFix" lazy-load="true"></image>
          </view>
          <view class="feature-list">
            <view class="feature-item">
              <view class="feature-icon warehouse-icon-1">
                <text class="iconfont icon-shangyun"></text>
              </view>
              <view class="feature-text">支持多店/异地/多仓库管理，商品溯源、库存周转率分析</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon warehouse-icon-2">
                <text class="iconfont icon-shuzihua"></text>
              </view>
              <view class="feature-text">支持按照最低库存、安全库存智能补货，避免缺货</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon warehouse-icon-3">
                <text class="iconfont icon-piaoju"></text>
              </view>
              <view class="feature-text">支持扫描条形码/二维码完成出入库、盘点</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon warehouse-icon-4">
                <text class="iconfont icon-goods_light"></text>
              </view>
              <view class="feature-text">支持批量商品标签制作，商品批次、保质期等多属性/多规格管理</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 线上商城功能 -->
      <view class="feature-block online-feature">
        <view class="feature-header">
          <view class="feature-title">【线上快人一步】</view>
          <view class="feature-subtitle">开通线上商城，打通门店数据，让业务24小时在线，增长快人一步！</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-right">
            <image src="https://mshop.bogoo.net/hsy_feature3.png" mode="widthFix" lazy-load="true"></image>
          </view>
          <view class="feature-list">
            <view class="feature-item">
              <view class="feature-icon online-icon-1">
                <text class="iconfont icon-lianjie"></text>
              </view>
              <view class="feature-text">门店和商城实现商品、库存、价格、订单、会员、优惠券统一</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon online-icon-2">
                <text class="iconfont icon-wuliu"></text>
              </view>
              <view class="feature-text">支持快递发货、同城配送和门店自提，多种配送模式</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon online-icon-3">
                <text class="iconfont icon-pingtai"></text>
              </view>
              <view class="feature-text">打通淘宝/天猫/京东/拼多多/快手/抖音等多平台，同步卖货</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 营销推广功能 -->
      <view class="feature-block marketing-feature">
        <view class="feature-header">
          <view class="feature-title">【营销再无死角】</view>
          <view class="feature-subtitle">30种营销策略，拼团、分销、直播带货......让获客不难、让财源广进！</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-left">
            <image src="https://mshop.bogoo.net/hsy_feature4.png" mode="widthFix" lazy-load="true"></image>
          </view>
          <view class="feature-list">
            <view class="feature-item">
              <view class="feature-icon marketing-icon-1">
                <text class="iconfont icon-lianmenglian"></text>
              </view>
              <view class="feature-text">微信群、朋友圈、QQ多渠道推广，渠道多销量多</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon marketing-icon-2">
                <text class="iconfont icon-hotfill"></text>
              </view>
              <view class="feature-text">秒杀、多人拼团、直播带货、满减满赠等30余种营销玩法</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon marketing-icon-3">
                <text class="iconfont icon-kuaisugaoxiao"></text>
              </view>
              <view class="feature-text">分销裂变促销量，朋友/客户帮你卖，分销返佣，高效获客</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 经营管理功能 -->
      <view class="feature-block business-feature">
        <view class="feature-header">
          <view class="feature-title">【经营降本增效】</view>
          <view class="feature-subtitle">手机查数据，收入、成本、利润实时掌握，经营异常及时预警，让管理不难！</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-right">
            <image src="https://mshop.bogoo.net/hsy_feature5.png" mode="widthFix" lazy-load="true"></image>
          </view>
          <view class="feature-list">
            <view class="feature-item">
              <view class="feature-icon business-icon-1">
                <text class="iconfont icon-tubiaozhutu"></text>
              </view>
              <view class="feature-text">手机查看经营数据，及时了解收入、成本、利润等，辅助经营决策</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon business-icon-2">
                <text class="iconfont icon-goods_light"></text>
              </view>
              <view class="feature-text">商品智能分析，畅销/滞销品、高毛利/低毛利品，实时把控</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon business-icon-3">
                <text class="iconfont icon-people"></text>
              </view>
              <view class="feature-text">智能客户分析，核心/重要/普通客户分层运营，提产出、防流失</view>
            </view>
            <view class="feature-item">
              <view class="feature-icon business-icon-4">
                <text class="iconfont icon-hot"></text>
              </view>
              <view class="feature-text">业务异常预警，避免商品滞销、客户流失、欠款超期等</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 行业解决方案 -->
      <view class="industry-section">
        <view class="section-header">
          <view class="main-title">典型行业<text class="highlight">使用方案</text></view>
          <view class="sub-title">定制级行业功能，为更好服务努力</view>
        </view>
        
        <!-- 现代化滑动行业卡片布局 -->
        <scroll-view scroll-x="true" class="industry-scroll" enhanced="true" show-scrollbar="false">
          <view class="industry-cards">
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon food">
                  <text class="iconfont icon-shipinxingye"></text>
                </view>
                <view class="industry-name">休闲零食</view>
                <view class="industry-desc">商品品类管理，有效期预警</view>
                <view class="industry-tag">已服务<text>1280+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon drink">
                  <text class="iconfont icon-niangjiuxingye"></text>
                </view>
                <view class="industry-name">酒水饮料</view>
                <view class="industry-desc">整件散件管理，包装物退回</view>
                <view class="industry-tag">已服务<text>860+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon fresh">
                  <text class="iconfont icon-nonglinmuyu"></text>
                </view>
                <view class="industry-name">生鲜冻品</view>
                <view class="industry-desc">库存损耗管理，线上订单发货</view>
                <view class="industry-tag">已服务<text>720+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon daily">
                  <text class="iconfont icon-shangyebaihuo"></text>
                </view>
                <view class="industry-name">日用百货</view>
                <view class="industry-desc">采购批次管理，滞销物分析</view>
                <view class="industry-tag">已服务<text>1540+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon clothes">
                  <text class="iconfont icon-fuzhuangxielei"></text>
                </view>
                <view class="industry-name">服装鞋帽</view>
                <view class="industry-desc">尺码款式管理，尾货促销策略</view>
                <view class="industry-tag">已服务<text>930+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon auto">
                  <text class="iconfont icon-qichezhizao-copy"></text>
                </view>
                <view class="industry-name">汽车配件</view>
                <view class="industry-desc">车型配件管理，经销商体系</view>
                <view class="industry-tag">已服务<text>650+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon hardware">
                  <text class="iconfont icon-jiajuxingye"></text>
                </view>
                <view class="industry-name">家具五金</view>
                <view class="industry-desc">五金品类管控，定制规格设置</view>
                <view class="industry-tag">已服务<text>790+</text>企业</view>
              </view>
            </view>
            
            <view class="industry-card">
              <view class="industry-card-inner">
                <view class="industry-icon chemical">
                  <text class="iconfont icon-huagongxingye"></text>
                </view>
                <view class="industry-name">化工涂料</view>
                <view class="industry-desc">储量计量管理，批次厂家溯源</view>
                <view class="industry-tag">已服务<text>480+</text>企业</view>
              </view>
            </view>
          </view>
        </scroll-view>
        
        <!-- 显示滑动提示 -->
        <view class="scroll-hint">
          <view class="hint-line"></view>
          <view class="hint-text">左右滑动查看更多</view>
          <view class="hint-line"></view>
        </view>
      </view>
      
      <!-- 服务保障 -->
      <view class="service-section">
        <view class="section-header">
          <view class="main-title">专业用心 <text class="highlight">服务</text></view>
          <view class="sub-title">服务有保障 售后无担忧</view>
        </view>
        
        <view class="service-grid">
          <view class="service-item">
            <view class="service-icon customer">
              <text class="iconfont icon-shouhoufuwu2"></text>
            </view>
            <view class="service-name">7*24小时</view>
            <view class="service-desc">在线客服 专业咨询</view>
          </view>
          <view class="service-item">
            <view class="service-icon video">
              <text class="iconfont icon-a-1zhuanyepeixun"></text>
            </view>
            <view class="service-name">视频教学</view>
            <view class="service-desc">视频指导 助你上手</view>
          </view>
          <view class="service-item">
            <view class="service-icon expert">
              <text class="iconfont icon-zhenshikexin"></text>
            </view>
            <view class="service-name">专家支持</view>
            <view class="service-desc">1对1支持 专业操作指导</view>
          </view>
          <view class="service-item">
            <view class="service-icon community">
              <text class="iconfont icon-shangyun"></text>
            </view>
            <view class="service-name">数据安全</view>
            <view class="service-desc">定期备份 多重加密</view>
          </view>
        </view>
      </view>
      
      <!-- 用户评价模块 -->
      <view class="testimonial-section">
        <view class="section-header">
          <view class="main-title">用户<text class="highlight">真实评价</text></view>
          <view class="sub-title">我们用实力赢得客户的信任</view>
        </view>
        
        <swiper class="testimonial-swiper" 
                circular="true" 
                autoplay="true" 
                interval="5000" 
                duration="500"
                previous-margin="60rpx"
                next-margin="60rpx">
          <swiper-item>
            <view class="testimonial-card">
              <view class="testimonial-content">
                "好生意系统太省心了，我们公司以前用Excel记账太乱，现在用手机就能随时查库存、看销售，效率提高了50%以上。"
              </view>
              <view class="testimonial-user">
                <view class="user-avatar">
                  <text class="avatar-text">张</text>
                </view>
                <view class="user-info">
                  <view class="user-name">张总</view>
                  <view class="user-company">武汉某食品贸易公司</view>
                </view>
              </view>
            </view>
          </swiper-item>
          
          <swiper-item>
            <view class="testimonial-card">
              <view class="testimonial-content">
                "系统功能齐全，而且很容易上手，我们整个团队一周就适应了。现在可以轻松管理上千种SKU，再也不怕库存积压了。"
              </view>
              <view class="testimonial-user">
                <view class="user-avatar">
                  <text class="avatar-text">李</text>
                </view>
                <view class="user-info">
                  <view class="user-name">李经理</view>
                  <view class="user-company">武汉某日用百货经销商</view>
                </view>
              </view>
            </view>
          </swiper-item>
          
          <swiper-item>
            <view class="testimonial-card">
              <view class="testimonial-content">
                "好生意的线上商城功能非常强大，我们在疫情期间靠这个实现了线上转型，现在线上业务占比已经达到40%，真的很感谢。"
              </view>
              <view class="testimonial-user">
                <view class="user-avatar">
                  <text class="avatar-text">王</text>
                </view>
                <view class="user-info">
                  <view class="user-name">王老板</view>
                  <view class="user-company">湖北某家居用品公司</view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 体验咨询区域 -->
      <view class="cta-section">
        <view class="cta-content">
          <view class="cta-title">立即体验数字化经营管理</view>
          <view class="cta-desc">好生意助力企业高效经营，提升业绩</view>
          <view class="cta-buttons">
            <button class="cta-btn outline" bindtap="makePhoneCall">
              <text class="btn-text">电话咨询</text>
            </button>
            <button class="cta-btn primary" open-type="contact">微信咨询</button>
          </view>
        </view>
      </view>
      <!-- 底部留白 -->
      <view style="height: 40rpx;"></view>      
      
      <!-- 616大厦悬浮按钮 -->
      <!-- <view class="floating-btn" bindtap="navigateToMore">
        <view class="floating-circle">616</view>
        <view class="floating-label">热线咨询</view>
      </view> -->
    </view>
  </scroll-view>

  <!-- 使用底部导航栏组件 -->
  <bottom-nav currentTab="hot" theme="blue" bindbottomnav="onBottomNavEvent"></bottom-nav>
  
  <!-- 添加悬浮咨询按钮 -->
  <float-consult positionKey="pdhsy"></float-consult>
</view> 