// 引入我们创建的价格计算服务
const pricingService = require('../services/pricing.service');

/**
 * @description 接收前端请求，计算订单项价格
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.calculatePrice = async (req, res) => {
  try {
    // 从请求体中获取所有计算价格所需的参数
    const {
      product_id,
      user_count,
      account_count,
      selected_features,
      duration_months
    } = req.body;

    // 参数验证
    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: '产品ID不能为空'
      });
    }

    if (!user_count || user_count < 1) {
      return res.status(400).json({
        success: false,
        message: '使用人数必须大于0'
      });
    }

    if (!account_count || account_count < 1) {
      return res.status(400).json({
        success: false,
        message: '账套数必须大于0'
      });
    }

    // 调用服务层的计算函数
    const calculatedPrice = await pricingService.calculateProductOrderPrice({
      product_id,
      user_count,
      account_count,
      selected_features: selected_features || [],
      duration_months: duration_months || 12
    });

    // 将计算结果以JSON格式返回给前端
    res.status(200).json({
      success: true,
      standard_price: calculatedPrice,
      calculation_details: {
        product_id,
        user_count,
        account_count,
        selected_features: selected_features || [],
        duration_months: duration_months || 12
      }
    });

  } catch (error) {
    // 如果在计算过程中发生任何错误（例如产品不存在）
    // 捕获错误并返回一个清晰的错误信息
    console.error('价格计算API出错:', error);
    res.status(400).json({
      success: false,
      message: error.message || '计算价格时发生未知错误。'
    });
  }
};