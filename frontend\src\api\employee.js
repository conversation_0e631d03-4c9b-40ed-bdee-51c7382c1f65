import service from '@/utils/request_extra.js';

const API_PATH = '/employees';

/**
 * [新增] 获取下一个可用的员工工号
 */
export const getNextEmployeeNumber = () => {
  return service.get(`${API_PATH}/next-id`);
}

/**
 * 获取员工列表的函数
 * @returns Promise
 */
export const getEmployees = () => {
  // 发送 GET 请求到 /api/employees
  return service.get(API_PATH);
};

/**
 * 创建一个新员工
 * @param {object} employeeData - 包含新员工信息的对象
 */
export const createEmployee = (employeeData) => {
  return service.post(API_PATH, employeeData);
};

/**
 * 更新一个员工的信息
 * @param {number} id - 要更新的员工的ID
 * @param {object} employeeData - 包含更新信息的对象
 */
export const updateEmployee = (id, employeeData) => {
  return service.put(`${API_PATH}/${id}`, employeeData);
};

/**
 * 删除一个员工
 * @param {number} id - 要删除的员工的ID
 */
export const deleteEmployee = (id) => {
  return service.delete(`${API_PATH}/${id}`);
}; 