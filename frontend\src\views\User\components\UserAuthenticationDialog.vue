<!-- 
  这个组件用于显示和编辑用户的认证信息。
  它接收两个 props：
  - modelValue: 控制对话框的显示/隐藏
  - userId: 用户ID
  
-->
<script setup>
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getUserAuthentication, upsertUserAuthentication } from '@/api/user_authentication.js';

// -- Props and Emits --
const props = defineProps({
  modelValue: Boolean, // 控制对话框显示/隐藏
  userId: { // 接收父组件传来的用户ID
    type: [Number, String],
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'success']);

// -- 响应式状态 --
const internalVisible = ref(props.modelValue);
const form = ref({
  id_card: '',
  bank_card: '',
  remark: ''
});
const loading = ref(false);

// -- 监听器 --
watch(() => props.modelValue, (val) => {
  internalVisible.value = val;
});

watch(internalVisible, (val) => {
  if (!val) {
    emit('update:modelValue', false);
  }
});

// 2. 在组件挂载时自动获取数据
onMounted(() => {
  if (props.userId) {
    fetchData();
  }
});

// -- 方法 --
const fetchData = async () => {
  loading.value = true;
  try {
    const data = await getUserAuthentication(props.userId);
    
    if (data) {
      // 使用更健壮的逐个属性赋值
      form.value.id_card = data.id_card || '';
      form.value.bank_card = data.bank_card || '';
      form.value.remark = data.remark || '';
    } else {
      // 正常情况下，如果没数据，会走到catch的404逻辑
      form.value = { id_card: '', bank_card: '', remark: '' };
    }
  } catch (error) {
    // 404表示无记录，是正常情况，重置表单即可，无需提示用户
    if (error.response?.status !== 404) {
      ElMessage.error('获取认证信息失败');
    }
    form.value = { id_card: '', bank_card: '', remark: '' };
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  loading.value = true;
  try {
    await upsertUserAuthentication(props.userId, form.value);
    ElMessage.success('保存成功');
    emit('success');
    internalVisible.value = false;
  } catch (error) {
    ElMessage.error('保存失败');
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  internalVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="internalVisible"
    title="用户认证信息"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="form" label-width="120px" v-loading="loading">
      <el-form-item label="身份证号">
        <el-input v-model="form.id_card" />
      </el-form-item>
      <el-form-item label="银行卡号">
        <el-input v-model="form.bank_card" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template> 