Component({
  /**
   * 组件的属性列表
   */
  properties: {
    position: {
      type: Object,
      value: {
        right: '30rpx',
        bottom: '260rpx'
      }
    },
    // 位置键名（用于存储不同页面的不同位置）
    positionKey: {
      type: String,
      value: 'default'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isExpanded: false,
    activeMenuId: 1, // 默认选中第一项
    autoplayTimer: null,
    // 按钮样式
    buttonStyle: '',
    // 菜单样式
    menuStyle: '',
    // 回电表单相关
    showCallbackForm: false,
    phoneNumber: '',
    name: '',
    remark: '',
    callbacks: []
  },

  lifetimes: {
    attached: function() {
      // 组件创建时，根据传入或存储的position设置样式
      this.updatePositionStyle();
      
      // 尝试从本地存储加载回电记录
      this.loadCallbacks();
    },
    
    detached: function() {
      // 组件销毁时清除定时器
      this.clearAutoplayTimer();
    }
  },

  observers: {
    'position': function(position) {
      // 当position属性变化时，更新样式
      this.updatePositionStyle();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新位置样式
    updatePositionStyle: function() {
      const position = this.properties.position;
      const buttonStyle = `right:${position.right};bottom:${position.bottom};`;
      
      // 向上垂直排列并保持中线对齐
      const menuStyle = `right:${position.right};bottom:calc(${position.bottom} + 140rpx);`;
      
      this.setData({
        buttonStyle,
        menuStyle
      });
    },
    
    // 切换菜单展开状态
    toggleMenu: function() {
      const isExpanded = !this.data.isExpanded;
      
      this.setData({
        isExpanded: isExpanded
      });
      
      if (isExpanded) {
        // 展开菜单时启动自动轮播
        this.startAutoplay();
      } else {
        // 收起菜单时停止自动轮播
        this.clearAutoplayTimer();
      }
    },
    
    // 关闭菜单
    closeMenu: function() {
      if (this.data.isExpanded) {
        this.setData({
          isExpanded: false
        });
        this.clearAutoplayTimer();
      }
    },
    
    // 点击遮罩关闭菜单
    onBackdropClick: function() {
      this.closeMenu();
    },
    
    // 启动自动轮播
    startAutoplay: function() {
      // 先清除可能存在的定时器
      this.clearAutoplayTimer();
      
      // 设置初始选中项
      this.setData({
        activeMenuId: 1
      });
      
      // 创建新的定时器，每3秒切换一次选中项
      const autoplayTimer = setInterval(() => {
        // 当前选中项
        let currentId = this.data.activeMenuId;
        
        // 计算下一个要选中的项
        let nextId = currentId + 1;
        if (nextId > 3) {
          nextId = 1; // 循环回第一项
        }
        
        // 更新选中项
        this.setData({
          activeMenuId: nextId
        });
      }, 3000); // 3秒切换一次
      
      // 保存定时器ID
      this.setData({
        autoplayTimer: autoplayTimer
      });
    },
    
    // 清除自动轮播定时器
    clearAutoplayTimer: function() {
      if (this.data.autoplayTimer) {
        clearInterval(this.data.autoplayTimer);
        this.setData({
          autoplayTimer: null
        });
      }
    },

    // 处理菜单项点击
    handleAction: function(e) {
      const action = e.currentTarget.dataset.action;
      const id = e.currentTarget.dataset.id;
      
      // 设置为当前选中项
      this.setData({
        activeMenuId: id
      });
      
      // 重置自动轮播
      if (this.data.isExpanded) {
        this.clearAutoplayTimer();
        this.startAutoplay();
      }
      
      switch(action) {
        case 'makePhoneCall':
          wx.makePhoneCall({
            phoneNumber: '131-0068-5010',
            success() {
              console.log('拨打电话成功');
            },
            fail() {
              wx.showModal({
                title: '提示',
                content: '拨打电话失败，请手动拨打131-0068-5010',
                showCancel: false
              });
            }
          });
          break;
          
        case 'waitCallback':
          // 显示回电表单
          this.showCallbackForm();
          break;
      }
    },
    
    // 显示回电表单
    showCallbackForm: function() {
      // 关闭菜单
      this.closeMenu();
      
      // 显示表单
      this.setData({
        showCallbackForm: true,
        phoneNumber: '',
        name: '',
        remark: ''
      });
    },
    
    // 关闭回电表单
    closeCallbackForm: function() {
      this.setData({
        showCallbackForm: false
      });
    },
    
    // 手机号输入事件
    onPhoneInput: function(e) {
      this.setData({
        phoneNumber: e.detail.value
      });
    },
    
    // 称呼输入事件
    onNameInput: function(e) {
      this.setData({
        name: e.detail.value
      });
    },
    
    // 备注输入事件
    onRemarkInput: function(e) {
      this.setData({
        remark: e.detail.value
      });
    },
    
    // 提交回电表单
    submitCallbackForm: function() {
      const { phoneNumber, name, remark } = this.data;
      
      // 验证手机号
      if (!phoneNumber || phoneNumber.length !== 11) {
        wx.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }
      
      // 创建回电记录
      const callback = {
        phoneNumber,
        name,
        remark,
        time: new Date().toLocaleString(),
        status: '待处理' // 可以是'待处理'、'已处理'等状态
      };
      
      // 添加到回电记录列表
      const callbacks = [...this.data.callbacks, callback];
      
      // 保存到本地存储
      this.saveCallbacks(callbacks);
      
      // 更新状态
      this.setData({
        callbacks,
        showCallbackForm: false
      });
      
      // 提示用户
      wx.showToast({
        title: '提交成功，我们将尽快与您联系',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 加载回电记录
    loadCallbacks: function() {
      try {
        const callbacks = wx.getStorageSync('callbacks') || [];
        this.setData({ callbacks });
      } catch (e) {
        console.error('加载回电记录失败', e);
      }
    },
    
    // 保存回电记录
    saveCallbacks: function(callbacks) {
      try {
        wx.setStorageSync('callbacks', callbacks);
      } catch (e) {
        console.error('保存回电记录失败', e);
      }
    }
  }
}) 