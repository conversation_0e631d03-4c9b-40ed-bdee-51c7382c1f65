const express = require('express');
const { employee, user } = require('../middleware/auth');
const {
  createUser,
  getAllUsers,
  getUserById,
  updateUser,
  updateUserProfile,
  deleteUser,
  getNextUserId,
  getNextPartnerId
} = require('../controllers/user.controller');

const router = express.Router();

// 获取下一个可用ID - 需要员工管理员权限
router.get('/next-user-id', employee.verifyAdmin, getNextUserId);
router.get('/next-partner-id', employee.verifyAdmin, getNextPartnerId);

// 当有 GET 请求访问 /api/users 时，调用 getAllUsers 函数 - 需要员工权限
router.get('/', employee.verifyEmployee, getAllUsers);
router.get('/:id', employee.verifyEmployee, getUserById); // 新增的路由 - 需要员工权限
router.post('/', employee.verifyAdmin, createUser); // 创建用户需要管理员权限
router.put('/:id', employee.verifyAdmin, updateUser); // 管理员更新用户需要管理员权限
// 用户自我更新路由（用户可以更新自己的信息）
router.put('/profile/:id', user.verifyUser, user.requireSelfAccess(), updateUserProfile);
router.delete('/:id', employee.verifyAdmin, deleteUser); // 删除用户需要管理员权限

module.exports = router; 