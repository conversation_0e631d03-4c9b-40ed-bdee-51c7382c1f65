// pages/login/login.js
const apiService = require('../../utils/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 隐私协议相关
    agreedProtocol: false,

    // 账号密码登录弹窗
    showPasswordModal: false,
    loginForm: {
      username: '',
      password: ''
    },

    // 加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已经登录
    if (apiService.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/me/me'
      });
      return;
    }
    
    // 从全局状态获取协议同意状态
    const app = getApp();
    if (app.globalData && app.globalData.agreedProtocol) {
      this.setData({
        agreedProtocol: app.globalData.agreedProtocol
      });
    }
  },

  /**
   * 切换隐私协议同意状态
   */
  toggleProtocol() {
    const newState = !this.data.agreedProtocol;
    this.setData({
      agreedProtocol: newState
    });
    
    // 同步到全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.agreedProtocol = newState;
    }
  },



  /**
   * 处理微信登录按钮点击（未同意协议时）
   */
  handleWechatLogin() {
    console.log('点击微信登录按钮 - 未同意协议');

    // 显示协议提示
    this.showProtocolTip();
  },

  /**
   * 微信授权手机号登录回调
   */
  onGetPhoneNumber(e) {
    console.log('微信手机号快速验证回调:', e);

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 用户同意授权
      const { code } = e.detail;

      wx.showLoading({
        title: '验证中...'
      });

      // 调用后端接口进行手机号验证登录
      this.phoneNumberLogin(code);
    } else if (e.detail.errno === 1400001) {
      // 额度不足
      wx.showModal({
        title: '提示',
        content: '手机号快速验证次数已达上限，请选择其他登录方式',
        showCancel: false,
        success: () => {
          // 可以引导用户使用其他登录方式
        }
      });
    } else {
      // 用户拒绝授权或其他错误
      console.log('用户拒绝手机号授权或发生错误');
      wx.showToast({
        title: '验证取消',
        icon: 'none'
      });
    }
  },

  /**
   * 手机号验证登录
   */
  async phoneNumberLogin(code) {
    try {
      const response = await apiService.request('/auth/phone-login', {
        method: 'POST',
        data: {
          code: code,
          loginType: 'phone_verification'
        }
      });

      wx.hideLoading();

      if (response.success) {
        // 保存登录信息
        apiService.saveLoginInfo(response);

        // 登录成功
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 跳转到个人中心
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/me/me'
          });
        }, 800);
      } else {
        // 合并错误信息，只显示一次
        const errorMessage = response.message || '登录失败';
        const errorDetails = response.details;

        // 如果有详细信息且与主要信息不同，则合并显示
        let finalMessage = errorMessage;
        if (errorDetails && errorDetails !== errorMessage) {
          finalMessage = `${errorMessage}：${errorDetails}`;
        }

        wx.showToast({
          title: finalMessage,
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('手机号验证登录失败:', error);

      // 根据错误类型显示不同提示
      let errorMessage = '微信授权失败，请重试';

      if (error.message && error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message && error.message.includes('用户拒绝')) {
        errorMessage = '需要授权手机号才能登录';
      } else if (error.statusCode === 500) {
        errorMessage = '服务器异常，请稍后重试';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },



  /**
   * 显示账号密码登录弹窗
   */
  showPasswordLogin() {
    this.setData({
      showPasswordModal: true
    });
  },

  /**
   * 显示协议提示
   */
  showProtocolTip() {
    wx.showModal({
      title: '提示',
      content: '请阅读并勾选用户协议',
      showCancel: false,
      confirmText: '我知道了',
      success: () => {
        // 可以在这里添加引导用户到协议区域的逻辑
      }
    });
  },

  /**
   * 隐藏账号密码登录弹窗
   */
  hidePasswordLogin() {
    this.setData({
      showPasswordModal: false,
      loginForm: {
        username: '',
        password: ''
      }
    });
  },

  /**
   * 阻止事件冒泡
   */
  preventClose() {
    // 阻止点击弹窗内容区域时关闭弹窗
  },

  /**
   * 用户名输入
   */
  onUsernameInput(e) {
    this.setData({
      'loginForm.username': e.detail.value
    });
  },

  /**
   * 密码输入
   */
  onPasswordInput(e) {
    this.setData({
      'loginForm.password': e.detail.value
    });
  },

  /**
   * 切换密码显示状态
   */
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  /**
   * 处理账号密码登录
   */
  async handlePasswordLogin() {
    const { username, password } = this.data.loginForm;
    
    // 输入验证
    if (!username.trim()) {
      wx.showToast({
        title: '请输入手机号/姓名/昵称',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 简单的输入格式验证
    const trimmedUsername = username.trim();
    if (trimmedUsername.length < 2) {
      wx.showToast({
        title: '用户名至少需要2个字符',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (password.trim().length < 6) {
      wx.showToast({
        title: '密码至少需要6位',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showLoading({
      title: '登录中...'
    });

    try {
      const response = await apiService.request('/auth/password-login', {
        method: 'POST',
        data: {
          username: username.trim(),
          password: password.trim(),
          loginType: 'password'
        }
      });

      wx.hideLoading();

      if (response.success) {
        // 保存登录信息
        apiService.saveLoginInfo(response);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        this.hidePasswordLogin();

        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/me/me'
          });
        }, 800);
      } else {
        // 合并错误信息，只显示一次
        const errorMessage = response.message || '登录失败';
        const errorDetails = response.details;

        // 如果有详细信息且与主要信息不同，则合并显示
        let finalMessage = errorMessage;
        if (errorDetails && errorDetails !== errorMessage) {
          finalMessage = `${errorMessage}：${errorDetails}`;
        }

        wx.showToast({
          title: finalMessage,
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('账号密码登录失败:', error);

      // 根据错误类型显示不同提示
      let errorMessage = '登录失败，请重试';

      if (error.message && error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message && error.message.includes('timeout')) {
        errorMessage = '请求超时，请重试';
      } else if (error.statusCode === 500) {
        errorMessage = '服务器异常，请稍后重试';
      } else if (error.statusCode === 401) {
        errorMessage = '登录信息有误，请检查';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 跳转到注册页面
   */
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  /**
   * 阻止滚动穿透
   */
  preventTouchMove() {
    return false;
  },


});
