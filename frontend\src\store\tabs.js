// 标签页管理器 (store/tabs.js)：管理多标签页的状态
// 负责标签页的打开、关闭、切换等功能，与现有的auth.js保持相同的设计模式

import { reactive, readonly } from 'vue';
import router from '@/router';

// 标签页状态管理
const state = reactive({
  // 当前打开的标签页列表
  tabs: [],
  // 当前激活的标签页
  activeTab: '',
  // 缓存的组件实例（用于keep-alive）
  cachedViews: []
});

/**
 * 根据路由信息生成标签页对象
 * @param {Object} route - Vue Router的路由对象
 * @returns {Object} 标签页对象
 */
function createTab(route) {
  // 不为根路径或登录页创建标签页
  if (route.path === '/' || route.path === '/login') {
    return null;
  }

  // 获取当前路由的meta信息，优先从matched的最后一个路由记录获取
  const currentRouteRecord = route.matched[route.matched.length - 1];
  const meta = currentRouteRecord?.meta || route.meta || {};

  return {
    // 使用路由的fullPath作为唯一标识
    name: route.fullPath,
    // 标签页显示的标题，优先使用meta.title，否则使用路由名称
    title: meta.title || route.name || '未命名页面',
    // 路由路径
    path: route.path,
    // 完整路径（包含查询参数）
    fullPath: route.fullPath,
    // 路由参数
    params: route.params,
    // 查询参数
    query: route.query,
    // 是否可以关闭（首页等特殊页面可能不允许关闭）
    closable: meta.closable !== false,
    // 是否需要缓存
    keepAlive: meta.keepAlive !== false
  };
}

/**
 * 添加新标签页
 * @param {Object} route - 路由对象
 */
function addTab(route) {
  const tab = createTab(route);

  // 如果createTab返回null，则不添加标签页
  if (!tab) {
    return;
  }

  // 检查是否已存在相同的标签页
  const existingIndex = state.tabs.findIndex(t => t.name === tab.name);

  if (existingIndex > -1) {
    // 如果已存在，更新标签页信息（可能参数有变化）
    state.tabs[existingIndex] = tab;
  } else {
    // 如果不存在，添加新标签页
    state.tabs.push(tab);
  }

  // 设置为当前激活标签页
  state.activeTab = tab.name;

  // 如果需要缓存，添加到缓存列表
  if (tab.keepAlive && !state.cachedViews.includes(tab.name)) {
    state.cachedViews.push(tab.name);
  }
}

/**
 * 关闭标签页
 * @param {String} tabName - 要关闭的标签页名称
 */
function closeTab(tabName) {
  const tabIndex = state.tabs.findIndex(t => t.name === tabName);
  
  if (tabIndex === -1) return;
  
  const tab = state.tabs[tabIndex];
  
  // 检查是否可以关闭
  if (!tab.closable) {
    console.warn('该标签页不允许关闭');
    return;
  }
  
  // 从标签页列表中移除
  state.tabs.splice(tabIndex, 1);
  
  // 从缓存中移除
  const cacheIndex = state.cachedViews.indexOf(tabName);
  if (cacheIndex > -1) {
    state.cachedViews.splice(cacheIndex, 1);
  }
  
  // 如果关闭的是当前激活的标签页，需要切换到其他标签页
  if (state.activeTab === tabName) {
    if (state.tabs.length > 0) {
      // 优先切换到右侧标签页，如果没有则切换到左侧
      const nextTab = state.tabs[tabIndex] || state.tabs[tabIndex - 1];
      if (nextTab) {
        setActiveTab(nextTab.name);
        // 导航到对应路由
        router.push(nextTab.fullPath);
      }
    } else {
      // 如果没有其他标签页了，跳转到首页
      router.push('/');
    }
  }
}

/**
 * 关闭其他标签页
 * @param {String} keepTabName - 要保留的标签页名称
 */
function closeOtherTabs(keepTabName) {
  // 找到要保留的标签页
  const keepTab = state.tabs.find(t => t.name === keepTabName);
  if (!keepTab) return;
  
  // 过滤出可关闭的其他标签页
  const tabsToClose = state.tabs.filter(t => t.name !== keepTabName && t.closable);
  
  // 从缓存中移除这些标签页
  tabsToClose.forEach(tab => {
    const cacheIndex = state.cachedViews.indexOf(tab.name);
    if (cacheIndex > -1) {
      state.cachedViews.splice(cacheIndex, 1);
    }
  });
  
  // 只保留不可关闭的标签页和指定的标签页
  state.tabs = state.tabs.filter(t => t.name === keepTabName || !t.closable);
  
  // 设置为当前激活标签页
  setActiveTab(keepTabName);
}

/**
 * 关闭所有标签页
 */
function closeAllTabs() {
  // 只保留不可关闭的标签页
  const unclosableTabs = state.tabs.filter(t => !t.closable);
  
  // 清除所有可关闭标签页的缓存
  state.tabs.filter(t => t.closable).forEach(tab => {
    const cacheIndex = state.cachedViews.indexOf(tab.name);
    if (cacheIndex > -1) {
      state.cachedViews.splice(cacheIndex, 1);
    }
  });
  
  state.tabs = unclosableTabs;
  
  if (unclosableTabs.length > 0) {
    setActiveTab(unclosableTabs[0].name);
    router.push(unclosableTabs[0].fullPath);
  } else {
    state.activeTab = '';
    router.push('/');
  }
}

/**
 * 设置当前激活的标签页
 * @param {String} tabName - 标签页名称
 */
function setActiveTab(tabName) {
  const tab = state.tabs.find(t => t.name === tabName);
  if (tab) {
    state.activeTab = tabName;
  }
}

/**
 * 刷新标签页（清除缓存）
 * @param {String} tabName - 要刷新的标签页名称
 */
function refreshTab(tabName) {
  // 从缓存中移除
  const cacheIndex = state.cachedViews.indexOf(tabName);
  if (cacheIndex > -1) {
    state.cachedViews.splice(cacheIndex, 1);
  }
  
  // 重新添加到缓存（如果需要缓存）
  const tab = state.tabs.find(t => t.name === tabName);
  if (tab && tab.keepAlive) {
    // 延迟添加，确保组件重新创建
    setTimeout(() => {
      state.cachedViews.push(tabName);
    }, 0);
  }
}

/**
 * 初始化标签页（应用启动时调用）
 */
function initTabs() {
  // 可以从localStorage恢复标签页状态
  const savedTabs = localStorage.getItem('savedTabs');
  if (savedTabs) {
    try {
      const parsed = JSON.parse(savedTabs);
      state.tabs = parsed.tabs || [];
      state.activeTab = parsed.activeTab || '';
      state.cachedViews = parsed.cachedViews || [];
    } catch (error) {
      console.warn('恢复标签页状态失败:', error);
    }
  }
}

/**
 * 保存标签页状态到localStorage
 */
function saveTabs() {
  const tabsData = {
    tabs: state.tabs,
    activeTab: state.activeTab,
    cachedViews: state.cachedViews
  };
  localStorage.setItem('savedTabs', JSON.stringify(tabsData));
}

// 监听路由变化，自动保存状态
let saveTimer = null;
function autoSave() {
  if (saveTimer) clearTimeout(saveTimer);
  saveTimer = setTimeout(saveTabs, 1000); // 延迟1秒保存，避免频繁操作
}

// 导出标签页管理功能
export const useTabs = () => {
  return {
    state: readonly(state),
    addTab,
    closeTab,
    closeOtherTabs,
    closeAllTabs,
    setActiveTab,
    refreshTab,
    initTabs,
    saveTabs,
    autoSave
  };
};
