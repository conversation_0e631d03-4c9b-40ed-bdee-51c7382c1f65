/**
 * 合伙人小程序个人中心页面
 * 显示合伙人基本信息、收益统计等
 * 提供登录状态管理和页面跳转功能
 */

// 获取应用实例
const app = getApp();
const api = require('../../utils/api');

// 合伙人个人中心页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,          // 合伙人用户基本信息
    isPartner: false,        // 是否是合伙人（权限验证）
    isPending: false,        // 是否是待审核合伙人

    // 收益相关数据 - 3个清晰的收益字段
    pending_earnings: '0.00',       // 待发放收益
    paid_earnings: '0.00',          // 已发放收益
    total_earnings: '0.00',         // 累计总收益

    // 佣金比例数据
    commission_base: '0.00',        // 基础分润比例
    commission_extra: '0.00',       // 额外分润比例
    commission_total: '0.00',       // 总佣金比例（计算后的值）
    order_count: 0,                 // 订单数量

    loading: false,          // 页面加载状态
    isDataLoaded: false,     // 数据是否已加载成功
    baseRateStyle: '',       // 基础分润比例样式（用于圆形进度条）
    extraRateStyle: ''       // 活动加点比例样式（用于圆形进度条）
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 页面首次加载时检查登录状态并初始化数据
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    // 每次显示页面时检查登录状态
    const token = wx.getStorageSync('access_token');
    if (!token) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    // 如果需要刷新数据
    if (this.needRefresh) {
      await this.refreshPageData();
      this.needRefresh = false;
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function() {
    console.log('触发下拉刷新');
    // 强制清除userInfo缓存，确保获取最新数据
    wx.removeStorageSync('userInfo');
    this.setData({ userInfo: null });
    
    await this.refreshPageData(true);
    wx.stopPullDownRefresh();
    
    // 显示刷新成功提示
    wx.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000
    });
  },

  async loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({ userInfo });
  },

  /**
   * 加载收益数据和佣金比例
   * 从后端API获取真实的收益数据
   */
  async loadEarningsData() {
    try {
      const token = wx.getStorageSync('access_token');

      // 如果未登录，则不请求数据
      if (!token) {
        return;
      }

      console.log('开始获取合伙人收益数据...');

      // 调用后端API获取真实收益数据
      const result = await api.getPartnerEarnings();
      console.log('API返回结果:', result);

      if (result.success) {
        const {
          pending_earnings,
          paid_earnings,
          total_earnings,
          commission_base,
          commission_extra,
          order_count
        } = result.data;

        // 计算总佣金比例
        const baseRate = parseFloat(commission_base) || 0.06;
        const extraRate = parseFloat(commission_extra) || 0.00;
        const totalRate = ((baseRate + extraRate) * 100).toFixed(2);

        // 直接使用后端返回的3个清晰的收益字段
        this.setData({
          pending_earnings: pending_earnings || '0.00',
          paid_earnings: paid_earnings || '0.00',
          total_earnings: total_earnings || '0.00',
          commission_base: baseRate,  // 确保是数字类型
          commission_extra: extraRate, // 确保是数字类型
          commission_total: totalRate, // 计算后的总佣金比例（百分比字符串）
          order_count: order_count || 0,
          isDataLoaded: true
        });

        console.log('收益数据加载完成:', {
          pending_earnings,
          paid_earnings,
          total_earnings,
          commission_base,
          commission_extra,
          order_count
        });



      } else {
        console.error('获取收益数据失败:', result.message);
        // 使用默认数据
        this.setData({
          pending_earnings: '0.00',
          paid_earnings: '0.00',
          total_earnings: '0.00',
          commission_base: 0.06,
          commission_extra: 0.00,
          commission_total: '6.00',
          order_count: 0,
          isDataLoaded: true
        });
      }

    } catch (error) {
      console.error('获取收益数据失败:', error);
      // 即使获取失败也将isDataLoaded设为true，避免显示加载错误
      this.setData({
        pending_earnings: '0.00',
        paid_earnings: '0.00',
        total_earnings: '0.00',
        commission_base: 0.06,
        commission_extra: 0.00,
        commission_total: '6.00',
        order_count: 0,
        isDataLoaded: true
      });
    }
  },

  /**
   * 检查登录状态并加载用户数据
   */
  async checkLoginStatus() {
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');

    if (!token) {
      // 未登录，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    if (userInfo) {
      console.log('本地存储的用户信息:', userInfo);

      // 检查是否为合伙人
      // 检查多种可能的合伙人标识字段
      const isPartner = userInfo.is_partner === 1 || userInfo.is_partner === true || userInfo.is_partner === '1';

      if (!isPartner) {
        wx.showModal({
          title: '权限不足',
          content: '您不是合伙人，无法访问此页面',
          showCancel: false,
          success: () => {
            api.clearLoginInfo();
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
        return;
      }

      this.setData({
        userInfo,
        isPartner: true
      });

      // 获取最新的用户信息
      await this.getCurrentUserInfo();
    } else {
      // 没有本地用户信息，尝试从服务器获取
      await this.getCurrentUserInfo();
    }

    // 加载收益数据
    await this.loadEarningsData();
  },

  /**
   * 从服务器获取当前用户信息
   */
  async getCurrentUserInfo() {
    try {
      console.log('从服务器获取当前用户信息');

      const result = await api.getCurrentUser();
      console.log('获取用户信息响应:', result);

      if (result && result.id) {
        // 检查是否为合伙人
        if (!result.is_partner) {
          wx.showModal({
            title: '权限不足',
            content: '您不是合伙人，无法访问此页面',
            showCancel: false,
            success: () => {
              api.clearLoginInfo();
              wx.reLaunch({
                url: '/pages/login/login'
              });
            }
          });
          return;
        }

        // 保存用户信息到本地存储
        wx.setStorageSync('user_info', result);

        // 更新页面数据
        this.setData({
          userInfo: result,
          isPartner: true,
          isDataLoaded: true
        });

        console.log('用户信息更新完成:', result);
      } else {
        throw new Error('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取当前用户信息失败:', error);

      // 如果获取失败，可能是token过期，跳转到登录页
      wx.showModal({
        title: '获取用户信息失败',
        content: '请重新登录',
        showCancel: false,
        success: () => {
          api.clearLoginInfo();
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      });
    }
  },



  /**
   * 页面整体数据刷新
   */
  async refreshPageData(forceRefresh = false) {
    try {
      this.setData({ loading: true });

      if (forceRefresh) {
        console.log('强制刷新所有数据');
      }

      // 重新获取用户信息和收益数据
      await Promise.all([
        this.getCurrentUserInfo(),
        this.loadEarningsData()
      ]);

    } catch (error) {
      console.error('刷新页面数据失败:', error);
      // 确保加载状态完成，即使发生错误
      this.setData({ isDataLoaded: true });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 退出登录
   */
  handleLogout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的登录信息
          api.clearLoginInfo();

          // 重置全局状态
          const app = getApp();
          app.globalData.isLoggedIn = false;
          app.globalData.userInfo = null;
          app.globalData.token = '';
          app.globalData.isPartner = false;

          // 显示退出成功提示
          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500
          });

          // 跳转到登录页
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  },



  /**
   * 跳转到收益详情页面
   */
  goToEarningsDetail() {
    console.log('点击查看详情按钮，准备跳转到收益页面');
    wx.switchTab({
      url: '/pages/earnings/earnings',
      success: function(res) {
        console.log('跳转成功:', res);
      },
      fail: function(err) {
        console.error('跳转失败:', err);
        // 如果switchTab失败，尝试使用navigateTo
        wx.navigateTo({
          url: '/pages/earnings/earnings'
        });
      }
    });
  },

  /**
   * 跳转到修改密码页面
   */
  goToChangePassword() {
    wx.navigateTo({
      url: '/pages/change-password/change-password'
    });
  },

  /**
   * 复制合伙人编号
   */
  copyPartnerCode: function() {
    const userInfo = this.data.userInfo;
    const partnerId = userInfo.partner_id;

    if (partnerId) {
      wx.setClipboardData({
        data: String(partnerId),
        success: () => {
          wx.showToast({
            title: '合伙人编号已复制',
            icon: 'success',
            duration: 2000
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无合伙人编号',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 跳转到认证信息页面
   */
  goToAuthInfo() {
    wx.navigateTo({
      url: '/pages/auth-info/auth-info'
    });
  },

  /**
   * 跳转到修改密码页面
   */
  goToChangePassword() {
    wx.navigateTo({
      url: '/pages/change-password/change-password'
    });
  },

  /**
   * 处理退出登录
   */
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          api.clearLoginInfo();

          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  }
});