<template>
  <div class="asset-list-page">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">资产管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="handleCreate" size="default">
            <el-icon><Plus /></el-icon>
            新增资产
          </el-button>
          <el-button @click="refreshData" :loading="loading" size="default">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="资产ID">
          <el-input
            v-model="searchForm.asset_id"
            placeholder="请输入资产ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="企业">
          <el-select
            v-model="searchForm.enterprise_id"
            placeholder="请选择企业"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in enterpriseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="在线" value="在线" />
            <el-option label="过期" value="过期" />
          </el-select>
        </el-form-item>

        <el-form-item label="产品-版本">
          <el-input
            v-model="searchForm.product_name"
            placeholder="请输入产品名称"
            clearable
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="产品到期日">
          <el-select
            v-model="searchForm.product_expiry_date"
            placeholder="请选择过滤条件"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="全部" />
            <el-option label="近一个月到期" value="近一个月到期" />
            <el-option label="近3个月到期" value="近3个月到期" />
            <el-option label="已过期" value="已过期" />
            <el-option label="未过期" value="未过期" />
          </el-select>
        </el-form-item>

        <el-form-item label="SPS到期日">
          <el-select
            v-model="searchForm.sps_expiry_date"
            placeholder="请选择过滤条件"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="全部" />
            <el-option label="近一个月到期" value="近一个月到期" />
            <el-option label="近3个月到期" value="近3个月到期" />
            <el-option label="已过期" value="已过期" />
            <el-option label="未过期" value="未过期" />
          </el-select>
        </el-form-item>

        <el-form-item label="服务到期日">
          <el-select
            v-model="searchForm.after_sales_expiry_date"
            placeholder="请选择过滤条件"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="全部" />
            <el-option label="近一个月到期" value="近一个月到期" />
            <el-option label="近3个月到期" value="近3个月到期" />
            <el-option label="已过期" value="已过期" />
            <el-option label="未过期" value="未过期" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 资产列表表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
        empty-text="暂无资产数据"
        @sort-change="handleSortChange"
      >
        <!-- 资产ID -->
        <el-table-column prop="asset_id" label="资产ID" width="140" fixed sortable>
          <template #default="{ row }">
            <el-button link type="primary" @click="goToDetail(row.id)" class="asset-id-link">
              {{ row.asset_id }}
            </el-button>
          </template>
        </el-table-column>

        <!-- 关联企业 -->
        <el-table-column prop="enterprise.name" label="关联企业" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="enterprise-info">
              <span class="enterprise-name">{{ row.enterprise?.name || 'N/A' }}</span>
              <span class="enterprise-id" v-if="row.enterprise?.enterprise_id">
                ({{ row.enterprise.enterprise_id }})
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- 关联用户 -->
        <el-table-column prop="user.name" label="关联用户" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-info" v-if="row.user">
              <span class="user-name">{{ row.user.name }}</span>
              <span class="user-id">({{ row.user.user_id }})</span>
            </div>
            <span v-else class="no-data">未关联</span>
          </template>
        </el-table-column>

        <!-- 产品信息 -->
        <el-table-column label="产品信息" width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="product-info" v-if="row.product">
              <div class="product-name">{{ row.product.product_name }}</div>
              <div class="product-version">版本: {{ row.product.version_name || 'undefined' }}</div>
            </div>
            <span v-else class="no-data">N/A</span>
          </template>
        </el-table-column>

        <!-- 使用信息 -->
        <el-table-column label="使用信息" width="120" align="center">
          <template #default="{ row }">
            <div class="usage-info">
              <div class="usage-item">{{ row.user_count }}人</div>
              <div class="usage-item">{{ row.account_count }}套</div>
            </div>
          </template>
        </el-table-column>

        <!-- 产品到期日 -->
        <el-table-column label="产品到期日" width="120" align="center">
          <template #default="{ row }">
            <div class="expiry-info">
              <div class="expiry-date" v-if="row.product_expiry_date">
                {{ formatDate(row.product_expiry_date) }}
              </div>
              <div class="expiry-status" :class="getExpiryStatusClass(row.product_expiry_date)">
                {{ getExpiryStatus(row.product_expiry_date) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- SPS到期日 -->
        <el-table-column label="SPS到期日" width="120" align="center">
          <template #default="{ row }">
            <div class="expiry-info">
              <div class="expiry-date" v-if="row.sps_expiry_date">
                {{ formatDate(row.sps_expiry_date) }}
              </div>
              <div class="expiry-status" :class="getExpiryStatusClass(row.sps_expiry_date)">
                {{ getExpiryStatus(row.sps_expiry_date) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 服务到期日 -->
        <el-table-column label="服务到期日" width="120" align="center">
          <template #default="{ row }">
            <div class="expiry-info">
              <div class="expiry-date" v-if="row.after_sales_expiry_date">
                {{ formatDate(row.after_sales_expiry_date) }}
              </div>
              <div class="expiry-status" :class="getExpiryStatusClass(row.after_sales_expiry_date)">
                {{ getExpiryStatus(row.after_sales_expiry_date) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 资产状态 -->
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="default">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column prop="createdAt" label="创建时间" width="160" sortable>
          <template #default="{ row }">
            <span>{{ formatDateTime(row.createdAt) }}</span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click="goToDetail(row.id)">详情</el-button>
              <el-button size="small" type="primary" @click="handleEdit(row.id)">修改</el-button>
              <el-button size="small" type="warning" @click="goToChange(row.id)">变更</el-button>
              <el-dropdown @command="(command) => handleMoreAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="renewal">续费</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, ArrowDown } from '@element-plus/icons-vue'
import { useAssetData } from '../composables/useAssetData.js'
import { formatDateTime } from '@/utils/format.js'
import { getEnterprises } from '@/api/enterprise.js'

const router = useRouter()

// 使用 composable
const { loading, fetchAssetList, deleteExistingAsset } = useAssetData()

// 状态数据
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const enterpriseOptions = ref([])

// 搜索表单
const searchForm = ref({
  asset_id: '',
  enterprise_id: null,
  status: '',
  product_name: '',
  product_expiry_date: '',
  sps_expiry_date: '',
  after_sales_expiry_date: ''
})

// 排序参数
const sortParams = ref({
  prop: 'createdAt',
  order: 'descending'
})

// 加载企业选项
const loadEnterpriseOptions = async () => {
  try {
    const enterprises = await getEnterprises()
    enterpriseOptions.value = enterprises || []
  } catch (error) {
    console.error('加载企业选项失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm.value,
      sortBy: sortParams.value.prop,
      sortOrder: sortParams.value.order === 'ascending' ? 'ASC' : 'DESC'
    }

    // 过滤空值参数和"全部"选项
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined || params[key] === '全部') {
        delete params[key]
      }
    })

    const response = await fetchAssetList(params)
    tableData.value = response.records || response || []
    total.value = response.total || response?.length || 0
  } catch (error) {
    console.error('获取资产列表失败:', error)
    ElMessage.error('获取资产列表失败')
  }
}

// 刷新数据
const refreshData = () => {
  currentPage.value = 1
  loadData()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    asset_id: '',
    enterprise_id: null,
    status: '',
    product_name: '',
    product_expiry_date: '',
    sps_expiry_date: '',
    after_sales_expiry_date: ''
  }
  currentPage.value = 1
  loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortParams.value = { prop, order }
  loadData()
}

// 页面操作
const handleCreate = () => {
  router.push({ name: 'asset-form', query: { mode: 'add' } })
}

const goToDetail = (id) => {
  router.push({ name: 'asset-detail', params: { id } })
}

const handleEdit = (id) => {
  router.push({ name: 'asset-form', params: { id }, query: { mode: 'edit' } })
}

const goToChange = (id) => {
  router.push({ name: 'asset-change-create', params: { id } })
}

const goToRenewal = (id) => {
  // 跳转到新增变更订单页面
  router.push({
    name: 'order-create',
    query: {
      type: 'renewal',
      assetId: id
    }
  })
}

// 更多操作处理
const handleMoreAction = async (command, row) => {
  switch (command) {
    case 'renewal':
      goToRenewal(row.id)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 删除资产
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除资产 "${row.asset_id}" 吗？此操作不可撤销！`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await deleteExistingAsset(row.id)
    await loadData() // 重新加载数据

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除资产失败:', error)
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

// 获取状态类型
const getStatusType = (status) => {
  return status === '在线' ? 'success' : 'danger'
}

// 获取到期状态
const getExpiryStatus = (expiryDate) => {
  if (!expiryDate) return '未设置'

  const now = new Date()
  const expiry = new Date(expiryDate)
  const diffDays = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays <= 30) return '即将过期'
  return '正常'
}

// 获取到期状态样式类
const getExpiryStatusClass = (expiryDate) => {
  const status = getExpiryStatus(expiryDate)
  return {
    'expired': status === '已过期',
    'expiring': status === '即将过期',
    'normal': status === '正常',
    'not-set': status === '未设置'
  }
}

// 生命周期
onMounted(async () => {
  await loadEnterpriseOptions()
  await loadData()
})
</script>

<style scoped>
.asset-list-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 20px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-bar .el-form {
  margin-bottom: 0;
}

.search-bar .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.el-table {
  border-radius: 8px;
}

/* 资产ID链接 */
.asset-id-link {
  font-weight: 600;
  font-size: 14px;
}

/* 企业信息 */
.enterprise-info {
  display: flex;
  flex-direction: column;
}

.enterprise-name {
  font-weight: 500;
  color: #303133;
}

.enterprise-id {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 用户信息 */
.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-id {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

/* 产品信息 */
.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.product-version {
  font-size: 12px;
  color: #909399;
}

/* 使用信息 */
.usage-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.usage-item {
  padding: 2px 8px;
  background: #f0f9ff;
  border-radius: 12px;
  font-size: 12px;
  color: #1976d2;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 到期信息 */
.expiry-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.expiry-date {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.expiry-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.expiry-status.normal {
  background: #f0f9ff;
  color: #1976d2;
}

.expiry-status.expiring {
  background: #fef3e2;
  color: #f56c6c;
}

.expiry-status.expired {
  background: #fef0f0;
  color: #f56c6c;
}

.expiry-status.not-set {
  background: #f5f7fa;
  color: #909399;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .search-bar .el-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .search-bar .el-form-item {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .asset-list-page {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .search-bar {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}

/* 表格行悬停效果 */
.el-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

/* 状态标签样式 */
.el-tag {
  font-weight: 500;
}

/* 下拉菜单图标 */
.el-icon--right {
  margin-left: 4px;
}
</style>
