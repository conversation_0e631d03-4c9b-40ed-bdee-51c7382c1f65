const db = require('../models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');

const Employee = db.Employee;
const User = db.User;
const UserPassword = db.UserPassword;
const JWT_SECRET = process.env.JWT_SECRET || 'your_default_jwt_secret_key'; // 从环境变量获取JWT密钥

/**
 * 员工登录
 */
exports.employeeLogin = async (req, res) => {
  try {
    // 2. 将接收字段从 employee_number 改为更通用的 loginId
    const { loginId, password } = req.body;

    // 3. 更新校验逻辑
    if (!loginId || !password) {
      return res.status(400).json({ message: '工号/用户名和密码不能为空' });
    }

    // 4. 根据工号 或 用户名 查找员工
    const employee = await Employee.findOne({
      where: {
        [Op.or]: [
          { employee_number: loginId },
          { name: loginId }
        ]
      }
    });
    if (!employee) {
      return res.status(401).json({ message: '认证失败：员工不存在' });
    }

    // 3. 校验密码
    const isPasswordValid = await bcrypt.compare(password, employee.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: '认证失败：密码错误' });
    }

    // 4. 密码正确，生成JWT
    const payload = {
      id: employee.id,
      employee_number: employee.employee_number,
      name: employee.name,
      role: employee.role,
      type: 'employee' // 标记token类型为员工
    };

    const token = jwt.sign(
      payload,
      JWT_SECRET,
      { expiresIn: '24h' } // Token有效期，例如24小时
    );

    // 5. 返回Token和员工基本信息给前端
    const employeeJson = employee.toJSON();
    delete employeeJson.password; // 再次确保不返回密码

    res.status(200).json({
      message: '登录成功',
      token,
      user: employeeJson
    });

  } catch (error) {
    console.error('员工登录时出错:', error);
    res.status(500).json({ message: '登录时发生服务器内部错误', error: error.message });
  }
};

/**
 * [!] 新增：员工修改自己的密码
 */
exports.changePassword = async (req, res) => {
  try {
    // 1. 从JWT令牌中获取当前登录用户的ID（后续中间件会提供）
    const employeeId = req.user.id; 
    const { oldPassword, newPassword } = req.body;

    // 2. 校验输入
    if (!oldPassword || !newPassword) {
      return res.status(400).json({ message: '旧密码和新密码不能为空' });
    }

    // 3. 查找员工
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      // 正常情况下，能通过JWT中间件的，用户一定存在
      return res.status(404).json({ message: '用户不存在' });
    }

    // 4. 验证旧密码是否正确
    const isPasswordValid = await bcrypt.compare(oldPassword, employee.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: '旧密码错误' });
    }

    // 5. 将新密码加密并更新
    const hashedNewPassword = await bcrypt.hash(newPassword, 8);
    await employee.update({ password: hashedNewPassword });

    // 6. 返回成功信息
    res.status(200).json({ message: '密码修改成功' });

  } catch (error) {
    console.error('修改密码时出错:', error);
    res.status(500).json({ message: '修改密码时发生服务器内部错误', error: error.message });
  }
};

/**
 * [!] 新增：验证当前登录用户的密码
 * 用于高权限操作前的二次确认
 * 管理员修改他人密码时需二次确认
 */
exports.verifyPassword = async (req, res) => {
  try {
    const employeeId = req.user.id;
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ message: '密码不能为空' });
    }

    const employee = await Employee.findByPk(employeeId);
    // 此处无需检查employee是否存在，因为verifyToken中间件已确保用户有效

    const isPasswordValid = await bcrypt.compare(password, employee.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: '管理员密码验证失败' });
    }

    res.status(200).json({ message: '密码验证成功' });

  } catch (error) {
    console.error('验证密码时出错:', error);
    res.status(500).json({ message: '验证密码时发生服务器内部错误' });
  }
};



/**
 * [新增] 获取当前用户信息
 */
exports.getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.type;

    let user;
    if (userType === 'employee') {
      // 员工用户
      user = await Employee.findByPk(userId, {
        attributes: { exclude: ['password'] }
      });
    } else {
      // 普通用户
      user = await User.findByPk(userId, {
        attributes: { exclude: ['password'] }
      });
    }

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 如果是普通用户，添加合伙人相关字段
    if (userType === 'user') {
      const userWithPartnerInfo = {
        ...user.toJSON(),
        is_partner: user.is_partner || false,
        partner_id: user.partner_id || null,
        commission_base: user.commission_base || null,
        commission_extra: user.commission_extra || null
      };
      res.status(200).json(userWithPartnerInfo);
    } else {
      res.status(200).json(user);
    }

  } catch (error) {
    console.error('获取当前用户信息时出错:', error);
    res.status(500).json({ message: '获取用户信息时发生服务器内部错误', error: error.message });
  }
};

/**
 * 微信1用户登录
 */
/**
 * 方式1：账号密码登录
 */
exports.passwordLogin = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 输入验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '请输入完整的登录信息',
        details: '手机号/姓名/昵称和密码都不能为空'
      });
    }

    // 去除首尾空格
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();

    if (!trimmedUsername || !trimmedPassword) {
      return res.status(400).json({
        success: false,
        message: '请输入有效的登录信息',
        details: '用户名和密码不能只包含空格'
      });
    }

    // 查找用户（支持手机号、姓名、昵称三种方式登录）
    const user = await User.findOne({
      where: {
        [Op.or]: [
          { mobile: trimmedUsername },
          { name: trimmedUsername },
          { nickname: trimmedUsername }
        ]
      },
      include: [{
        model: UserPassword,
        as: 'passwordDetail'
      }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '账号不存在',
        details: '请检查您输入的手机号、姓名或昵称是否正确'
      });
    }

    if (!user.passwordDetail) {
      return res.status(401).json({
        success: false,
        message: '该账号未设置密码',
        details: '请使用微信手机号登录或联系管理员设置密码'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(trimmedPassword, user.passwordDetail.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '密码错误',
        details: '请检查您输入的密码是否正确'
      });
    }

    // 生成JWT token
    const payload = {
      id: user.id,
      name: user.name,
      mobile: user.mobile,
      type: 'user',
      loginType: 'password'
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
    const refresh_token = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

    // 更新最后登录时间
    await user.update({
      last_login_at: new Date()
    });

    res.json({
      success: true,
      message: '登录成功',
      token,
      refresh_token,
      user: {
        id: user.id,
        user_id: user.user_id,
        name: user.name,
        nickname: user.nickname,
        mobile: user.mobile,
        email: user.email,
        is_partner: user.is_partner,
        partner_id: user.partner_id,
        commission_base: user.commission_base,
        commission_extra: user.commission_extra,
        loginType: 'password'
      }
    });

  } catch (error) {
    console.error('账号密码登录失败:', error);

    // 根据错误类型返回不同的提示
    if (error.name === 'SequelizeConnectionError') {
      return res.status(500).json({
        success: false,
        message: '服务器连接异常',
        details: '请稍后重试或联系技术支持'
      });
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '数据验证失败',
        details: '请检查输入的信息格式是否正确'
      });
    }

    res.status(500).json({
      success: false,
      message: '登录服务暂时不可用',
      details: '系统正在维护中，请稍后重试'
    });
  }
};



/**
 * 方式2：微信手机号快速验证
 */
exports.phoneLogin = async (req, res) => {
  try {
    const { code, loginType } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少验证码'
      });
    }

    console.log('微信手机号快速验证，code:', code, 'loginType:', loginType);

    // 判断是否来自合伙人小程序
    const isPartnerApp = loginType === 'phone_verification';

    // 调用微信接口获取手机号
    const phoneResponse = await getPhoneNumberFromWechat(code, isPartnerApp);

    if (!phoneResponse.success) {
      return res.status(400).json({
        success: false,
        message: phoneResponse.message || '获取手机号失败'
      });
    }

    const phoneNumber = phoneResponse.phoneNumber;
    console.log('获取到手机号:', phoneNumber);

    // 查找是否已存在该手机号的用户
    let user = await User.findOne({
      where: { mobile: phoneNumber }
    });

    if (user) {
      // 用户已存在，直接登录
      const payload = {
        id: user.id,
        name: user.name,
        mobile: user.mobile,
        type: 'user',
        loginType: 'phone_verification'
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
      const refresh_token = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

      // 更新最后登录时间
      await user.update({
        last_login_at: new Date()
      });

      res.json({
        success: true,
        message: '登录成功',
        token,
        refresh_token,
        user: {
          id: user.id,
          user_id: user.user_id,
          name: user.name,
          nickname: user.nickname,
          mobile: user.mobile,
          email: user.email,
          is_partner: user.is_partner,
          partner_id: user.partner_id,
          commission_base: user.commission_base,
          commission_extra: user.commission_extra,
          loginType: 'phone_verification'
        }
      });
    } else {
      // 新用户，自动创建账户
      const { generateUserId } = require('../utils/id_helper');
      const newUserId = await generateUserId();

      const newUser = await User.create({
        user_id: newUserId,
        mobile: phoneNumber,
        name: `用户${phoneNumber.slice(-4)}`, // 使用手机号后4位作为默认昵称
        login_type: 'wechat'
      });

      // 为新用户设置初始密码 202020
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('202020', 10);
      await UserPassword.create({
        user_id: newUser.id,
        password: hashedPassword
      });

      const payload = {
        id: newUser.id,
        name: newUser.name,
        mobile: newUser.mobile,
        type: 'user',
        loginType: 'phone_verification'
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
      const refresh_token = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

      res.json({
        success: true,
        message: '注册并登录成功',
        token,
        refresh_token,
        user: {
          id: newUser.id,
          user_id: newUser.user_id,
          name: newUser.name,
          nickname: newUser.nickname,
          mobile: newUser.mobile,
          email: newUser.email,
          is_partner: newUser.is_partner,
          partner_id: newUser.partner_id,
          commission_base: newUser.commission_base,
          commission_extra: newUser.commission_extra,
          loginType: 'phone_verification'
        }
      });
    }

  } catch (error) {
    console.error('微信手机号快速验证失败:', error);
    res.status(500).json({
      success: false,
      message: '验证失败',
      error: error.message
    });
  }
};



/**
 * 从微信获取手机号的辅助函数
 */
async function getPhoneNumberFromWechat(code, isPartnerApp = false) {
  try {
    const axios = require('axios');
    const accessToken = await getWechatAccessToken(isPartnerApp);

    if (!accessToken) {
      return { success: false, message: '获取微信访问令牌失败' };
    }

    const url = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`;
    const response = await axios.post(url, { code });

    console.log('微信手机号接口响应:', response.data);

    if (response.data.errcode === 0) {
      const phoneInfo = response.data.phone_info;
      return {
        success: true,
        phoneNumber: phoneInfo.phoneNumber,
        purePhoneNumber: phoneInfo.purePhoneNumber,
        countryCode: phoneInfo.countryCode
      };
    } else {
      return {
        success: false,
        message: `微信接口错误: ${response.data.errcode} - ${response.data.errmsg}`
      };
    }
  } catch (error) {
    console.error('获取微信手机号失败:', error);
    return { success: false, message: '获取手机号失败' };
  }
}

/**
 * 获取微信访问令牌
 */
async function getWechatAccessToken(isPartnerApp = false) {
  try {
    const axios = require('axios');
    const appId = isPartnerApp ? process.env.WECHAT_PARTNER_APPID : process.env.WECHAT_APPID;
    const appSecret = isPartnerApp ? process.env.WECHAT_PARTNER_SECRET : process.env.WECHAT_SECRET;

    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
    const response = await axios.get(url);

    if (response.data.access_token) {
      return response.data.access_token;
    } else {
      console.error('获取access_token失败:', response.data);
      return null;
    }
  } catch (error) {
    console.error('获取微信访问令牌失败:', error);
    return null;
  }
}

/**
 * 获取合伙人收益统计
 */
exports.getPartnerEarnings = async (req, res) => {
  try {
    const userId = req.user.id;

    // 验证用户是否为合伙人
    const user = await User.findByPk(userId);
    if (!user || !user.is_partner) {
      return res.status(403).json({
        success: false,
        message: '您不是合伙人，无法查看收益信息'
      });
    }

    // 查询合伙人相关订单的收益统计
    const { OrderHead } = require('../models');

    // 查询所有合伙人订单 - 根据实际数据库存储使用user.id（自增主键）
    console.log('查询合伙人订单，用户信息:', { id: user.id, user_id: user.user_id, name: user.name });

    const partnerOrders = await OrderHead.findAll({
      where: {
        partner_user_id: user.id,  // 现在是INT类型，直接使用数字
        is_partner_order: 1
      },
      attributes: [
        'commission_amount',
        'commission_status',
        'payment_status'
      ]
    });

    console.log('查询到的合伙人订单数量:', partnerOrders.length);
    console.log('订单详情:', partnerOrders.map(order => ({
      commission_amount: order.commission_amount,
      commission_status: order.commission_status,
      payment_status: order.payment_status
    })));

    // 计算收益统计 - 重新规划为3个清晰的字段
    let pendingEarnings = 0;  // 待发放收益
    let paidEarnings = 0;     // 已发放收益
    let totalEarnings = 0;    // 累计总收益

    partnerOrders.forEach(order => {
      const amount = parseFloat(order.commission_amount || 0);

      if (order.commission_status === '已发放') {
        // 已发放收益
        paidEarnings += amount;
      } else if (order.commission_status === '未发放' && order.payment_status === '已支付') {
        // 待发放收益
        pendingEarnings += amount;
      }
    });

    // 累计总收益 = 已发放 + 待发放
    totalEarnings = paidEarnings + pendingEarnings;

    res.json({
      success: true,
      data: {
        pending_earnings: pendingEarnings.toFixed(2),  // 待发放收益
        paid_earnings: paidEarnings.toFixed(2),        // 已发放收益
        total_earnings: totalEarnings.toFixed(2),      // 累计总收益
        commission_base: user.commission_base || 0.06,
        commission_extra: user.commission_extra || 0.00,
        partner_id: user.partner_id,
        order_count: partnerOrders.length
      }
    });

  } catch (error) {
    console.error('获取合伙人收益失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收益信息失败'
    });
  }
};

/**
 * 验证手机号格式
 */
function isValidMobile(mobile) {
  const mobileRegex = /^1[3-9]\d{9}$/;
  return mobileRegex.test(mobile);
}