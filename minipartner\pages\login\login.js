/**
 * 合伙人小程序登录页面
 * 只允许is_partner=1的用户登录
 * 与后端认证API对接，确保安全性
 */
const api = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    phone: '',              // 手机号或用户名
    password: '',           // 密码
    submitting: false,      // 是否正在提交登录请求
    errorMessage: ''        // 错误信息显示
  },

  /**
   * 处理手机号输入
   * @param {Object} e - 输入事件对象
   */
  handlePhoneInput(e) {
    this.setData({
      phone: e.detail.value,
      errorMessage: ''
    });
  },

  /**
   * 处理密码输入
   * @param {Object} e - 输入事件对象
   */
  handlePasswordInput(e) {
    this.setData({
      password: e.detail.value,
      errorMessage: ''
    });
  },

  /**
   * 处理合伙人登录
   * 只有is_partner=1的用户才能登录合伙人小程序
   */
  async handleLogin() {
    try {
      // 验证表单输入
      if (!this.validateForm()) {
        return;
      }

      this.setData({ submitting: true, errorMessage: '' });

      console.log('合伙人登录请求:', {
        username: this.data.phone,
        password: this.data.password
      });

      // 调用合伙人登录接口
      const result = await api.partnerLogin({
        username: this.data.phone, // 使用手机号作为用户名
        password: this.data.password
      });

      console.log('合伙人登录响应:', result);

      // 检查登录是否成功
      if (result.success) {
        const { token, user } = result;

        console.log('登录成功，用户信息:', user);
        console.log('Token:', token);

        // 验证用户是否为合伙人
        // 检查多种可能的合伙人标识字段
        const isPartner = user.is_partner === 1 || user.is_partner === true || user.is_partner === '1';

        console.log('用户合伙人状态检查:', {
          is_partner: user.is_partner,
          isPartner: isPartner,
          userType: typeof user.is_partner
        });

        if (!isPartner) {
          this.setData({
            errorMessage: '您不是合伙人，无法登录此小程序',
            submitting: false
          });
          return;
        }

        // 保存登录信息到本地存储
        api.saveLoginInfo({
          access_token: token,
          user: user
        });

        // 更新全局应用状态
        const app = getApp();
        app.globalData.isLoggedIn = true;
        app.globalData.userInfo = user;
        app.globalData.token = token;
        app.globalData.isPartner = true;

        // 显示登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 跳转到个人中心页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/profile/profile'
          });
        }, 1500);

      } else {
        // 登录失败，显示错误信息
        this.setData({
          errorMessage: result.message || '登录失败，请检查用户名和密码',
          submitting: false
        });
      }
    } catch (error) {
      console.error('合伙人登录失败:', error);

      this.setData({
        errorMessage: error.message || '登录失败，请检查网络连接',
        submitting: false
      });
    }
  },
  
  /**
   * 微信授权手机号登录回调
   */
  onGetPhoneNumber(e) {
    console.log('微信手机号快速验证回调:', e);

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 用户同意授权
      const { code } = e.detail;

      wx.showLoading({
        title: '验证中...'
      });

      // 调用后端接口进行手机号验证登录
      this.wechatPhoneLogin(code);
    } else if (e.detail.errno === 1400001) {
      // 额度不足
      wx.showModal({
        title: '提示',
        content: '手机号快速验证次数已达上限，请选择其他登录方式',
        showCancel: false
      });
    } else {
      // 用户拒绝授权或其他错误
      console.log('用户拒绝手机号授权或发生错误');
      wx.showToast({
        title: '验证取消',
        icon: 'none'
      });
    }
  },

  /**
   * 微信手机号验证登录
   */
  async wechatPhoneLogin(code) {
    try {
      const result = await api.partnerWechatPhoneLogin({ code });

      wx.hideLoading();

      console.log('微信手机号验证登录响应:', result);

      if (result.success) {
        const { token, user } = result;

        console.log('微信验证登录成功，用户信息:', user);

        // 验证用户是否为合伙人
        const isPartner = user.is_partner === 1 || user.is_partner === true || user.is_partner === '1';

        console.log('微信登录用户合伙人状态检查:', {
          is_partner: user.is_partner,
          isPartner: isPartner,
          userType: typeof user.is_partner
        });

        if (!isPartner) {
          this.setData({
            errorMessage: '您不是合伙人，无法登录此小程序'
          });
          return;
        }

        // 保存登录信息到本地存储
        api.saveLoginInfo({
          access_token: token,
          user: user
        });

        // 更新全局应用状态
        const app = getApp();
        app.globalData.isLoggedIn = true;
        app.globalData.userInfo = user;
        app.globalData.token = token;
        app.globalData.isPartner = true;

        // 显示登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 跳转到个人中心页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/profile/profile'
          });
        }, 1500);

      } else {
        // 登录失败，显示错误信息
        this.setData({
          errorMessage: result.message || '微信验证登录失败'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('微信手机号验证登录失败:', error);

      // 根据错误类型显示不同提示
      let errorMessage = '微信授权失败，请重试';

      if (error.message && error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message && error.message.includes('用户拒绝')) {
        errorMessage = '需要授权手机号才能登录';
      } else if (error.statusCode === 500) {
        errorMessage = '服务器异常，请稍后重试';
      }

      this.setData({
        errorMessage: errorMessage
      });
    }
  },

  /**
   * 处理注册按钮点击
   * 跳转到注册页面
   */
  handleRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  /**
   * 处理忘记密码按钮点击
   */
  handleForgetPassword() {
    wx.showModal({
      title: '找回密码',
      content: '请联系管理员重置密码',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 验证表单输入
   * @returns {boolean} 验证是否通过
   */
  validateForm() {
    const { phone, password } = this.data;

    if (!phone || !phone.trim()) {
      this.setData({
        errorMessage: '请输入手机号或用户名'
      });
      return false;
    }

    if (!password || !password.trim()) {
      this.setData({
        errorMessage: '请输入密码'
      });
      return false;
    }

    // 简单的手机号格式验证（如果输入的是手机号）
    if (/^1[3-9]\d{9}$/.test(phone.trim())) {
      // 是手机号格式，进行格式验证
      if (!/^1[3-9]\d{9}$/.test(phone.trim())) {
        this.setData({
          errorMessage: '请输入正确的手机号格式'
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 页面加载时的处理
   */
  onLoad: function() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '合伙人登录'
    });

    // 检查是否已经登录
    if (api.isLoggedIn()) {
      const userInfo = api.getLocalUserInfo();
      if (userInfo && userInfo.is_partner) {
        // 已登录且是合伙人，直接跳转到个人中心
        wx.switchTab({
          url: '/pages/profile/profile'
        });
        return;
      } else {
        // 已登录但不是合伙人，清除登录信息
        api.clearLoginInfo();
      }
    }
  }
});