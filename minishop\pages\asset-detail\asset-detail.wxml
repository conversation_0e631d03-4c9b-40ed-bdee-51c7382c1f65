<!--pages/asset-detail/asset-detail.wxml-->
<wxs module="utils">
  // 计算剩余天数
  function calculateDaysLeft(expiryDateStr) {
    if (!expiryDateStr) return 0;
    var today = getDate();
    var expiryDate = getDate(expiryDateStr);
    var timeDiff = expiryDate.getTime() - today.getTime();
    return Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
  }
  
  // 获取到期状态
  function getExpiryStatus(expiryDateStr) {
    if (!expiryDateStr) {
      return { class: 'normal', text: '无限期' };
    }
    
    var daysLeft = calculateDaysLeft(expiryDateStr);
    
    if (daysLeft <= 0) {
      return { class: 'expired', text: '已过期' };
    } else if (daysLeft <= 30) {
      return { class: 'warning', text: daysLeft + '天后到期' };
    } else {
      return { class: 'normal', text: daysLeft + '天后到期' };
    }
  }
  
  // 格式化价格
  function formatPrice(price) {
    if (!price) return '0.00';
    return parseFloat(price).toFixed(2);
  }

  // 计算总价格
  function calculateTotalPrice(standard, sps, afterSales, implementation) {
    var total = (standard || 0) + (sps || 0) + (afterSales || 0) + (implementation || 0);
    return formatPrice(total);
  }
  
  module.exports = {
    getExpiryStatus: getExpiryStatus,
    calculateDaysLeft: calculateDaysLeft,
    formatPrice: formatPrice,
    calculateTotalPrice: calculateTotalPrice
  };
</wxs>

<view class="page-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="navbar-title">资产详情</view>
      <view class="navbar-right">
        <text class="nav-icon" bindtap="showMoreActions">⋯</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 资产详情内容 -->
  <view wx:else class="content-container">
    <!-- 基本信息卡片 -->
    <view class="info-card main-card">
      <view class="card-header">
        <view class="product-info-main">
          <view class="product-name">{{asset.product ? asset.product.product_name : '未知产品'}}</view>
          <view class="asset-id">资产编号：{{asset.asset_id}}</view>
        </view>
        <view class="asset-status {{asset.status === '过期' ? 'expired' : 'active'}}">
          <text class="status-dot"></text>
          {{asset.status}}
        </view>
      </view>

      <view class="product-details" wx:if="{{asset.product}}">
        <view class="detail-item" wx:if="{{asset.product.version_name}}">
          <view class="detail-icon">
            <text class="detail-icon-text">V</text>
          </view>
          <view class="detail-content">
            <view class="detail-label">产品版本</view>
            <view class="detail-value">{{asset.product.version_name}}</view>
          </view>
        </view>
        <view class="detail-item" wx:if="{{asset.product.product_type}}">
          <view class="detail-icon">
            <text class="detail-icon-text">T</text>
          </view>
          <view class="detail-content">
            <view class="detail-label">产品类型</view>
            <view class="detail-value">{{asset.product.product_type}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用信息卡片 -->
    <view class="info-card">
      <view class="card-title">
        <text class="title-icon">👥</text>
        <text>使用信息</text>
      </view>

      <view class="usage-grid">
        <view class="usage-item">
          <view class="usage-number">{{asset.user_count}}</view>
          <view class="usage-label">用户数</view>
        </view>
        <view class="usage-item">
          <view class="usage-number">{{asset.account_count}}</view>
          <view class="usage-label">账套数</view>
        </view>
        <view class="usage-item" wx:if="{{asset.duration_months}}">
          <view class="usage-number">{{asset.duration_months}}</view>
          <view class="usage-label">购买时长(月)</view>
        </view>
      </view>

      <!-- 购买日期 -->
      <view class="purchase-info" wx:if="{{asset.purchase_date}}">
        <view class="info-row">
          <view class="info-icon">📅</view>
          <view class="info-content">
            <view class="info-label">购买日期</view>
            <view class="info-value">{{asset.purchase_date}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 产品功能卡片 -->
    <view class="info-card" wx:if="{{asset.product}}">
      <view class="card-title">
        <text class="title-icon">⚡</text>
        <text>产品功能</text>
      </view>

      <!-- 有功能数据时显示功能列表 -->
      <view class="features-list" wx:if="{{asset.product.features && asset.product.features.length > 0}}">
        <view wx:for="{{asset.product.features}}" wx:key="id" class="feature-item">
          <view class="feature-icon">✓</view>
          <view class="feature-content">
            <view class="feature-name">{{item.feature_name}}</view>
            <view class="feature-desc" wx:if="{{item.description}}">{{item.description}}</view>
            <view class="feature-price" wx:if="{{item.ProductFeatureRelation && item.ProductFeatureRelation.feature_price}}">
              ¥{{utils.formatPrice(item.ProductFeatureRelation.feature_price)}}
            </view>
          </view>
        </view>
      </view>

      <!-- 没有功能数据时显示提示信息 -->
      <view class="no-features" wx:else>
        <view class="no-features-icon">📦</view>
        <view class="no-features-text">暂无产品功能信息</view>
        <view class="no-features-desc">请联系客服获取详细功能说明</view>
      </view>
    </view>

    <!-- 到期信息卡片 -->
    <view class="info-card">
      <view class="card-title">
        <text class="title-icon">📅</text>
        <text>到期信息</text>
      </view>

      <view class="expiry-list">
        <view class="expiry-item {{utils.getExpiryStatus(asset.product_expiry_date).class}}" wx:if="{{asset.product_expiry_date}}">
          <view class="expiry-label">
            <text class="expiry-icon">📦</text>
            产品到期日
          </view>
          <view class="expiry-value">{{asset.product_expiry_date}}</view>
          <view class="expiry-status {{utils.getExpiryStatus(asset.product_expiry_date).class}}">
            {{utils.getExpiryStatus(asset.product_expiry_date).text}}
          </view>
        </view>

        <view class="expiry-item {{utils.getExpiryStatus(asset.sps_expiry_date).class}}" wx:if="{{asset.sps_expiry_date}}">
          <view class="expiry-label">
            <text class="expiry-icon">🛠️</text>
            SPS到期日
          </view>
          <view class="expiry-value">{{asset.sps_expiry_date}}</view>
          <view class="expiry-status {{utils.getExpiryStatus(asset.sps_expiry_date).class}}">
            {{utils.getExpiryStatus(asset.sps_expiry_date).text}}
          </view>
        </view>

        <view class="expiry-item {{utils.getExpiryStatus(asset.after_sales_expiry_date).class}}" wx:if="{{asset.after_sales_expiry_date}}">
          <view class="expiry-label">
            <text class="expiry-icon">🎧</text>
            服务到期日
          </view>
          <view class="expiry-value">{{asset.after_sales_expiry_date}}</view>
          <view class="expiry-status {{utils.getExpiryStatus(asset.after_sales_expiry_date).class}}">
            {{utils.getExpiryStatus(asset.after_sales_expiry_date).text}}
          </view>
        </view>
      </view>
    </view>

    <!-- 价格信息卡片 -->
    <view class="info-card" wx:if="{{asset.standard_price || asset.sps_annual_fee || asset.after_sales_service_fee || asset.implementation_fee}}">
      <view class="card-title">
        <text class="title-icon">💰</text>
        <text>价格信息</text>
      </view>

      <view class="price-list">
        <view class="price-item" wx:if="{{asset.product_standard_price}}">
          <view class="price-label">
            <text class="price-icon">📦</text>
            产品标准价
          </view>
          <view class="price-value">¥{{utils.formatPrice(asset.product_standard_price)}}</view>
        </view>

        <view class="price-item" wx:if="{{asset.sps_annual_fee}}">
          <view class="price-label">
            <text class="price-icon">🛠️</text>
            SPS年费
          </view>
          <view class="price-value">¥{{utils.formatPrice(asset.sps_annual_fee)}}</view>
        </view>

        <view class="price-item" wx:if="{{asset.after_sales_service_fee}}">
          <view class="price-label">
            <text class="price-icon">🎧</text>
            售后服务费用
          </view>
          <view class="price-value">¥{{utils.formatPrice(asset.after_sales_service_fee)}}</view>
        </view>

        <view class="price-item" wx:if="{{asset.implementation_fee}}">
          <view class="price-label">
            <text class="price-icon">⚙️</text>
            实施费用
          </view>
          <view class="price-value">¥{{utils.formatPrice(asset.implementation_fee)}}</view>
        </view>
      </view>

      <!-- 价格汇总 -->
      <view class="price-summary" wx:if="{{asset.product_standard_price || asset.sps_annual_fee || asset.after_sales_service_fee || asset.implementation_fee}}">
        <view class="summary-item">
          <view class="summary-label">总计金额</view>
          <view class="summary-value">¥{{utils.calculateTotalPrice(asset.product_standard_price, asset.sps_annual_fee, asset.after_sales_service_fee, asset.implementation_fee)}}</view>
        </view>
      </view>
    </view>

    <!-- 激活信息卡片 -->
    <view class="info-card" wx:if="{{asset.activation_code || asset.activation_phone}}">
      <view class="card-title">
        <text class="title-icon">🔑</text>
        <text>激活信息</text>
      </view>
      
      <view class="activation-list">
        <view class="activation-item" wx:if="{{asset.activation_code}}">
          <view class="activation-label">激活码</view>
          <view class="activation-value">{{asset.activation_code}}</view>
          <view class="copy-btn" bindtap="copyText" data-text="{{asset.activation_code}}">
            <text class="copy-icon">📋</text>
          </view>
        </view>

        <view class="activation-item" wx:if="{{asset.activation_phone}}">
          <view class="activation-label">激活手机号</view>
          <view class="activation-value">{{asset.activation_phone}}</view>
          <view class="copy-btn" bindtap="copyText" data-text="{{asset.activation_phone}}">
            <text class="copy-icon">📋</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="info-card" wx:if="{{asset.remark}}">
      <view class="card-title">
        <text class="title-icon">📝</text>
        <text>备注信息</text>
      </view>
      <view class="remark-content">{{asset.remark}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="contactService">
        <text class="btn-icon">📞</text>
        联系客服
      </button>
      <button class="action-btn secondary" bindtap="viewChangeLogs">
        <text class="btn-icon">📋</text>
        变更记录
      </button>
    </view>
  </view>

  <!-- 浮动咨询组件 -->
  <float-consult />
</view>
