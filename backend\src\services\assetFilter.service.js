// /backend/src/services/assetFilter.service.js
const { Op } = require('sequelize');

/**
 * 资产过滤服务
 * 专门处理资产列表的各种过滤条件
 */
class AssetFilterService {
  /**
   * 构建资产查询的 where 条件
   * @param {Object} filters - 过滤条件
   * @returns {Object} Sequelize where 条件对象
   */
  static buildAssetWhereCondition(filters) {
    const {
      q,
      asset_id,
      enterprise_id,
      status,
      product_expiry_date,
      sps_expiry_date,
      after_sales_expiry_date
    } = filters;

    let where = {};

    // 通用搜索（资产ID或关联表字段）
    if (q) {
      where[Op.or] = [
        { asset_id: { [Op.like]: `%${q}%` } },
        { '$product.product_name$': { [Op.like]: `%${q}%` } },
        { '$product.version_name$': { [Op.like]: `%${q}%` } },
        { '$enterprise.name$': { [Op.like]: `%${q}%` } },
        { '$enterprise.enterprise_id$': { [Op.like]: `%${q}%` } },
        { '$user.name$': { [Op.like]: `%${q}%` } },
        { '$user.user_id$': { [Op.like]: `%${q}%` } }
      ];
    }

    // 具体字段过滤（支持模糊搜索）
    if (asset_id) {
      where.asset_id = { [Op.like]: `%${asset_id}%` };
    }
    if (enterprise_id) {
      where.enterprise_id = enterprise_id;
    }
    if (status) {
      where.status = { [Op.like]: `%${status}%` };
    }

    // 日期过滤（支持年份、月份、日期的灵活搜索和预设过滤选项）
    if (product_expiry_date) {
      const dateFilter = AssetFilterService.buildDateFilter(product_expiry_date);
      if (dateFilter) {
        where.product_expiry_date = dateFilter;
      }
    }
    if (sps_expiry_date) {
      const dateFilter = AssetFilterService.buildDateFilter(sps_expiry_date);
      if (dateFilter) {
        where.sps_expiry_date = dateFilter;
      }
    }
    if (after_sales_expiry_date) {
      const dateFilter = AssetFilterService.buildDateFilter(after_sales_expiry_date);
      if (dateFilter) {
        where.after_sales_expiry_date = dateFilter;
      }
    }

    return where;
  }

  /**
   * 构建日期过滤条件
   * @param {string} dateInput - 用户输入的日期关键词或预设选项
   * @returns {Object} Sequelize where 条件对象
   */
  static buildDateFilter(dateInput) {
    if (!dateInput) return null;

    const input = dateInput.toString().trim();
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD 格式

    // 预设过滤选项
    switch (input) {
      case '全部':
        return null; // 不添加任何过滤条件

      case '近一个月到期':
        const oneMonthLater = new Date(today);
        oneMonthLater.setMonth(today.getMonth() + 1);
        const oneMonthStr = oneMonthLater.toISOString().split('T')[0];
        return {
          [Op.and]: [
            { [Op.gte]: todayStr }, // 大于等于今天
            { [Op.lte]: oneMonthStr } // 小于等于一个月后
          ]
        };

      case '近3个月到期':
        const threeMonthsLater = new Date(today);
        threeMonthsLater.setMonth(today.getMonth() + 3);
        const threeMonthsStr = threeMonthsLater.toISOString().split('T')[0];
        return {
          [Op.and]: [
            { [Op.gte]: todayStr }, // 大于等于今天
            { [Op.lte]: threeMonthsStr } // 小于等于三个月后
          ]
        };

      case '已过期':
        return { [Op.lt]: todayStr }; // 小于今天

      case '未过期':
        return { [Op.gte]: todayStr }; // 大于等于今天
    }

    // 如果输入是4位数字，当作年份处理 (如: 2025)
    if (/^\d{4}$/.test(input)) {
      const yearStart = `${input}-01-01`;
      const yearEnd = `${input}-12-31`;
      return {
        [Op.and]: [
          { [Op.gte]: yearStart },
          { [Op.lte]: yearEnd }
        ]
      };
    }

    // 如果输入是1-2位数字，当作月份或日期处理
    if (/^\d{1,2}$/.test(input)) {
      const paddedInput = input.padStart(2, '0'); // 补零，如 8 -> 08
      const currentYear = today.getFullYear();

      // 尝试作为月份匹配
      const monthStart = `${currentYear}-${paddedInput}-01`;
      const monthEnd = `${currentYear}-${paddedInput}-31`;

      // 尝试作为日期匹配（当前月份）
      const currentMonth = (today.getMonth() + 1).toString().padStart(2, '0');
      const dayMatch = `${currentYear}-${currentMonth}-${paddedInput}`;

      return {
        [Op.or]: [
          // 匹配月份
          {
            [Op.and]: [
              { [Op.gte]: monthStart },
              { [Op.lte]: monthEnd }
            ]
          },
          // 匹配具体日期
          { [Op.eq]: dayMatch }
        ]
      };
    }

    // 其他情况尝试解析为完整日期
    try {
      const date = new Date(input);
      if (!isNaN(date.getTime())) {
        const dateStr = date.toISOString().split('T')[0];
        return { [Op.eq]: dateStr };
      }
    } catch (e) {
      // 忽略解析错误
    }

    // 如果都不匹配，返回null（不过滤）
    return null;
  }

  /**
   * 构建产品查询的 where 条件
   * @param {Object} filters - 过滤条件
   * @returns {Object} Sequelize where 条件对象
   */
  static buildProductWhereCondition(filters) {
    const { product_name } = filters;
    let where = {};

    if (product_name && product_name.trim() !== '') {
      where[Op.or] = [
        { product_name: { [Op.like]: `%${product_name.trim()}%` } },
        { version_name: { [Op.like]: `%${product_name.trim()}%` } }
      ];
    }

    return where;
  }

  /**
   * 构建企业权限过滤条件
   * @param {Object} user - 当前用户信息
   * @returns {Object} 企业权限过滤条件
   */
  static buildEnterprisePermissionFilter(user) {
    let enterpriseWhere = {};

    if (user) {
      if (user.type === 'user') {
        // 普通用户只能看到自己绑定企业的资产
        enterpriseWhere.user_id = user.id;
      } else if (user.type === 'employee' && user.role !== 'admin') {
        // 非管理员员工只能看到自己负责企业的资产
        enterpriseWhere.employee_id = user.id;
      }
      // 管理员可以看到所有企业的资产，不添加额外过滤条件
    }

    return enterpriseWhere;
  }

  /**
   * 构建资产变更记录查询的 where 条件
   * @param {Object} filters - 过滤条件
   * @returns {Object} 包含各种查询条件的对象
   */
  static buildAssetChangeWhereConditions(filters) {
    const {
      asset_change_id,
      asset_id,
      change_date_start,
      change_date_end,
      creator_name,
      q // 通用搜索关键词
    } = filters;

    const whereConditions = {};
    const assetWhereConditions = {};
    const creatorWhereConditions = {};

    // 通用搜索（支持变更ID、资产ID、制单人姓名的模糊搜索）
    if (q && q.trim()) {
      const searchTerm = q.trim();
      whereConditions[Op.or] = [
        { asset_change_id: { [Op.like]: `%${searchTerm}%` } },
        { remark: { [Op.like]: `%${searchTerm}%` } },
        { '$asset.asset_id$': { [Op.like]: `%${searchTerm}%` } },
        { '$creator.name$': { [Op.like]: `%${searchTerm}%` } }
      ];
    }

    // 变更ID过滤（模糊搜索）
    if (asset_change_id && asset_change_id.trim()) {
      whereConditions.asset_change_id = {
        [Op.like]: `%${asset_change_id.trim()}%`
      };
    }

    // 资产ID过滤（模糊搜索）
    if (asset_id && asset_id.trim()) {
      assetWhereConditions.asset_id = {
        [Op.like]: `%${asset_id.trim()}%`
      };
    }

    // 日期范围过滤
    if (change_date_start && change_date_end) {
      whereConditions.change_date = {
        [Op.between]: [change_date_start, change_date_end]
      };
    } else if (change_date_start) {
      whereConditions.change_date = {
        [Op.gte]: change_date_start
      };
    } else if (change_date_end) {
      whereConditions.change_date = {
        [Op.lte]: change_date_end
      };
    }

    // 制单人过滤（模糊搜索）
    if (creator_name && creator_name.trim()) {
      creatorWhereConditions.name = {
        [Op.like]: `%${creator_name.trim()}%`
      };
    }

    return {
      whereConditions,
      assetWhereConditions,
      creatorWhereConditions
    };
  }

  /**
   * 构建排序条件
   * @param {string} sortBy - 排序字段
   * @param {string} sortOrder - 排序方向
   * @returns {Array} Sequelize order 数组
   */
  static buildOrderCondition(sortBy = 'createdAt', sortOrder = 'DESC') {
    const orderField = sortBy || 'createdAt';
    const orderDirection = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    return [[orderField, orderDirection]];
  }

  /**
   * 构建分页参数
   * @param {number} page - 页码
   * @param {number} pageSize - 每页大小
   * @returns {Object} 包含 offset 和 limit 的对象
   */
  static buildPaginationParams(page = 1, pageSize = 20) {
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);
    return { offset, limit };
  }
}

module.exports = AssetFilterService;
