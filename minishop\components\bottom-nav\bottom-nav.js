Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前激活的选项卡
    currentTab: {
      type: String,
      value: 'activity'
    },
    // 导航栏主题，可选值：'default'(蓝色), 'red'(红色)
    theme: {
      type: String,
      value: 'default'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    activeTab: 'activity', // 默认选中活动标签
    currentTheme: 'default' // 默认蓝色主题
  },

  // 生命周期函数
  lifetimes: {
    attached: function() {
      // 组件初始化时，将传入的currentTab设为激活状态
      if (this.properties.currentTab) {
        this.setData({
          activeTab: this.properties.currentTab
        });
      }
      
      // 设置当前主题
      if (this.properties.theme) {
        this.setData({
          currentTheme: this.properties.theme
        });
      }
    }
  },

  // 属性监听
  observers: {
    'currentTab': function(currentTab) {
      if (currentTab) {
        this.setData({
          activeTab: currentTab
        });
      }
    },
    'theme': function(newVal) {
      // 当外部theme改变时，更新当前主题
      this.setData({
        currentTheme: newVal
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 统一的点击处理函数
     * @param {Object} e - 事件对象
     */
    handleTap(e) {
      const tab = e.currentTarget.dataset.tab;
      // 仅当点击的不是当前标签时才触发事件
      if (this.data.activeTab !== tab) {
        // 触发自定义事件，将点击的 tab 传递给父组件
        this.triggerEvent('bottomnav', { tab: tab });
      }
    }
  }
}) 