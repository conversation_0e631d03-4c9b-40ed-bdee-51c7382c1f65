const app = getApp()
const navService = require('../../utils/navigator.js');

Page({
  data: {
    promotionInfo: {},
    // 账套数基础价格配置
    packageBasePrice: {
      '50': 998,
      '100': 1998,
      '200': 2998,
      '300': 3998, 
      '500': 4998,
      '800': 6998
    },
    // 当前选择
    selectedPackage: '50',    // 默认选择50账套
    selectedDuration: 1,     // 默认选择1年
    selectedDiscount: 0.85,  // 默认折扣
    selectedDiscountText: '', // 新增：用于显示的折扣文本
    // 计算结果
    originalPrice: 0,        // 原价
    discountPrice: 0,        // 特惠价
    dailyPrice: 0,           // 每账套每天价格
    giftProduct: '',         // 赠品产品
    progressPercent: '0%',   // 满减进度条百分比
    // 易代账功能特点
    ydzFeatures: [
      '财务核算',
      '自动票据处理',
      '自动进销台账',
      '税务风险管理',
      '客户管理',
      '代账业务管理',
      '运营服务支持'
    ],
    activeTab: 0,
    tabs: ['套餐详情', '活动规则', '常见问题'],
    activityTimeRange: ""
  },

  onLoad: function () {
    // 获取促销信息
    let promotionInfo = app.globalData.promotionInfo || {};
    
    // 格式化折扣规则，添加用于显示的 discountText
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      promotionInfo.rules = promotionInfo.rules.map(rule => {
        return {
          ...rule,
          discountText: parseFloat((rule.discount * 10).toFixed(1))
        };
      });
    }

    // 格式化活动时间范围
    const startTime = promotionInfo.startTime || '';
    const endTime = promotionInfo.endTime || '';
    const activityTimeRange = startTime.replace(/\//g, '-').substring(0, 10) + 
                             ' 至 ' + 
                             endTime.replace(/\//g, '-').substring(0, 10);
    
    // 获取默认折扣（1年折扣）
    let defaultDiscount = 0.85;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      const rule = promotionInfo.rules.find(item => item.period === 1);
      if (rule) {
        defaultDiscount = rule.discount;
      }
    }
    
    this.setData({
      promotionInfo: promotionInfo,
      activityTimeRange: activityTimeRange,
      selectedDiscount: defaultDiscount,
      selectedDiscountText: parseFloat((defaultDiscount * 10).toFixed(1)), // 设置初始折扣文本
    });
    
    // 计算初始价格
    this.calculatePrice();
  },
  
  /**
   * 选择账套数
   */
  selectPackage: function(e) {
    const packageSize = e.currentTarget.dataset.package;
    this.setData({ selectedPackage: packageSize });
    this.calculatePrice();
  },
  
  /**
   * 选择时长
   */
  selectDuration: function(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    
    // 从促销规则中找出对应时长的折扣
    let discount = 1;
    if (this.data.promotionInfo.rules && this.data.promotionInfo.rules.length > 0) {
      const rule = this.data.promotionInfo.rules.find(item => item.period === duration);
      if (rule) {
        discount = rule.discount;
      }
    }
    
    this.setData({ 
      selectedDuration: duration,
      selectedDiscount: discount,
      selectedDiscountText: parseFloat((discount * 10).toFixed(1)) // 更新折扣文本
    });
    this.calculatePrice();
  },
  
  /**
   * 计算满减进度百分比
   */
  calculateProgressPercent: function(price) {
    const gifts = this.data.promotionInfo.gifts || [];
    if (!gifts || gifts.length < 3) {
      return '0%';
    }
    
    try {
      if (price >= gifts[2].threshold) {
        return '100%';
      } else if (price >= gifts[1].threshold) {
        return '66%';
      } else if (price >= gifts[0].threshold) {
        return '33%';
      } else {
        return '0%';
      }
    } catch (e) {
      console.error('计算满减进度出错:', e);
      return '0%';
    }
  },
  
  /**
   * 计算价格和赠品
   */
  calculatePrice: function() {
    const { selectedPackage, selectedDuration, packageBasePrice, promotionInfo } = this.data;
    
    // 1. 计算原价
    const basePrice = packageBasePrice[selectedPackage];
    const originalPrice = basePrice * selectedDuration;
    
    // 2. 获取对应年限的折扣
    let discount = 1;
    if (promotionInfo.rules && promotionInfo.rules.length > 0) {
      // 从促销规则中查找对应年限的折扣
      const rule = promotionInfo.rules.find(item => item.period === selectedDuration);
      if (rule) {
        discount = rule.discount;
      }
    }
    
    // 3. 计算特惠价
    const discountPrice = (originalPrice * discount).toFixed(1);
    
    // 4. 计算每账套每天价格
    const dailyPrice = (discountPrice / (selectedDuration * 365) / selectedPackage).toFixed(2);
    
    // 5. 根据特惠价确定赠品
    let giftProduct = '';
    const price = parseFloat(discountPrice);
    const gifts = promotionInfo.gifts || [];
    
    // 从大到小查找适用的赠品区间
    for (let i = gifts.length - 1; i >= 0; i--) {
      if (price >= gifts[i].threshold) {
        giftProduct = gifts[i].name;
        break;
      }
    }
    
    // 6. 计算满减进度条百分比
    const progressPercent = this.calculateProgressPercent(price);
    
    this.setData({
      originalPrice,
      discountPrice,
      dailyPrice,
      giftProduct,
      progressPercent
    });
  },
  


  /**
   * 切换标签页
   */
  switchTab: function(e) {
    const tabIndex = parseInt(e.currentTarget.dataset.index);
    this.setData({
      activeTab: tabIndex
    });
  },

  /**
   * 显示产品详情
   */
  showProductDetail: function() {
    navService.navigateToProduct('ydz');
  },



  /**
   * 用户分享
   */
  onShareAppMessage: function () {
    return {
      title: '易代账618超级特惠，力度空前，一年仅此一次！',
      path: '/pages/promotion/promotion',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '易代账618超级特惠，力度空前，一年仅此一次！',
      query: '',
      imageUrl: 'https://mshop.bogoo.net/share_promotion.jpg'
    };
  },


}) 