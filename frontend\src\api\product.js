// /frontend/src/api/product.js
import service from '@/utils/request_extra.js';
// 导入功能相关的API
import { getFeatures } from './feature.js';

const API_PATH = '/products';

/**
 * [新增] 获取下一个可用的产品ID
 */
export const getNextProductId = () => {
  return service.get(`${API_PATH}/next-id`);
}

/**
 * 获取所有产品，用于列表选择
 */
export const getProducts = () => {
  return service.get(API_PATH);
};

/**
 * 根据ID获取单个产品信息
 */
export const getProductById = (id) => {
  return service.get(`${API_PATH}/${id}`);
};

/**
 * 创建一个新产品
 */
export const createProduct = (productData) => {
  return service.post(API_PATH, productData);
};

/**
 * 根据ID更新一个产品
 */
export const updateProduct = (id, productData) => {
  return service.put(`${API_PATH}/${id}`, productData);
};

/**
 * 根据ID删除一个产品
 */
export const deleteProduct = (id) => {
  return service.delete(`${API_PATH}/${id}`);
};

/**
 * 为产品添加一个功能关联
 */
export const addFeatureToProduct = (productId, relationData) => {
  return service.post(`${API_PATH}/${productId}/features`, relationData);
};

/**
 * 从产品中移除一个功能关联
 */
export const removeFeatureFromProduct = (productId, featureId) => {
  return service.delete(`${API_PATH}/${productId}/features/${featureId}`);
};

// 新增：更新产品与功能的关联信息
export const updateFeatureOnProduct = (productId, featureId, data) => {
  return service.put(`${API_PATH}/${productId}/features/${featureId}`, data);
};

/**
 * [新增] 获取指定产品的所有用户数增购阶梯价格
 * @param {string|number} productId - 产品ID
 */
export const getUserAddonTiers = (productId) => {
  return service.get(`${API_PATH}/${productId}/tiers`, {
    params: {
      // [修复] 添加一个时间戳参数来防止GET请求被浏览器缓存
      _t: new Date().getTime(),
    }
  });
};

/**
 * [新增] 保存指定产品的所有用户数增购阶梯价格（覆盖式更新）
 * @param {string|number} productId - 产品ID
 * @param {Array} tiers - 阶梯价格对象数组
 */
export const saveUserAddonTiers = (productId, tiers) => {
  // 注意，请求体需要符合后端要求，将数组放在 tiers 键中
  return service.post(`${API_PATH}/${productId}/tiers`, { tiers: tiers });
};

/**
 * [新增] 获取单个产品下的所有功能
 * @param {string|number} productId - 产品ID
 */
export const getProductFeaturesByProductId = (productId) => {
  return service.get(`${API_PATH}/${productId}/features`);
};

/**
 * 兼容性函数 - 根据参数决定调用哪个功能函数
 * @param {string|number|undefined} productId - 可选的产品ID
 * @returns {Promise} - 返回功能列表的Promise
 */
export const getProductFeatures = (productId) => {
  // 如果提供了productId，调用获取特定产品功能的API
  if (productId) {
    return getProductFeaturesByProductId(productId);
  }
  // 否则获取所有功能
  return getFeatures();
};