const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 订单附件模型 (order_attachment)
 * 存储订单相关的附件信息，支持合同、发票等类型
 */
const OrderAttachment = sequelize.define('OrderAttachment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键'
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联order_head.id'
  },
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '原始文件名'
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '文件大小（字节）'
  },
  file_type: {
    type: DataTypes.ENUM('合同', '发票', '其他'),
    allowNull: false,
    defaultValue: '其他',
    comment: '文件类型'
  },
  file_path: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件存储路径'
  },
  uploader_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '上传人，关联employee.id'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'order_attachment',
  timestamps: true,
  createdAt: 'uploaded_at',
  updatedAt: false,
  underscored: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci'
});

module.exports = OrderAttachment;