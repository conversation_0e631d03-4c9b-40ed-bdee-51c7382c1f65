1. 创建统一导航服务
我创建了 utils/navigator.js 模块，它作为所有页面跳转逻辑的中心。这个服务有以下优点：
集中配置：页面路径、索引等都集中在一处，便于维护
标准化接口：提供标准化的导航方法
错误处理：包含基本的错误处理功能
统一动画：确保页面切换动画统一
完整功能：覆盖所有导航场景（产品页跳转、版本详情、底部导航等）
2. 页面跳转的区别处理
针对不同类型的跳转，我使用了不同的微信小程序导航API：
普通页面跳转：使用 wx.navigateTo (保留当前页)
产品间切换：使用 wx.redirectTo (替换当前页)
Tab页面切换：使用 wx.switchTab (底部tabBar页面)
3. 底部导航栏与普通跳转的区别
底部导航栏：现在使用统一的事件名bottomnav，传递标准化的tab参数
事件处理简化：页面中只需要一个onBottomNavEvent方法处理所有底部导航事件
自动激活状态：底部导航组件根据currentTab属性显示当前激活项

4. 实际使用方式
在页面中引入导航服务：
   const navService = require('../../utils/navigator.js');

添加产品标识
在data中添加对应的productKey：
   data: {
     // 其他数据...
     productKey: '产品标识' // 使用: hkj, ydz, hyc, hsy 等
   },
各产品页面的productKey值
请在各产品页面中使用以下productKey：
好会计页面：hkj
易代账页面：ydz
T+Online页面：tonline (已经设置好了)
好云仓页面：hyc
好生意页面：hsy

修改导航方法
替换所有导航相关方法为navService调用，例如：
   navigateToHome: function() {
     navService.navigateToHome();
   },
   
   navigateToMe: function() {
     navService.handleBottomNav('me');
   },
   
   makePhoneCall: function() {
     navService.makePhoneCall();
   }

使用导航服务进行跳转：
   // 跳转到产品页面
   navService.navigateToProduct('hkj');
   
   // 跳转到版本详情
   navService.navigateToVersionPage('tonline');
   
   // 拨打咨询电话
   navService.makePhoneCall();

处理底部导航：
   // WXML: 
   <bottom-nav currentTab="hot" bindbottomnav="onBottomNavEvent">
   </bottom-nav>
   
   // JS:
   onBottomNavEvent: function(e) {
     const tab = e.detail.tab;
     navService.handleBottomNav(tab);
   }

5. 横向滑动切换
我优化了产品间的横向滑动切换逻辑，使用了产品索引和标识符，
而不是硬编码的数组索引，更加健壮和可维护。
通过这种方式，您可以在所有页面中使用一致的导航逻辑，
不必担心页面之间的导航规则不一致。当需要修改导航行为时，
只需要更新导航服务模块，无需修改每个页面的代码
修改onTouchEnd方法，使用switchProductPage：
   onTouchEnd: function(e) {
     // ... 其他代码 ...
     if (Math.abs(moveX) > swipeThreshold) {
       const direction = moveX > 0 ? 'left' : 'right';
       this.setData({ isAnimating: true });
       
       navService.switchProductPage(this.data.productKey, direction);
       
       setTimeout(() => {
         this.setData({ isAnimating: false });
       }, 350);
     }
   },