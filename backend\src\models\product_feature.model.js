// /backend/src/models/product_feature.model.js

// 引入 Sequelize 库和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 ProductFeature (产品功能) 模型/表
 */
const ProductFeature = sequelize.define(
  'ProductFeature',
  {
    // 字段定义
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '自增主键ID',
    },
    feature_id: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true, // 确保业务ID的唯一性
      comment: '功能ID（如FT001）',
    },
    feature_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '功能名称',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '功能描述',
    },
  },
  {
    // 模型选项
    tableName: 'product_feature', // 强制 Sequelize 使用我们指定的表名
    timestamps: false, // 禁用 createdAt 和 updatedAt 字段
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
  }
);

module.exports = ProductFeature; 