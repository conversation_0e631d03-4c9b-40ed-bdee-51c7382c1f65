// 引入模型调度中心，它包含了所有模型
const db = require('../models');
const { generateProductId } = require('../utils/id_helper'); // [新增] 引入新的ID生成器
const { Op } = require('sequelize'); // [新增] 引入Op操作符
const Product = db.Product;
const ProductFeature = db.ProductFeature; // 引入 ProductFeature 模型

/**
 * [新增] 获取下一个可用的产品ID
 */
exports.getNextProductId = async (req, res) => {
  try {
    const nextId = await generateProductId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个产品ID时出错:', error);
    res.status(500).json({ message: '生成产品ID失败', error: error.message });
  }
};

/**
 * @description 创建一个新产品
 * @param {object} req - Express的请求对象，包含请求体(body)
 * @param {object} res - Express的响应对象
 */
exports.createProduct = async (req, res) => {
  try {
    const newProductData = req.body;

    // [修改] 如果用户没有手动提供 product_id，我们就自动生成一个
    if (!newProductData.product_id) {
      newProductData.product_id = await generateProductId();
    } else {
      // 如果用户手动提供了，我们要检查它是否唯一
      const existing = await Product.findOne({ where: { product_id: newProductData.product_id } });
      if (existing) {
        return res.status(409).json({ message: `产品ID '${newProductData.product_id}' 已存在，请使用其他ID。` });
      }
    }

    const product = await Product.create(newProductData);
    res.status(201).json(product);
  } catch (error) {
    console.error('创建产品时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '创建产品失败，请检查服务器日志。', error: error.message });
  }
};

/**
 * @description 获取所有产品信息
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.getAllProducts = async (req, res) => {
  try {
    // 更新：查询时通过 include 一并加载关联的功能
    const products = await Product.findAll({
      include: [{
        model: ProductFeature,
        as: 'features', // 使用在模型中定义的别名
        attributes: ['id', 'feature_id', 'feature_name'], // [修改] 加上自增ID
        through: {
            attributes: ['id', 'feature_price', 'remark'] // [修改] 加上关联表的自增ID
        }
      }]
    });
    // 返回状态码 200 (OK) 和产品列表
    res.status(200).json(products);
  } catch (error) {
    console.error('获取产品列表时出错:', error);
    res.status(500).json({ message: '获取产品列表失败，请查看服务器日志。' });
  }
};

/**
 * @description 根据ID获取单个产品信息
 * @param {object} req - Express的请求对象，包含URL参数
 * @param {object} res - Express的响应对象
 */
exports.getProductById = async (req, res) => {
  try {
    // 从URL参数中获取产品ID (例如 /api/products/5)
    const { id } = req.params;
    
    // [!] 核心修复：之前这里错误地使用了 product_id 查询，现在修正为通过主键ID查询。
    // 使用 findByPk 是通过主键查找的最高效方式。
    const product = await Product.findByPk(id, {
        include: [{
            model: ProductFeature,
            as: 'features',
            attributes: ['id', 'feature_id', 'feature_name', 'description'],
            through: {
                attributes: ['id', 'feature_price', 'remark']
            }
        }]
    });

    if (product) {
      // 如果找到了产品，返回状态码 200 和产品信息
      res.status(200).json(product);
    } else {
      // 如果根据ID找不到对应的产品
      res.status(404).json({ message: '未找到指定ID的产品' });
    }
  } catch (error) {
    console.error('获取单个产品信息时出错:', error);
    res.status(500).json({ message: '获取产品信息失败，请查看服务器日志。' });
  }
};

/**
 * @description 根据ID更新一个产品的信息
 * @param {object} req - Express的请求对象，包含URL参数和请求体
 * @param {object} res - Express的响应对象
 */
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const productData = req.body;

    // [新增] 如果用户试图修改 product_id，我们要检查新ID的唯一性
    if (productData.product_id) {
      const existing = await Product.findOne({ 
        where: { 
          product_id: productData.product_id,
          id: { [Op.ne]: id } // 排除当前正在编辑的这条记录
        } 
      });
      if (existing) {
        return res.status(409).json({ message: `产品ID '${productData.product_id}' 已被其他产品占用。` });
      }
    }
    
    const productToUpdate = await Product.findByPk(id);

    if (productToUpdate) {
      await productToUpdate.update(productData);
      
      // [修复] 更新后返回包含完整关联的数据
      const updatedProduct = await Product.findByPk(id, {
        include: [{
          model: ProductFeature,
          as: 'features',
          attributes: ['id', 'feature_id', 'feature_name'],
          through: {
              attributes: ['id', 'feature_price', 'remark']
          }
        }]
      });
      res.status(200).json(updatedProduct);
    } else {
      res.status(404).json({ message: '未找到指定ID的产品' });
    }
  } catch (error) {
    console.error('更新产品时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新产品失败，请查看服务器日志。' });
  }
};

/**
 * @description 根据ID删除一个产品
 * @param {object} req - Express的请求对象，包含URL参数
 * @param {object} res - Express的响应对象
 */
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params; // 这个 id 现在是自增主键 ID
    
    // [重要修改] 直接通过主键ID删除，更高效
    const deleted = await Product.destroy({ where: { id: id } });

    if (deleted) {
      // 返回状态码 204 (No Content)，表示成功删除，无需返回内容
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定ID的产品' });
    }
  } catch (error) {
    console.error('删除产品时出错:', error);
    res.status(500).json({ message: '删除产品失败，请查看服务器日志。' });
  }
};

/**
 * [新增] 获取产品的所有可选功能及其价格
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.getProductFeatures = async (req, res) => {
  try {
    const { id } = req.params; // 这是产品的主键ID

    const product = await Product.findByPk(id, {
      // 使用在模型中定义的关联和别名 'features'
      include: [{
        model: ProductFeature,
        as: 'features',
        // 我们只需要功能的核心信息和价格
        attributes: ['id', 'feature_id', 'feature_name', 'description'],
        // 'through' 选项允许我们从中间表(product_feature_relation)中获取属性
        through: {
          attributes: ['feature_price']
        }
      }]
    });

    if (!product) {
      return res.status(404).json({ message: '未找到指定ID的产品' });
    }

    // 从查询结果中提取并格式化功能列表
    const features = product.features.map(feature => {
      return {
        id: feature.id, // 功能自身的主键ID
        feature_id: feature.feature_id, // 功能的业务ID
        feature_name: feature.feature_name, // 功能名称
        description: feature.description, // 功能描述
        // 从中间表获取这个功能针对当前产品的特定价格
        price: feature.ProductFeatureRelation.feature_price 
      };
    });

    res.status(200).json(features);

  } catch (error) {
    console.error('获取产品功能列表时出错:', error);
    res.status(500).json({ message: '获取产品功能列表失败，请检查服务器日志。', error: error.message });
  }
};

/**
 * @description 为产品添加一个功能关联
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.addProductFeature = async (req, res) => {
  try {
    const { productId } = req.params; // 这个 productId 现在是产品的自增主键 ID
    const { feature_id, feature_price, remark } = req.body; // feature_id 依然是业务ID

    // 1. 检查输入
    if (!feature_id || feature_price === undefined) {
      return res.status(400).json({ message: '功能ID和功能价格是必填项。' });
    }

    // 2. 查找产品和功能是否存在
    // [重要修改] 使用 findByPk 通过主键ID高效查询
    const product = await Product.findByPk(productId);
    // [不变] 功能表依然可以使用业务ID查询，因为它在此处是"输入"
    const feature = await ProductFeature.findOne({ where: { feature_id: feature_id } });

    if (!product) {
      return res.status(404).json({ message: '未找到指定ID的产品' });
    }
    if (!feature) {
      return res.status(404).json({ message: '未找到指定ID的功能' });
    }

    // 3. 使用 Sequelize 的 add<Alias> 魔术方法添加关联，并传入中间表的额外数据
    // [重要修改] addFeature 需要传入 feature 对象的主键ID，所以我们需要确保 feature 对象被正确获取
    await product.addFeature(feature, {
      through: { feature_price: feature_price, remark: remark }
    });

    res.status(201).json({ message: '功能已成功关联到产品' });
  } catch (error) {
    console.error('为产品添加功能时出错:', error);
    res.status(500).json({ message: '为产品添加功能失败，请检查服务器日志。', error: error.message });
  }
};

/**
 * @description 从产品中移除一个功能关联
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.removeProductFeature = async (req, res) => {
  try {
    const { productId, featureId } = req.params; // productId 是主键ID, featureId 是业务ID

    // 1. 查找产品和功能
    // [重要修改]
    const product = await Product.findByPk(productId);
    const feature = await ProductFeature.findOne({ where: { feature_id: featureId } });

    if (!product) {
        return res.status(404).json({ message: '未找到指定ID的产品' });
    }
    if (!feature) {
        return res.status(404).json({ message: '未找到指定ID的功能' });
    }

    // 2. 使用 Sequelize 的 remove<Alias> 魔术方法移除关联
    await product.removeFeature(feature);

    res.status(200).json({ message: '产品功能关联已成功移除' });
  } catch (error) {
    console.error('从产品中移除功能时出错:', error);
    res.status(500).json({ message: '从产品中移除功能失败，请检查服务器日志。', error: error.message });
  }
};

/**
 * @description 更新产品与特定功能关联的信息（例如价格、备注）
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.updateProductFeature = async (req, res) => {
  try {
    const { productId, featureId } = req.params; // productId 是主键ID, featureId 是业务ID
    const { feature_price, remark } = req.body;

    // 1. 验证输入
    if (feature_price === undefined) {
      return res.status(400).json({ message: '功能价格是必填项。' });
    }

    // 2. 查找产品和功能
    // [重要修改]
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ message: '未找到指定ID的产品' });
    }

    const feature = await ProductFeature.findOne({ where: { feature_id: featureId } });
    if (!feature) {
      return res.status(404).json({ message: '未找到指定ID的功能' });
    }

    // 3. 使用 addFeature 方法更新中间表。如果关联已存在，它会更新 through 对象的属性
    await product.addFeature(feature, {
      through: { 
        feature_price: Number(feature_price),
        remark: remark 
      }
    });

    res.status(200).json({ message: '产品功能关联信息更新成功' });

  } catch (error) {
    console.error('更新产品功能关联信息时出错:', error);
    res.status(500).json({ message: '更新产品功能关联信息失败', error: error.message });
  }
}; 