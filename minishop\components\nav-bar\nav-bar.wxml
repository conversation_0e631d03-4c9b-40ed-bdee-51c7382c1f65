<view class="floating-nav">
  <scroll-view 
    class="nav-scroll glass-active"
    style="backdrop-filter: blur(20px) contrast(1.2) saturate(1.1); -webkit-backdrop-filter: blur(20px) contrast(1.2) saturate(1.1);"
    scroll-x="{{true}}"
    scroll-with-animation="{{true}}"
    scroll-left="{{scrollLeft}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}">
    
    <!-- 简单玻璃背景效果 -->
    <view class="glass-bg" style="width: {{totalContentWidth}}px;"></view>
    
    <!-- 简单反射效果 -->
    <view class="glass-reflections">
      <view class="reflection reflection-1"></view>
      <view class="reflection reflection-2"></view>
    </view>
    
    <!-- 导航标签 -->
    <view class="nav-tabs">
      <view wx:for="{{tabs}}" 
            wx:key="index" 
            class="nav-item {{index === activeTab ? 'active' : ''}}" 
            data-index="{{index}}" 
            bindtap="onTabClick">{{item.name}}</view>
    </view>
  </scroll-view>
</view> 