const express = require('express');
const router = express.Router();
const { employee } = require('../middleware/auth');

// 引入价格计算控制器
const pricingController = require('../controllers/pricing.controller');

/**
 * @description 定义价格计算相关的路由
 * @route POST /api/pricing/calculate
 * @access Private (需要登录)
 */
router.post(
  '/calculate',
  employee.verifyEmployee, // 调用此接口需要员工登录认证
  pricingController.calculatePrice
);

module.exports = router; 