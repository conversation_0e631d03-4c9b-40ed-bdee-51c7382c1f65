// api/enterprise.js
import service from '@/utils/request_extra.js';

const API_PATH = '/enterprises';

/**
 * [新增] 获取下一个可用的企业ID
 */
export const getNextEnterpriseId = () => {
  return service.get(`${API_PATH}/next-id`);
}

/**
 * 获取企业列表的函数
 * @param {object} params - 查询参数，例如 { userId: 1, q: 'keyword' }
 * @returns Promise
 */
export const getEnterprises = (params = {}) => {
  // 发送 GET 请求到 /api/enterprises
  return service.get(API_PATH, { params });
};

/**
 * 创建一个新企业
 * @param {FormData} formData - 包含新企业信息的对象
 */
export const createEnterprise = (formData) => {
  return service.post(API_PATH, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 更新一个企业的信息
 * @param {string} id - 要更新的企业的ID
 * @param {FormData} formData - 包含更新信息的对象
 */
export const updateEnterprise = (id, formData) => {
  return service.put(`${API_PATH}/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 删除一个企业
 * @param {string} id - 要删除的企业的ID
 */
export const deleteEnterprise = (id) => {
  return service.delete(`${API_PATH}/${id}`);
};

/**
 * [新增] 根据ID获取单个企业的详细信息
 * @param {string | number} id - 企业ID
 */
export const getEnterpriseById = (id) => {
  return service.get(`${API_PATH}/${id}`);
};

// 未来我们可以在这里添加更多函数，例如：
// export const createEnterprise = (data) => service.post('/', data);
// export const getEnterpriseById = (id) => service.get(`/${id}`); 