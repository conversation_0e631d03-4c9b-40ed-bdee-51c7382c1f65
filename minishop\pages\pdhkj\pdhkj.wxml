<view class="container">
  <!-- 使用顶部导航栏组件 -->
  <nav-bar activeTab="{{activeTab}}" bindtabchange="handleTabChange" class="full-width-nav"></nav-bar>

  <!-- 整个内容区域 -->
  <!-- 产品海报区域 -->
  <view class="poster" 
        style="background: linear-gradient(135deg, #4643d3 0%, #3987ea 50%, #6a40f3 100%);"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd">
    
    <!-- 背景元素 -->
    <view class="poster-background">
      <!-- 几何动效元素 -->
      <view class="geo-element circle-1"></view>
      <view class="geo-element circle-2"></view>
      <view class="geo-element circle-3"></view>
      <view class="geo-element square-1"></view>
      <view class="geo-element square-2"></view>
      <view class="geo-element triangle-1"></view>
      <view class="dot-grid"></view>
      
      <!-- 动态连接线 -->
      <view class="connecting-lines">
        <view class="line line-1"></view>
        <view class="line line-2"></view>
        <view class="line line-3"></view>
      </view>
      
      <!-- 波浪动效 -->
      <view class="wave-container">
        <view class="wave wave1"></view>
        <view class="wave wave2"></view>
      </view>
      
      <!-- 渐变光晕 -->
      <view class="glow-effect"></view>
      
      <!-- 粒子效果容器 -->
      <view class="particles-container">
        <view class="particle" wx:for="{{12}}" wx:key="index"></view>
      </view>
    </view>
    
    <!-- 产品标题 -->
    <view class="product-title">
      <view class="title-main">{{posterTitle}}</view>
      <view class="title-divider"></view>
      <view class="title-sub">{{posterSubtitle}}</view>
      <view class="slogan">{{posterSlogan}}</view>
    </view>
    
    <!-- 促销卡片 -->
    <view class="promo-card-position">
      <promo-card initialShow="{{true}}" pageKey="{{productKey}}" bindjoinpromo="handleJoinPromo"></promo-card>
    </view>
    
    <!-- 底部羽化效果 -->
    <view class="poster-bottom-fade"></view>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content">
    <!-- 主标题 -->
    <view class="main-header">
      <view class="header-content">
        <view class="main-title">您的智慧财务系统</view>
        <view class="main-subtitle">一站式财税管理解决方案，让财务管理更轻松</view>
        <view class="divider"></view>
      </view>
    </view>

    <!-- 产品亮点展示 -->
    <view class="highlights-section">
      <view class="highlights-container">
        <view class="highlight-card">
          <view class="highlight-icon">
            <text class="iconfont icon-kuaisugaoxiao"></text>
          </view>
          <view class="highlight-info">
            <view class="highlight-title">高效智能</view>
            <view class="highlight-desc">智能化处理，效率提升80%</view>
          </view>
        </view>
        <view class="highlight-card">
          <view class="highlight-icon">
            <text class="iconfont icon-zhenshikexin"></text>
          </view>
          <view class="highlight-info">
            <view class="highlight-title">安全可靠</view>
            <view class="highlight-desc">金融级数据加密技术</view>
          </view>
        </view>
        <view class="highlight-card">
          <view class="highlight-icon">
            <text class="iconfont icon-shangyun"></text>
          </view>
          <view class="highlight-info">
            <view class="highlight-title">云端同步</view>
            <view class="highlight-desc">多端协同，随时随地办公</view>
          </view>
        </view>
        <view class="highlight-card">
          <view class="highlight-icon">
            <text class="iconfont icon-fuwu"></text>
          </view>
          <view class="highlight-info">
            <view class="highlight-title">专业服务</view>
            <view class="highlight-desc">一对一专业财税顾问</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 生态互联图 -->
    <view class="ecosystem-section">
      <view class="section-header modern">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-lianmenglian"></text>
          </view>
          <view class="section-title">生态互联 全员参与</view>
        </view>
        <view class="section-subtitle">打通多方数据，实现企业会计轻松管理</view>
        <view class="section-decoration"></view>
      </view>
      
      <view class="accordion-container">
        <view 
          wx:for="{{ecoItems}}" 
          wx:key="title" 
          class="accordion-item {{item.isExpanded ? 'expanded' : ''}}"
          bindtap="toggleEcoItem"
          data-index="{{index}}"
        >
          <!-- 卡片标题栏 -->
          <view class="accordion-header">
            <view class="accordion-icon iconfont {{item.icon}}"></view>
            <view class="accordion-title">{{item.title}}</view>
          </view>
          
          <!-- 卡片内容区 -->
          <view class="accordion-content" wx:if="{{item.isExpanded}}">
            <view wx:for="{{item.items}}" wx:for-item="detail" wx:key="*this" class="accordion-detail-item">
              <text>{{detail}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 补充说明 -->
      <view class="ecosystem-description">
        <view class="eco-description-title">全面生态互联，打造企业管理闭环</view>
        <view class="eco-description-content">
          好会计系统打通企业各部门业务流程，实现财务与业务数据的无缝集成。从老板决策到员工执行，从财务管理到税务申报，构建企业运营全流程数据闭环，让企业管理更加高效透明。
        </view>
      </view>
    </view>

    <!-- 做账更方便 -->
    <view class="feature-section modern">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-fapiao"></text>
          </view>
          <view class="section-title">让做账更方便</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature1.jpg" mode="widthFix"></image>
          <view class="feature-badge">提升效率</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">软件可以一键获取发票、银行流水、存货出入库等业务单据；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">根据获取的单据信息，自动生成凭证，会计负责查看与修正；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">手工录入凭证时，无需自己写分录，可以调用凭证模板，完成自动做账，自动关联辅助核算，不设置明细科目也能行。</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">智能识别单据信息，自动匹配会计科目，大幅减少手动录入工作量。</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 报表更实用 -->
    <view class="feature-section modern alt">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-tubiaozhutu"></text>
          </view>
          <view class="section-title">让报表更实用</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature2.jpg" mode="widthFix"></image>
          <view class="feature-badge">数据可视化</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">软件可以自动生成财务报表，并且适用于最新的会计准则/制度；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">有季度的利润表、季度现金流量表，会计不用手工汇总啦；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">管理报表助力会计进行统计分析、汇报工作，更支持自定义报表取值。</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">多维度数据图表展示，让企业经营状况一目了然，辅助管理层决策。</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 结账更省力 -->
    <view class="feature-section modern">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-wendingkekao"></text>
          </view>
          <view class="section-title">让结账更省力</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature3.jpg" mode="widthFix"></image>
          <view class="feature-badge">智能检查</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">月末结账时，提供结账模板，可以一键生成凭证，防止常规业务遗忘；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">有库存的企业，可以实现库存月结，保证月末成本计算精准；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">结账时自动执行36项月末检查，给出账务系统中存在的问题，点击问题即可进入具体页面，无需会计自己熬夜查找。</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">自动结转损益，生成结账报告，大幅减少月末结账工作量和错误率。</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 记账更简单 -->
    <view class="feature-section modern alt">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-shuzihua"></text>
          </view>
          <view class="section-title">让记账更简单</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature5.jpg" mode="widthFix"></image>
          <view class="feature-badge">云端协作</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">在家办公，电脑联网就能用；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">出差在外，打开手机就能查账；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">多地多人协同，做账高效、快捷</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">云端数据实时同步，无需担心数据丢失，多终端访问更便捷。</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 管理更全面 -->
    <view class="feature-section modern">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-fenbushi"></text>
          </view>
          <view class="section-title">让管理更全面</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature6.jpg" mode="widthFix"></image>
          <view class="feature-badge">全面管理</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">日记账管理：可直接根据银行回单生成日记账，一键生成凭证，高效联查；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">资产管理：自动计提折旧，资产变动处理便捷，固定资产、无形资产、待摊费用全生命周期管理；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">工资管理：薪资、个税自动计算，凭证一键生成；智能工资条，一键群发；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">数电要全生命周期自动化管理，发票信息实时获取，发票验真查重，轻松入账归档。</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 报税更放心 -->
    <view class="feature-section modern alt">
      <view class="section-header side">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-jianguanfengkong"></text>
          </view>
          <view class="section-title">让报税更放心</view>
        </view>
        <view class="section-decoration left"></view>
      </view>
      
      <view class="feature-card vertical">
        <view class="feature-image full-width">
          <image src="https://mshop.bogoo.net/hkj_feature4.jpg" mode="widthFix"></image>
          <view class="feature-badge">合规申报</view>
        </view>
        
        <view class="feature-content">
          <view class="feature-detail">
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">税表清册，根据最新时政自动生成报税表格，报税有依据；</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">自动监控企业税负情况，通过税负测算、风险预警等功能实现税务管理。</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">智能税务筹划建议，合理避税，提升企业经营效益。</view>
            </view>
            <view class="feature-point">
              <view class="point-icon"><text class="iconfont icon-xuanzhong"></text></view>
              <view class="point-text">自动更新最新税收政策，确保申报合规，避免税务风险。</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 功能特色展示 -->
    <view class="feature-highlights">
      <view class="section-header modern">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-fenbushi"></text>
          </view>
          <view class="section-title">好会计特色功能</view>
        </view>
        <view class="section-subtitle">与众不同的功能特色，给您带来更好的使用体验</view>
        <view class="section-decoration"></view>
      </view>
      
      <view class="feature-highlights-slider">
        <view class="highlight-slide">
          <view class="highlight-slide-icon">
            <text class="iconfont icon-chayan"></text>
          </view>
          <view class="highlight-slide-title">智能票据识别</view>
          <view class="highlight-slide-desc">智能AI识别技术自动读取票据信息，准确率高达99%，大幅减少人工录入</view>
        </view>
        <view class="highlight-slide">
          <view class="highlight-slide-icon">
            <text class="iconfont icon-lianjie"></text>
          </view>
          <view class="highlight-slide-title">电子账本</view>
          <view class="highlight-slide-desc">全部凭证一键生成电子账本，自带封面，支持下载、打印、装订</view>
        </view>
        <view class="highlight-slide">
          <view class="highlight-slide-icon">
            <text class="iconfont icon-piaoju"></text>
          </view>
          <view class="highlight-slide-title">智能科目匹配</view>
          <view class="highlight-slide-desc">AI自动匹配会计科目，降低会计人员专业门槛，提高记账效率</view>
        </view>
      </view>
    </view>
    
    <!-- 一体化服务 -->
    <view class="integrated-service modern">
      <view class="section-header modern">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-pingtai"></text>
          </view>
          <view class="section-title">为您提供财务管理一体化服务</view>
        </view>
        <view class="section-subtitle">全方位集成解决方案，企业财务全流程管理</view>
        <view class="section-decoration"></view>
      </view>
      
      <view class="service-grid">
        <view class="service-item" wx:for="{{serviceItems}}" wx:key="name">
          <view class="service-item-inner">
            <text class="iconfont {{item.icon}} service-icon-font"></text>
            <view class="service-content">
              <view class="service-name">{{item.name}}</view>
              <view class="service-desc">{{item.desc}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 适用行业 -->
    <view class="industries-section">
      <view class="section-header modern">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-lianmenglian"></text>
          </view>
          <view class="section-title">适用行业</view>
        </view>
        <view class="section-subtitle">多行业解决方案，满足不同企业需求</view>
        <view class="section-decoration"></view>
      </view>
      
      <view class="industries-grid">
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-shangyebaihuo"></text>
          </view>
          <view class="industry-name">商贸零售</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-zhuanyeshebeizhizaoye"></text>
          </view>
          <view class="industry-name">制造业</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-fuwu"></text>
          </view>
          <view class="industry-name">服务业</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-jianzhuhangye"></text>
          </view>
          <view class="industry-name">建筑业</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-wuliu"></text>
          </view>
          <view class="industry-name">物流运输</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-kejihangye"></text>
          </view>
          <view class="industry-name">IT科技</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-kejipeixun1"></text>
          </view>
          <view class="industry-name">教育培训</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-shouhouguanli"></text>
          </view>
          <view class="industry-name">医疗健康</view>
        </view>
        <view class="industry-card">
          <view class="industry-icon">
            <text class="iconfont icon-nonglinmuyu"></text>
          </view>
          <view class="industry-name">农林牧渔</view>
        </view>
      </view>

      <!-- 提示文字 -->
      <view class="industry-tip">
        <text>好会计适用于不同行业财务管理，</text>
        <text>欢迎与我们沟通您的财务管理需求！</text>
      </view>
    </view>

    <!-- 服务保障区域 -->
    <view class="service-guarantee-section modern">
      <view class="section-header modern">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="iconfont icon-zhenshikexin"></text>
          </view>
          <view class="section-title">服务保障</view>
        </view>
        <view class="section-subtitle">全方位服务支持，让您安心使用</view>
        <view class="section-decoration"></view>
      </view>
      
      <view class="service-guarantee-grid">
        <view class="service-guarantee-card" wx:for="{{serviceGuarantees}}" wx:key="title">
          <text class="iconfont {{item.icon}} service-guarantee-icon"></text>
          <view class="service-guarantee-title">{{item.title}}</view>
          <view class="service-guarantee-desc">{{item.desc}}</view>
        </view>
      </view>
    </view>
    
    <!-- 咨询区域 -->
    <view class="cta-section modern">
      <view class="cta-content">
        <view class="cta-title">立即体验智慧财务管理</view>
        <view class="cta-desc">好会计助力企业高效管理，优化财务流程</view>
        <view class="cta-buttons">
          <button class="cta-btn outline" bindtap="makePhoneCall">
            <text class="btn-text">电话咨询</text>
          </button>
          <button class="cta-btn primary" open-type="contact">微信咨询</button>
        </view>
      </view>
    </view>

    <!-- 底部留白 -->
    <view style="height: 40rpx;"></view>


  </view>

  <!-- 使用底部导航栏组件 -->
  <bottom-nav currentTab="hot" bindbottomnav="onBottomNavEvent"></bottom-nav>
  
  <!-- 添加悬浮咨询按钮 -->
  <float-consult positionKey="pdhkj"></float-consult>
</view> 