<view class="container">
  <!-- 使用顶部导航栏组件 -->
  <nav-bar activeTab="{{activeTab}}" bindtabchange="handleTabChange"></nav-bar>

  <!-- 整个内容区域使用scroll-view包裹，实现整体滚动 -->
  <scroll-view 
    scroll-y="true" 
    class="scroll-container">
    
    <!-- 产品海报区域 -->
    <view class="poster" 
          style="background: linear-gradient(135deg, #0e74e9, #0257c7);"
          bindtouchstart="onTouchStart"
          bindtouchmove="onTouchMove"
          bindtouchend="onTouchEnd">
      
      <!-- 背景元素 -->
      <view class="poster-background">
        <!-- 现代简约几何元素 -->
        <view class="geometric-container">
          <!-- 简约几何元素 -->
          <view class="geo-circle geo-small"></view>
          <view class="geo-circle geo-medium"></view>
          <view class="geo-circle geo-large"></view>
          
          <!-- 简约装饰线条 -->
          <view class="geo-line line-1"></view>
          <view class="geo-line line-2"></view>
          
          <!-- 中央光晕 -->
          <view class="center-glow"></view>
          
          <!-- 添加浮动圆点 -->
          <view class="floating-dots">
            <view class="dot dot1"></view>
            <view class="dot dot2"></view>
            <view class="dot dot3"></view>
            <view class="dot dot4"></view>
          </view>
        </view>
        
        <!-- 全息投影效果 -->
        <view class="hologram-effect"></view>
      </view>
      
      <!-- 产品标题 -->
      <view class="product-title">
        <view class="title-main">{{posterTitle}}</view>
        <view class="title-sub">{{posterSubtitle}}</view>
        <view class="title-divider"></view>
        <view class="slogan">{{posterSlogan}}</view>
      </view>
      
      <!-- 促销卡片 -->
      <!-- <view class="promo-card-position">
        <promo-card initialShow="{{true}}" pageKey="{{productKey}}" bindjoinpromo="handleJoinPromo"></promo-card>
      </view> -->
      
      <!-- 底部羽化效果 -->
      <view class="poster-bottom-fade"></view>
    </view>

    <!-- 详情内容 - 直接放在海报下方 -->
    <view class="detail-content">
      <!-- 智能财税痛点区域 -->
      <view class="pain-points-section">
        <view class="section-header">
          <view class="section-title">这些问题是否一直困扰着您？</view>
          <view class="section-subtitle">企业财务管理面临的挑战</view>
        </view>
        
        <view class="pain-points-container">
          <!-- 涉税风险 -->
          <view class="pain-point-card">
            <view class="pain-point-icon">
              <text class="iconfont icon-jianguanfengkong"></text>
            </view>
            <view class="pain-point-content">
              <view class="pain-point-title">涉税风险激增</view>
              <view class="pain-point-subtitle">企业成本过高</view>
              <view class="pain-point-desc">
                企业缺乏专业税务筹划经验，无法降低企业运营成本，资金周转能力差，容易导致资金短缺
              </view>
            </view>
          </view>
          
          <!-- 会计 -->
          <view class="pain-point-card">
            <view class="pain-point-icon">
              <text class="iconfont icon-jiangben"></text>
            </view>
            <view class="pain-point-content">
              <view class="pain-point-title">账税管理难</view>
              <view class="pain-point-subtitle">核算难度加大</view>
              <view class="pain-point-desc">
                企业会计面对复杂多变的政策规定，账务处理工作量大，核算工作压力大
              </view>
            </view>
          </view>
          
          <!-- 效率 -->
          <view class="pain-point-card">
            <view class="pain-point-icon">
              <text class="iconfont icon-kuaisugaoxiao"></text>
            </view>
            <view class="pain-point-content">
              <view class="pain-point-title">效率低下</view>
              <view class="pain-point-subtitle">资源又浪费</view>
              <view class="pain-point-desc">
                处理财务流程一人一步骤，人为审批环节多，合作效率低下，一票一单处理？
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 从IT到AI替 智能财税全场景 -->
      <view class="ai-finance-section">
        <view class="section-header">
          <view class="section-title">从IT到"AI替" 智能财税全应用场景</view>
          <view class="section-subtitle">人工智能驱动的财税解决方案</view>
        </view>
        
        <view class="ai-features-container">
          <!-- 查验取票 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-chayan"></text>
            </view>
            <view class="ai-feature-title">查验取票</view>
            <view class="ai-feature-desc">
              查验金额流水，智能管理支付
            </view>
          </view>
          
          <!-- 做账 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-fapiao"></text>
            </view>
            <view class="ai-feature-title">做账</view>
            <view class="ai-feature-desc">
              智能财务核算，一键完成账务
            </view>
          </view>
          
          <!-- 出表 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-tubiaozhutu"></text>
            </view>
            <view class="ai-feature-title">出表</view>
            <view class="ai-feature-desc">
              财务报表一键出，各类手工计算省
            </view>
          </view>
          
          <!-- 在线财务管理 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-shujufenxi"></text>
            </view>
            <view class="ai-feature-title">在线财务管理</view>
            <view class="ai-feature-desc">
              数据驱动智能决策，账表全景可视化
            </view>
          </view>
          
          <!-- 电子档案 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-piaoju"></text>
            </view>
            <view class="ai-feature-title">电子档案</view>
            <view class="ai-feature-desc">
              电子凭证管理轻松，购销、费用、出纳
            </view>
          </view>
          
          <!-- 税务风险检测 -->
          <view class="ai-feature-item">
            <view class="ai-feature-icon">
              <text class="iconfont icon-jianguanfengkong"></text>
            </view>
            <view class="ai-feature-title">税务风险检测</view>
            <view class="ai-feature-desc">
              自动预报，风险检测
            </view>
          </view>
        </view>
        
        <view class="ai-platform-slogan">
          <view class="slogan-text">带有AI属性的数智财税工作平台</view>
        </view>
      </view>
      
      <!-- 性价比出众 卓越配置 -->
      <view class="value-config-section">
        <view class="section-header">
          <view class="section-title">性价比出众 卓越配置</view>
          <view class="section-subtitle">为您的企业提供全方位智能财税解决方案</view>
        </view>
        
        <view class="value-config-container">
          <!-- 添加中央圆圈 -->
          <view class="central-circle-container">
            <view class="central-circle">
              <view class="circle-glow"></view>
              <view class="circle-border"></view>
              <view class="circle-content">
                <view class="circle-title">连接 + 融合</view>
              </view>
            </view>
          </view>
          
          <view class="config-grid">
            <!-- 银企互联 -->
            <view class="config-item">
              <view class="config-icon">
                <text class="iconfont icon-renminbi"></text>
              </view>
              <view class="config-title">银企互联</view>
              <view class="config-desc">银企互联、资金管理、对账支付</view>
            </view>
            
            <!-- 税企互联 -->
            <view class="config-item">
              <view class="config-icon">
                <text class="iconfont icon-yinzhangrenzheng"></text>
              </view>
              <view class="config-title">税企互联</view>
              <view class="config-desc">税务数字账户、税企业管理系统对接</view>
            </view>
            
            <!-- 进销管理 -->
            <view class="config-item">
              <view class="config-icon highlighted">
                <text class="iconfont icon-dingdan"></text>
              </view>
              <view class="config-title">进销管理</view>
              <view class="config-desc">财电票管理</view>
              <view class="config-tag">新</view>
            </view>
            
            <!-- AI财务 -->
            <view class="config-item">
              <view class="config-icon highlighted">
                <text class="iconfont icon-shujufenxi"></text>
              </view>
              <view class="config-title">AI财务</view>
              <view class="config-desc">全能报表、自动账期、自动账务、账务分析</view>
              <view class="config-tag hot">热</view>
            </view>
            
            <!-- AI税务 -->
            <view class="config-item">
              <view class="config-icon highlighted">
                <text class="iconfont icon-tubiaozhutu"></text>
              </view>
              <view class="config-title">AI税务</view>
              <view class="config-desc">风险预测、风控自动化</view>
              <view class="config-tag hot">热</view>
            </view>
            
            <!-- 电子档案 -->
            <view class="config-item">
              <view class="config-icon">
                <text class="iconfont icon-piaoju"></text>
              </view>
              <view class="config-title">电子档案</view>
              <view class="config-desc">手机端、软件端数据上传入系统</view>
            </view>
            
            <!-- 经营管理 -->
            <view class="config-item">
              <view class="config-icon highlighted">
                <text class="iconfont icon-kuaisugaoxiao"></text>
              </view>
              <view class="config-title">经营管理</view>
              <view class="config-desc">资源管理、业绩分析、业绩对比</view>
              <view class="config-tag recommend">荐</view>
            </view>
            
            <!-- 连接服务 -->
            <view class="config-item">
              <view class="config-icon">
                <text class="iconfont icon-lianjie"></text>
              </view>
              <view class="config-title">连接服务</view>
              <view class="config-desc">深度业务对接，10分钟内完成</view>
            </view>
            
            <!-- 连接管理 -->
            <view class="config-item">
              <view class="config-icon">
                <text class="iconfont icon-lianmenglian"></text>
              </view>
              <view class="config-title">连接管理</view>
              <view class="config-desc">连接企微钉钉，协同办公</view>
            </view>
          </view>
          
          <!-- 添加底部功能模块 -->
          <view class="config-features-footer">
            <view class="feature-item">
              <view class="feature-title">票财税一体化</view>
            </view>
            <view class="feature-item">
              <view class="feature-title">报销-入账-支付一体化</view>
            </view>
            <view class="feature-item">
              <view class="feature-title">发票全生命周期管理</view>
            </view>
            <view class="feature-item">
              <view class="feature-title">资产全生命周期管理</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 财务智能化提效 数据全方位保护 -->
      <view class="data-security-section">
        <view class="section-header">
          <view class="section-title">财务智能化提效 数据全方位保护</view>
          <view class="section-subtitle">企业数据安全是我们的首要任务</view>
        </view>
        
        <view class="security-subtitle">
          <view class="subtitle-text">100+流云服务器配置，一流文化保障您的数据安全</view>
        </view>
        
        <view class="security-shield-container">
          
          <view class="security-features-list">
            <!-- 屏蔽物理攻击 -->
            <view class="security-feature-item">
              <view class="security-icon">
                <text class="iconfont icon-jianguanfengkong"></text>
              </view>
              <view class="security-feature-content">
                <view class="security-feature-title">屏蔽物理攻击</view>
                <view class="security-feature-desc">高防护盾，防范网络攻击，数据多节点一键备份</view>
              </view>
            </view>
            
            <!-- 数据安全保障 -->
            <view class="security-feature-item">
              <view class="security-icon">
                <text class="iconfont icon-zhenshikexin"></text>
              </view>
              <view class="security-feature-content">
                <view class="security-feature-title">数据安全保障</view>
                <view class="security-feature-desc">多重加密备份，专业团队7*24小时保障</view>
              </view>
            </view>
            
            <!-- 访问策略控制 -->
            <view class="security-feature-item">
              <view class="security-icon">
                <text class="iconfont icon-wendingkekao"></text>
              </view>
              <view class="security-feature-content">
                <view class="security-feature-title">访问策略控制</view>
                <view class="security-feature-desc">账号、应用、权限、证书精细化控制</view>
              </view>
            </view>
            
            <!-- 主动安全防护 -->
            <view class="security-feature-item">
              <view class="security-icon">
                <text class="iconfont icon-kuaisugaoxiao"></text>
              </view>
              <view class="security-feature-content">
                <view class="security-feature-title">主动安全防护</view>
                <view class="security-feature-desc">自主安全防御，日志、防火墙、入侵检测</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 了解更多 - 作为数据安全区域的延伸部分 -->
      <view class="more-section">
        <view class="more-content-wrapper">
          <view class="more-content">
            <view class="more-title">咨询客服了解更多适合您的应用</view>
            <view class="more-actions">
              <button class="action-btn" open-type="contact">
                <view class="btn-inner">立即咨询</view>
              </button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部留白 -->
      <view class="bottom-space"></view>
    </view>
  </scroll-view>

  <!-- 使用底部导航栏组件 -->
  <bottom-nav currentTab="hot" theme="blue" bindbottomnav="onBottomNavEvent"></bottom-nav>
  
  <!-- 添加悬浮咨询按钮 -->
  <float-consult positionKey="pdtzsypjb"></float-consult>
</view> 