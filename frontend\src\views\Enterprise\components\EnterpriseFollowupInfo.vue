<template>
  <div class="followup-info">
    <!-- 新增按钮 -->
    <div class="action-bar" v-if="!readonly" style="margin-bottom: 16px;">
      <el-button type="primary" @click="handleAddFollowup">
        新增跟进记录
      </el-button>
    </div>

    <!-- 跟进记录表格 -->
    <el-table :data="followups" v-loading="followupLoading" border style="width: 100%">
      <el-table-column prop="id" label="记录ID" width="80"></el-table-column>
      <el-table-column label="跟进时间" width="200">
        <template #default="scope">
          {{ formatDateTime(scope.row.followup_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="situation" label="跟进情况" min-width="200"></el-table-column>
      <el-table-column label="附件" width="150">
        <template #default="scope">
          <el-button
            v-if="scope.row.attachment"
            type="primary"
            link
            @click="handleDownloadAttachment(scope.row)"
          >
            下载附件
          </el-button>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="跟进员工" width="120">
        <template #default="scope">
          {{ scope.row.employee ? scope.row.employee.name : 'N/A' }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150"></el-table-column>
      <el-table-column label="操作" width="180" v-if="!readonly">
        <template #default="scope">
          <el-button size="small" @click="handleEditFollowup(scope.row)">修改</el-button>
          <el-button size="small" type="danger" @click="handleDeleteFollowup(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/修改跟进记录弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isFollowupEditMode ? '修改跟进记录' : '新增跟进记录'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="newFollowup" label-width="100px" ref="followupFormRef">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="跟进时间" prop="followup_time" :rules="[{ required: true, message: '请选择跟进时间', trigger: 'change' }]">
              <el-date-picker
                v-model="newFollowup.followup_time"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%;"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跟进员工" prop="employee_id" :rules="[{ required: true, message: '请选择跟进员工', trigger: 'change' }]">
              <el-select
                v-model="newFollowup.employee_id"
                placeholder="选择员工"
                clearable
                filterable
                :disabled="!isAdmin"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in employeesList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="跟进情况" prop="situation" :rules="[{ required: true, message: '请填写跟进情况', trigger: 'blur' }]">
          <el-input v-model="newFollowup.situation" type="textarea" :rows="3" placeholder="请描述跟进情况"></el-input>
        </el-form-item>
        <el-form-item label="附件上传">
          <el-upload
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            :file-list="fileList"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持上传单个文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="newFollowup.remark" type="textarea" placeholder="请填写备注信息（可选）"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleFollowupSubmit" :loading="submitting">
            {{ isFollowupEditMode ? '更新' : '提交' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, defineProps, defineEmits } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getFollowupsByEnterpriseId, createFollowup, updateFollowup, deleteFollowup } from '@/api/followup.js';
import { getEmployees } from '@/api/employee.js';
import { useAuth } from '@/store/auth.js';
import { formatDateTime } from '@/utils/format.js';
import service from '@/utils/request_extra.js';

// Props
const props = defineProps({
  enterpriseId: {
    type: [Number, String],
    required: true,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(['followup-updated']);

// Auth and State
const { state: authState } = useAuth();
const isAdmin = computed(() => authState.user?.role === 'admin');

// Component-Specific State
const followupLoading = ref(false);
const followups = ref([]);
const employeesList = ref([]);

const getInitialFollowupForm = () => ({
  followup_time: new Date(),
  situation: '',
  employee_id: authState.user?.id ?? null, // Default to current user
  remark: '',
});

const newFollowup = ref(getInitialFollowupForm());
const followupFile = ref(null);
const isFollowupEditMode = ref(false);
const dialogVisible = ref(false);
const submitting = ref(false);
const fileList = ref([]);
const followupFormRef = ref(null);

// Methods
const loadFollowups = async () => {
  if (!props.enterpriseId) return;
  try {
    followupLoading.value = true;
    const response = await getFollowupsByEnterpriseId(props.enterpriseId);
    followups.value = response;
  } catch (error) {
    ElMessage.error('获取跟进记录失败');
  } finally {
    followupLoading.value = false;
  }
};

const loadEmployees = async () => {
    try {
        employeesList.value = await getEmployees();
    } catch(e) {
        ElMessage.error('获取员工列表失败');
    }
}

// 新增跟进记录
const handleAddFollowup = () => {
  resetForm();
  isFollowupEditMode.value = false;
  dialogVisible.value = true;
};

// 编辑跟进记录
const handleEditFollowup = (followup) => {
  newFollowup.value = JSON.parse(JSON.stringify(followup));
  isFollowupEditMode.value = true;
  fileList.value = [];
  dialogVisible.value = true;
};

const handleFileChange = (file) => {
  followupFile.value = file.raw;
  fileList.value = [file];
};

const handleFollowupSubmit = async () => {
  // 表单验证
  try {
    await followupFormRef.value?.validate();
  } catch (error) {
    ElMessage.warning('请完善表单信息');
    return;
  }

  submitting.value = true;
  try {
    const formData = new FormData();
    const followupTime = newFollowup.value.followup_time instanceof Date
      ? newFollowup.value.followup_time.toISOString()
      : new Date(newFollowup.value.followup_time).toISOString();

    formData.append('enterprise_id', props.enterpriseId);
    formData.append('followup_time', followupTime);
    formData.append('situation', newFollowup.value.situation);
    formData.append('employee_id', newFollowup.value.employee_id);
    formData.append('remark', newFollowup.value.remark || '');

    if (followupFile.value) {
      formData.append('attachment', followupFile.value);
    }

    if (isFollowupEditMode.value) {
      await updateFollowup(newFollowup.value.id, formData);
      ElMessage.success('修改跟进记录成功');
    } else {
      await createFollowup(formData);
      ElMessage.success('添加跟进记录成功');
    }

    handleDialogClose();
    await loadFollowups();
    emit('followup-updated');
  } catch (error) {
    ElMessage.error((isFollowupEditMode.value ? '修改' : '添加') + '失败: ' + (error.response?.data?.message || error.message));
  } finally {
    submitting.value = false;
  }
};

const handleDeleteFollowup = (followupId) => {
  ElMessageBox.confirm('您确定要删除这条跟进记录吗？', '警告', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteFollowup(followupId);
      ElMessage.success('删除成功');
      await loadFollowups();
      emit('followup-updated');
    } catch (error) {
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 关闭弹窗
const handleDialogClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  isFollowupEditMode.value = false;
  newFollowup.value = getInitialFollowupForm();
  followupFile.value = null;
  fileList.value = [];
  followupFormRef.value?.clearValidate();
};

// 处理跟进记录附件下载的方法
const handleDownloadAttachment = (row) => {
  if (!row.id || !row.attachment) {
    ElMessage.warning('没有可供下载的附件');
    return;
  }
  const baseURL = service.defaults.baseURL || '';
  const token = localStorage.getItem('authToken');
  const downloadUrl = `${baseURL}/followups/${row.id}/attachment/download?token=${token}`;
  window.open(downloadUrl, '_blank');
};

// Watchers
watch(() => props.enterpriseId, (newEnterpriseId) => {
  if (newEnterpriseId) {
    resetForm();
    loadFollowups();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  loadEmployees();
});
</script>

<style scoped>
.followup-info {
  padding: 20px;
}

.followup-form {
  margin-top: 20px;
}

.followup-form h4 {
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}
</style>
