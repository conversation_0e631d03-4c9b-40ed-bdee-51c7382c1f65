/* 合伙人注册页面样式 */
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单容器 */
.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 表单分组 */
.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.input:focus {
  border-color: #667eea;
  background: #ffffff;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.textarea:focus {
  border-color: #667eea;
  background: #ffffff;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input .input {
  flex: 1;
  padding-right: 100rpx;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  font-size: 24rpx;
  color: #667eea;
  padding: 10rpx;
}

/* 协议同意 */
.protocol-section {
  margin: 40rpx 0;
}

.protocol-item {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 6rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
}

.checkbox.checked {
  background: #667eea;
  border-color: #667eea;
}

.checkmark {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

.protocol-text {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
}

.protocol-link {
  color: #667eea;
  text-decoration: underline;
}

/* 按钮区域 */
.button-section {
  margin-top: 50rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.submit-btn:not(.disabled):active {
  transform: scale(0.98);
  transition: transform 0.1s;
}

.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #667eea;
  padding: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .register-container {
    padding: 30rpx 20rpx;
  }
  
  .form-container {
    padding: 30rpx;
  }
  
  .title {
    font-size: 42rpx;
  }
}
