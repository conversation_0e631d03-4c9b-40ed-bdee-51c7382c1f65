const db = require('../models');
const { generateFeatureId } = require('../utils/id_helper');
const { Op } = require('sequelize');
const ProductFeature = db.ProductFeature;

/**
 * @description 创建一个新功能
 */
exports.createFeature = async (req, res) => {
  try {
    const newFeatureData = req.body;
    if (!newFeatureData.feature_id) {
      newFeatureData.feature_id = await generateFeatureId();
    } else {
      const existing = await ProductFeature.findOne({ where: { feature_id: newFeatureData.feature_id } });
      if (existing) {
        return res.status(409).json({ message: `功能ID '${newFeatureData.feature_id}' 已存在。` });
      }
    }
    const feature = await ProductFeature.create(newFeatureData);
    res.status(201).json(feature);
  } catch (error) {
    console.error('创建功能时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '创建功能失败，请检查服务器日志。', error: error.message });
  }
};

/**
 * @description 获取所有功能列表
 */
exports.getAllFeatures = async (req, res) => {
  try {
    const features = await ProductFeature.findAll();
    res.status(200).json(features);
  } catch (error) {
    console.error('获取功能列表时出错:', error);
    res.status(500).json({ message: '获取功能列表失败，请查看服务器日志。' });
  }
};

/**
 * @description 根据ID更新一个功能
 */
exports.updateFeature = async (req, res) => {
  try {
    const { id } = req.params;
    const featureData = req.body;

    if (featureData.feature_id) {
      const existing = await ProductFeature.findOne({
        where: {
          feature_id: featureData.feature_id,
          id: { [Op.ne]: id }
        }
      });
      if (existing) {
        return res.status(409).json({ message: `功能ID '${featureData.feature_id}' 已被其他功能占用。` });
      }
    }

    const featureToUpdate = await ProductFeature.findByPk(id);
    if (featureToUpdate) {
      await featureToUpdate.update(featureData);
      res.status(200).json(featureToUpdate);
    } else {
      res.status(404).json({ message: '未找到指定ID的功能' });
    }
  } catch (error) {
    console.error('更新功能时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新功能失败，请查看服务器日志。' });
  }
};

/**
 * @description 根据ID删除一个功能
 */
exports.deleteFeature = async (req, res) => {
  try {
    const { id } = req.params;
    const featureToDelete = await ProductFeature.findByPk(id);
    if (featureToDelete) {
      await featureToDelete.destroy();
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定ID的功能' });
    }
  } catch (error) {
    console.error('删除功能时出错:', error);
    res.status(500).json({ message: '删除功能失败，请查看服务器日志。' });
  }
};

/**
 * [新增] 获取下一个可用的功能ID
 */
exports.getNextFeatureId = async (req, res) => {
  try {
    const nextId = await generateFeatureId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个功能ID时出错:', error);
    res.status(500).json({ message: '生成功能ID失败', error: error.message });
  }
}; 