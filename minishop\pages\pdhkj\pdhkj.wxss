/*
 * @pdhkj.wxss
 * 重构和重新排序以匹配 pdhkj.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
page {
  --primary-color: rgb(52, 49, 237);
  --secondary-color: #3987ea;
  --background-color: #f7faff;
  --text-color: #333;
  --white: #fff;
  --shadow-color: rgba(70, 67, 211, 0.18);
  --gradient-main: linear-gradient(135deg, #6a40f3 0%, #4643d3 100%);
}

@import "/static/fonts/iconfont.wxss";

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
}

.full-width-nav {
  width: 100vw;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
  border: none;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100%;
  height: 66vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #4643d3 0%, #3987ea 50%, #6a40f3 100%);
  color: #fff;
  margin: 0;
  padding: 0;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.poster::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 85%, #ffffff 100%);
  z-index: 2;
  pointer-events: none;
}

/* --- 海报背景元素 --- */
.poster-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.geo-element {
  position: absolute;
  opacity: 0.15;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  z-index: 2;
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  top: -50rpx;
  right: -100rpx;
  background: radial-gradient(circle, rgba(57, 135, 234, 0.2) 0%, rgba(70, 67, 211, 0) 70%);
  animation: float 20s infinite ease-in-out;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  bottom: 15%;
  left: -50rpx;
  background: radial-gradient(circle, rgba(244, 117, 0, 0.2) 0%, rgba(70, 67, 211, 0) 70%);
  animation: float 15s infinite ease-in-out reverse;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  top: 30%;
  right: 10%;
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  animation: pulse 4s infinite ease-in-out;
}

.square-1 {
  width: 100rpx;
  height: 100rpx;
  bottom: 35%;
  right: 20%;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  animation: rotate 30s linear infinite;
}

.square-2 {
  width: 200rpx;
  height: 200rpx;
  top: 40%;
  left: -100rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transform: rotate(20deg);
  animation: rotate 40s linear infinite reverse;
}

.triangle-1 {
  width: 0;
  height: 0;
  border-left: 80rpx solid transparent;
  border-right: 80rpx solid transparent;
  border-bottom: 150rpx solid rgba(255, 255, 255, 0.05);
  position: absolute;
  bottom: 10%;
  right: 10%;
  animation: float 25s infinite ease-in-out;
}

.dot-grid {
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  top: 15%;
  left: 5%;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 20rpx 20rpx;
  transform: rotate(30deg);
  animation: pan 60s infinite linear;
}

.connecting-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  z-index: 1;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  height: 1px;
  width: 100%;
  transform-origin: left center;
}

.line-1 {
  top: 30%;
  transform: rotate(30deg);
  animation: glow 8s infinite ease-in-out;
}

.line-2 {
  top: 60%;
  transform: rotate(-15deg);
  animation: glow 12s infinite ease-in-out 2s;
}

.line-3 {
  top: 75%;
  transform: rotate(5deg);
  animation: glow 10s infinite ease-in-out 4s;
}

.wave-container {
  position: absolute;
  width: 100%;
  height: 200rpx;
  bottom: 0;
  left: 0;
  opacity: 0.2;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.wave1 {
  bottom: -150rpx;
  left: -50%;
  animation: wave 20s infinite linear;
}

.wave2 {
  bottom: -120rpx;
  left: -50%;
  animation: wave 15s infinite linear reverse;
}

.glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 20%, rgba(103, 232, 249, 0.15) 0%, rgba(103, 232, 249, 0) 50%),
    radial-gradient(circle at 80% 80%, rgba(6, 182, 212, 0.1) 0%, rgba(6, 182, 212, 0) 50%);
  z-index: 1;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 10rpx 2rpx rgba(255, 255, 255, 0.4);
  opacity: 0;
  animation: particle-animation 10s infinite;
}

.particle:nth-child(1) { left: 10%; top: 20%; animation-delay: 0s; }
.particle:nth-child(2) { left: 30%; top: 40%; animation-delay: 1s; }
.particle:nth-child(3) { left: 50%; top: 10%; animation-delay: 2s; }
.particle:nth-child(4) { left: 70%; top: 30%; animation-delay: 3s; }
.particle:nth-child(5) { left: 80%; top: 60%; animation-delay: 4s; }
.particle:nth-child(6) { left: 20%; top: 70%; animation-delay: 5s; }
.particle:nth-child(7) { left: 65%; top: 75%; animation-delay: 6s; }
.particle:nth-child(8) { left: 85%; top: 15%; animation-delay: 7s; }
.particle:nth-child(9) { left: 5%; top: 50%; animation-delay: 8s; }
.particle:nth-child(10) { left: 40%; top: 85%; animation-delay: 9s; }
.particle:nth-child(11) { left: 60%; top: 5%; animation-delay: 2.5s; }
.particle:nth-child(12) { left: 90%; top: 40%; animation-delay: 3.5s; }

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 100rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 0%, #7dd3fc 100%);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 20rpx rgba(0, 64, 255, 0.15);
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #38bdf8 0%, #fff 100%);
  border-radius: 3rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(56, 189, 248, 0.5);
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
}

.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  z-index: 10;
  filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.1));
}

/* 详情内容区域 */
.detail-content {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  padding: 30rpx 30rpx 0;
  margin-top: -80rpx;
  border-radius: 40rpx 40rpx 0 0;
  z-index: 20;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
}

/* ==================================
   详情内容区域
   ================================== */
/* --- 主标题区域 --- */
.main-header {
  padding: 60rpx 40rpx 40rpx;
  background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.main-header:before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(70, 67, 211, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.main-title {
  font-size: 44rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 16rpx;
  line-height: 1.3;
  position: relative;
  display: inline-block;
}

.main-subtitle {
  font-size: 30rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.divider {
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  margin: 20rpx auto 0;
  border-radius: 3rpx;
}

/* --- 产品亮点 --- */
.highlights-section {
  padding: 40rpx 30rpx;
  background-color: #fff;
  position: relative;
}

.highlights-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.highlight-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  border-radius: 16rpx;
  padding: 20rpx;
  width: calc(50% - 10rpx);
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.highlight-card:active {
  transform: translateY(-4rpx);
}

.highlight-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.highlight-icon .iconfont {
  font-size: 36rpx;
  color: #fff;
}

.highlight-info {
  flex: 1;
}

.highlight-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 4rpx;
}

.highlight-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}

/* --- 通用区块样式 --- */
.section-header.modern {
  text-align: center;
  padding: 0 40rpx 40rpx;
  position: relative;
  z-index: 2;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.section-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(70, 67, 211, 0.2);
}

.section-icon .iconfont {
  font-size: 34rpx;
  color: #fff;
}

.section-title {
  font-size: 38rpx;
  font-weight: bold;
  color: var(--primary-color);
  position: relative;
}

.section-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.section-decoration {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  margin: 16rpx auto 0;
  border-radius: 3rpx;
}

.section-header.side {
  text-align: left;
  padding: 40rpx 40rpx 20rpx;
}

.section-header.side .section-title-wrapper {
  justify-content: flex-start;
}

.section-decoration.left {
  margin: 16rpx 0 0;
}

/* --- 生态互联 --- */
.ecosystem-section {
  padding: 60rpx 0 40rpx;
  background: linear-gradient(135deg, #f5f9ff 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.ecosystem-section::before {
  content: '';
  position: absolute;
  right: -150rpx;
  top: -150rpx;
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(70, 67, 211, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 1;
}

.accordion-container {
  display: flex;
  padding: 20rpx 30rpx;
  overflow-x: hidden;
  position: relative;
  min-height: 360rpx;
  z-index: 2;
}

.accordion-item {
  position: relative;
  height: 470rpx;
  border-radius: 20rpx;
  margin-right: 15rpx;
  box-shadow: 0 8rpx 24rpx rgba(70, 67, 211, 0.12);
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1);
  flex: 0 0 80rpx;
  overflow: hidden;
  background-color: #f7faff;
  border: none;
}

.accordion-item.expanded {
  flex: 1;
  background: linear-gradient(180deg, #e6f0ff 0%, #f0f7ff 100%);
  box-shadow: 0 12rpx 30rpx rgba(70, 67, 211, 0.18);
}

.accordion-header {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 15rpx;
}

.accordion-item:not(.expanded) .accordion-header {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  height: 100%;
  width: 80rpx;
  justify-content: center;
  background: linear-gradient(180deg, rgba(70, 67, 211, 0.05) 0%, rgba(70, 67, 211, 0.01) 100%);
}

.accordion-icon {
  width: 70rpx;
  height: 70rpx;
  margin-right: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(70, 67, 211, 0.08);
  transition: all 0.3s ease;
}

.accordion-item.expanded .accordion-icon {
  background-color: rgba(70, 67, 211, 0.15);
  transform: scale(1.1);
}

.accordion-icon.icon-user:before,
.accordion-icon.icon-gongwenbao:before,
.accordion-icon.icon-cangchucangku:before,
.accordion-icon.icon-renminbi:before,
.accordion-icon.icon-shuzihua:before {
  font-family: "iconfont";
  font-size: 42rpx;
  color: var(--primary-color);
}

.accordion-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--primary-color);
  letter-spacing: 1rpx;
}

.accordion-item:not(.expanded) .accordion-icon {
  margin: 15rpx 0;
  background-color: transparent;
}

.accordion-content {
  padding: 20rpx 25rpx;
  overflow-y: auto;
  max-height: calc(100% - 90rpx);
  position: relative;
}

.accordion-content:before {
  content: '';
  position: absolute;
  top: 0;
  left: 25rpx;
  right: 25rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(70, 67, 211, 0.2), transparent);
}

.accordion-detail-item {
  font-size: 26rpx;
  color: #444;
  margin-bottom: 16rpx;
  line-height: 1.6;
  position: relative;
  padding-left: 24rpx;
}

.accordion-detail-item:before {
  content: '';
  position: absolute;
  left: 0;
  top: 14rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.ecosystem-description {
  padding: 40rpx 30rpx;
  background-color: rgba(255, 255, 255, 0.7);
  margin: 30rpx 30rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 2;
}

.eco-description-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 16rpx;
  text-align: center;
}

.eco-description-content {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  text-align: justify;
}

/* --- 功能特性区域 --- */
.feature-section.modern {
  padding: 50rpx 0 60rpx;
  position: relative;
  background-color: #fff;
  margin-bottom: 0;
  overflow: hidden;
}

.feature-section.modern.alt {
  background-color: #f8fbff;
}

.feature-card.vertical {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.feature-image.full-width {
  position: relative;
  max-width: 100%;
  width: 100%;
}

.feature-image.full-width image {
  width: 100%;
  height: auto;
  max-height: none;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.feature-image.full-width image:active {
  transform: scale(0.98);
}

.feature-badge {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: #fff;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.feature-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  flex: 1;
}

.feature-section.modern.alt .feature-content {
  background-color: #f8fbff;
}

.feature-detail {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-point {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.point-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.point-icon .iconfont {
  font-size: 29rpx;
  color: var(--primary-color);
}

.point-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}

/* --- 特色功能滑块 --- */
.feature-highlights {
  padding: 60rpx 30rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, #3987ea 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.feature-highlights .section-header.modern .section-title,
.feature-highlights .section-subtitle {
  color: #fff;
}

.feature-highlights .section-icon {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.feature-highlights .section-decoration {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
}

.feature-highlights-slider {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
  padding: 20rpx 0;
  -webkit-overflow-scrolling: touch;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
}

.highlight-slide {
  flex: 0 0 auto;
  width: 280rpx;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease;
}

.highlight-slide:active {
  transform: translateY(-6rpx);
}

.highlight-slide-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.highlight-slide-icon .iconfont {
  font-size: 40rpx;
  color: #fff;
}

.highlight-slide-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
  text-align: center;
}

.highlight-slide-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  text-align: center;
}

/* --- 一体化服务 --- */
.integrated-service.modern {
  padding: 60rpx 0;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  position: relative;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0;
}

.service-item {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.service-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.service-item-inner {
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-icon-font {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f5ff, #f5f9ff);
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 55rpx;
  color: var(--primary-color);
}

.service-content {
  text-align: center;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* --- 适用行业 --- */
.industries-section {
  padding: 60rpx 30rpx;
  background-color: #fff;
  position: relative;
}

.industries-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 30rpx;
}

.industry-card {
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.industry-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.industry-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.industry-icon .iconfont {
  font-size: 36rpx;
  color: #fff;
}

.industry-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.industry-tip {
  margin-top: 30rpx;
  text-align: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #eef6ff 0%, #f0f8ff 100%);
  border-radius: 16rpx;
  color: var(--text-color);
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.5;
  box-shadow: 0 4rpx 16rpx rgba(70, 67, 211, 0.12);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.industry-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  border-radius: 3rpx 0 0 3rpx;
}

.industry-tip text {
  position: relative;
  z-index: 1;
}

/* --- 服务保障 --- */
.service-guarantee-section.modern {
  padding: 60rpx 0;
  background: linear-gradient(135deg, #f5f9ff 0%, #ffffff 100%);
  position: relative;
  margin-top: 0;
}

.service-guarantee-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0;
  position: relative;
  z-index: 1;
}

.service-guarantee-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-guarantee-card:active {
  transform: translateY(-6rpx);
}

.service-guarantee-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.service-guarantee-icon {
  font-size: 44rpx;
  color: var(--primary-color);
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f5ff 0%, #f8fbff 100%);
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(70, 67, 211, 0.1);
}

.service-guarantee-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
  text-align: center;
}

.service-guarantee-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  text-align: center;
}

/* --- 咨询区域 --- */
.cta-section.modern {
  margin: 40rpx 0 100rpx;
  padding: 50rpx 30rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, #3987ea 100%);
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(70, 67, 211, 0.25);
  position: relative;
  overflow: hidden;
}

.cta-section.modern:before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.cta-section.modern:after {
  content: '';
  position: absolute;
  bottom: -100rpx;
  left: -100rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.cta-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.cta-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}

.cta-desc {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  text-align: center;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 30rpx;
  width: 100%;
  justify-content: center;
}

.cta-btn {
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-btn:active {
  transform: scale(0.97);
}

.cta-btn.outline {
  background-color: transparent;
  border: 2rpx solid #fff;
  color: #fff;
}

.cta-btn.primary {
  background-color: var(--white);
  color: var(--primary-color);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.btn-text {
  z-index: 2;
}

/* ==================================
   媒体查询
   ================================== */
@media screen and (min-width: 768px) {
  .highlights-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .service-guarantee-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .industries-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* ==================================
   动画
   ================================== */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.15; }
  50% { transform: scale(1.05); opacity: 0.25; }
  100% { transform: scale(1); opacity: 0.15; }
}

@keyframes pan {
  0% { transform: rotate(30deg) translate(0, 0); }
  100% { transform: rotate(30deg) translate(-20rpx, -20rpx); }
}

@keyframes glow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.5; }
}

@keyframes wave {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes particle-animation {
  0% {
    transform: translate(0, 0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translate(var(--tx, 30px), var(--ty, -50px));
    opacity: 0;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

