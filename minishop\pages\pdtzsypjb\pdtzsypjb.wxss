/*
 * @pdtzsypjb.wxss
 * 重构和重新排序以匹配 pdtzsypjb.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
@import "/static/fonts/iconfont.wxss";

/* 定义主题颜色变量 - 明亮科技蓝色系 */
page {
  --primary-color: #0e74e9;       /* 主色调：明亮科技蓝 */
  --primary-light: #3885f8;       /* 浅色调 */
  --primary-dark: #0257c7;        /* 深色调 */
  --accent-color: #7da5fc;        /* 强调色 */
  --text-on-primary: #ffffff;     /* 主色上的文字 */
  --text-primary: #1e293b;        /* 主要文字 */
  --text-secondary: #64748b;      /* 次要文字 */
  --background-light: #f8fafc;    /* 浅色背景 */
  --card-bg: #ffffff;             /* 卡片背景 */
  --success-color: #22c55e;       /* 成功色 */
  --warning-color: #eab308;       /* 警告色 */
  --info-color: #0e5be9;          /* 信息色 */
  --shadow-sm: 0 4rpx 6rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10rpx 25rpx rgba(0, 0, 0, 0.12);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: var(--background-light);
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
}

/* ==================================
   滚动容器
   ================================== */
.scroll-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  -webkit-overflow-scrolling: touch;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100%;
  height: 66vh;
  position: relative;
  overflow: hidden;
  color: #fff;
  margin: 0;
  padding: 0;
  border: none;
}

/* --- 背景动效 --- */
.poster-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
  z-index: 1;
}

.geometric-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.geo-circle {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  z-index: 1;
}
.geo-small { width: 150rpx; height: 150rpx; top: 15%; right: 10%; animation: float 20s infinite ease-in-out; }
.geo-medium { width: 250rpx; height: 250rpx; top: 60%; left: 5%; animation: float 25s infinite ease-in-out reverse; }
.geo-large { width: 350rpx; height: 350rpx; bottom: -100rpx; right: -50rpx; animation: float 30s infinite ease-in-out; }

.geo-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
}
.line-1 { width: 40%; top: 30%; left: 10%; transform: rotate(-15deg); }
.line-2 { width: 30%; bottom: 40%; right: 20%; transform: rotate(20deg); }

.center-glow {
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  opacity: 0.8;
  animation: pulse 4s infinite ease-in-out;
}

.floating-dots {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.dot {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  filter: blur(3px);
}
.dot1 { width: 20rpx; height: 20rpx; top: 25%; left: 30%; animation: floatDot 15s infinite ease-in-out; }
.dot2 { width: 15rpx; height: 15rpx; top: 45%; left: 70%; animation: floatDot 18s infinite ease-in-out reverse; }
.dot3 { width: 25rpx; height: 25rpx; top: 65%; left: 20%; animation: floatDot 20s infinite ease-in-out; }
.dot4 { width: 18rpx; height: 18rpx; top: 15%; left: 85%; animation: floatDot 12s infinite ease-in-out reverse; }

.hologram-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 1;
}

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 80rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 20%, #7dd3fc 80%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 6rpx 24rpx rgba(0, 64, 255, 0.18);
  margin-bottom: 18rpx;
  animation: fadeInUp 1s;
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  animation: fadeInUp 1.2s;
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #38bdf8 0%, #fff 100%);
  border-radius: 4rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(56, 189, 248, 0.5);
  animation: fadeIn 1.5s;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
  animation: fadeInUp 1.4s;
}

/* --- 促销卡片 --- */
.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0 40rpx;
  animation: fadeInUp 0.8s ease-out;
  filter: drop-shadow(0 6rpx 16rpx rgba(0, 0, 0, 0.1));
}

/* --- 底部羽化 --- */
.poster-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 85%, #ffffff 100%);
  z-index: 5;
  pointer-events: none;
}

/* ==================================
   详情内容区域
   ================================== */
.detail-content {
  width: 100%;
  background-color: #ffffff;
  z-index: 10;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  padding: 30rpx 30rpx 0;
  margin-top: -80rpx;
  position: relative;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
  -webkit-overflow-scrolling: touch;
  border: none;
}

/* --- 通用区块头 --- */
.section-header {
  text-align: center;
  margin: 60rpx 0 40rpx;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 42rpx;
  font-weight: 700;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
  padding-bottom: 20rpx;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-dark), var(--primary-light));
  border-radius: 3rpx;
}

.section-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-top: 16rpx;
  opacity: 0.85;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

/* --- 智能财税痛点 --- */
.pain-points-section {
  background-color: #f7faff;
  padding: 20rpx 30rpx;
  padding-bottom: 40rpx;
}

.pain-points-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 40rpx;
}

.pain-point-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1rpx solid #e5e7eb;
  width: 100%;
  box-sizing: border-box;
}

.pain-point-card:active {
  transform: translateY(-5rpx);
  box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.08);
}

.pain-point-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 24rpx;
  font-size: 40rpx;
}

.pain-point-content {
  display: flex;
  flex-direction: column;
}

.pain-point-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6rpx;
}

.pain-point-subtitle {
  font-size: 28rpx;
  color: #4b5563;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.pain-point-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* --- AI财税全应用场景 --- */
.ai-finance-section {
  padding: 60rpx 30rpx;
  margin: 20rpx 0 60rpx;
  background: linear-gradient(135deg, #0e74e9, #0257c7);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  color: var(--text-on-primary);
}

.ai-finance-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="1" fill="%23ffffff" fill-opacity="0.3"/></svg>');
  background-size: 20px 20px;
  opacity: 0.3;
}

.ai-finance-section .section-title {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ai-finance-section .section-title:after {
  background: linear-gradient(90deg, rgba(255,255,255,0.7), rgba(255,255,255,0.3));
}

.ai-finance-section .section-subtitle {
  color: rgba(255, 255, 255, 0.85);
}

.ai-features-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  margin-top: 40rpx;
  padding: 0 20rpx;
  position: relative;
  z-index: 1;
}

.ai-feature-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-md);
  padding: 40rpx 24rpx;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-feature-item:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.2);
}

.ai-feature-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
}

.ai-feature-icon .iconfont {
  font-size: 50rpx;
  color: white;
}

.ai-feature-title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-align: center;
}

.ai-feature-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.5;
  text-align: center;
}

.ai-platform-slogan {
  margin-top: 60rpx;
  text-align: center;
  position: relative;
  z-index: 1;
}

.slogan-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  padding: 20rpx 40rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  display: inline-block;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* --- 性价比出众配置 --- */
.value-config-section {
  padding: 60rpx 30rpx;
  margin: 20rpx 0 60rpx;
  border-radius: var(--radius-lg);
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.value-config-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(14, 116, 233, 0.1) 0%, rgba(14, 116, 233, 0) 70%);
  border-radius: 50%;
}

.value-config-section::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -30%;
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, rgba(14, 116, 233, 0.08) 0%, rgba(14, 116, 233, 0) 70%);
  border-radius: 50%;
}

.value-config-container {
  padding: 0;
  position: relative;
  z-index: 1;
}

.central-circle-container {
  position: relative;
  width: 100%;
  margin: 40rpx 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 350rpx;
  overflow: visible;
}

.central-circle {
  width: 500rpx;
  height: 500rpx;
  border-radius: 50%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6);
}

.circle-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  box-shadow: 
    0 0 60rpx rgba(255, 255, 255, 0.8),
    0 0 120rpx rgba(14, 116, 233, 0.4),
    inset 0 0 60rpx rgba(14, 116, 233, 0.3);
  opacity: 0.8;
  z-index: 1;
}

.circle-border {
  position: absolute;
  width: 90%;
  height: 90%;
  border-radius: 50%;
  border: 2rpx dashed rgba(14, 116, 233, 0.4);
  z-index: 2;
  animation: rotate 60s linear infinite;
}

.circle-content {
  position: relative;
  z-index: 3;
  width: 80%;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: top;
  align-items: center;
  padding-bottom: 60rpx;
}

.circle-title {
  font-size: 42rpx;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 20rpx;
  margin-top: 30rpx;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: -240rpx;
  position: relative;
  z-index: 5;
  padding: 0 10rpx;
}

.config-item {
  background: var(--card-bg);
  border-radius: var(--radius-md);
  padding: 30rpx 15rpx;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: var(--transition-normal);
  position: relative;
  border: 1px solid rgba(14, 116, 233, 0.08);
  z-index: 2;
}

.config-item:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-md);
}

.config-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: rgba(14, 116, 233, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.config-icon .iconfont {
  font-size: 46rpx;
  color: var(--info-color);
}

.config-icon.highlighted {
  background: rgba(14, 116, 233, 0.2);
}

.config-icon.highlighted .iconfont {
  color: var(--primary-dark);
}

.config-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 10rpx;
  text-align: center;
}

.config-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  text-align: center;
  min-height: 68rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  padding: 0 8rpx;
}

.config-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: var(--info-color);
  color: var(--text-on-primary);
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-weight: 600;
}

.config-tag.hot { background: var(--primary-dark); }
.config-tag.recommend { background: var(--success-color); }

.config-features-footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 40rpx;
  padding: 20rpx;
  background: linear-gradient(to right, rgba(14, 116, 233, 0.05), rgba(14, 116, 233, 0.1), rgba(14, 116, 233, 0.05));
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.config-features-footer .feature-item {
  padding: 20rpx 30rpx;
  margin: 10rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(14, 116, 233, 0.1);
}

.config-features-footer .feature-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-dark);
  text-align: center;
}

/* --- 数据安全与提效 --- */
.data-security-section {
  padding: 60rpx 30rpx;
  margin: 20rpx 0 0;
  background: linear-gradient(135deg, #1a365d, #2d3748);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  position: relative;
  overflow: hidden;
  color: white;
  z-index: 5;
}

.data-security-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0 20 L40 20 M20 0 L20 40" stroke="%23ffffff" stroke-width="0.5" stroke-opacity="0.1"/></svg>');
  background-size: 40px 40px;
  opacity: 0.3;
}

.data-security-section .section-title {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.data-security-section .section-title:after {
  background: linear-gradient(90deg, rgba(255,255,255,0.7), rgba(255,255,255,0.3));
}

.data-security-section .section-subtitle {
  color: rgba(255, 255, 255, 0.85);
}

.security-subtitle {
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.subtitle-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.security-shield-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40rpx 20rpx;
  position: relative;
  z-index: 1;
}

.security-features-list {
  width: 100%; /*
    调整为100%，因为原wxml中只有一个子元素
  */
}

.security-feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-md);
  padding: 24rpx;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition-normal);
}

.security-feature-item:active {
  transform: translateX(4rpx);
  background: rgba(255, 255, 255, 0.15);
}

.security-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.security-icon .iconfont {
  font-size: 40rpx;
  color: white;
}

.security-feature-content {
  flex: 1;
}

.security-feature-title {
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.security-feature-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* --- 了解更多 --- */
.more-section {
  padding: 0;
  text-align: center;
  margin: 15rpx 10rpx 15rpx 10rpx;
  position: relative;
  overflow: hidden;
  background: var(--primary-color);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  transform: translateY(0);
  box-shadow: var(--shadow-lg);
  z-index: 4;
  border-radius: 20rpx;
}

.more-content-wrapper {
  position: relative;
  z-index: 5;
  padding: 30rpx;
}

.more-content {
  padding: 40rpx 40rpx 50rpx;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.more-title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 40rpx;
}

.more-actions {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.more-actions .action-btn {
  max-width: 400rpx;
  height: 90rpx;
  border-radius: 45rpx;
  background: linear-gradient(135deg, #388bf8, #0e6de9);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
  width: 100%;
}


.more-actions .btn-inner {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  letter-spacing: 2rpx;
  position: relative;
  z-index: 2;
}

/* --- 底部留白 --- */
.bottom-space {
  height: 100rpx;
  background-color: var(--background-light);
  position: relative;
  z-index: 1;
}

/* ==================================
   动画
   ================================== */
@keyframes floatDot {
  0%, 100% { transform: translateY(0) translateX(0); opacity: 0.4; }
  25% { transform: translateY(-30rpx) translateX(20rpx); opacity: 0.6; }
  50% { transform: translateY(0) translateX(40rpx); opacity: 0.4; }
  75% { transform: translateY(30rpx) translateX(20rpx); opacity: 0.6; }
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
  50% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.5; }
}

@keyframes float {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(20rpx, -30rpx); }
  50% { transform: translate(-20rpx, 20rpx); }
  75% { transform: translate(30rpx, 10rpx); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(40rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} 