const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 服务订单表体模型 (order_service_item)
 * 存储服务订单的详细信息，支持实施服务、售后服务、sps服务
 */
const OrderServiceItem = sequelize.define('OrderServiceItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键'
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联order_head.id'
  },
  service_name: {
    type: DataTypes.ENUM('实施服务', '售后服务', 'sps服务'),
    allowNull: false,
    comment: '服务名称'
  },
  standard_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '标准价格'
  },
  actual_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '实际价格'
  },
  asset_price_field: {
    type: DataTypes.ENUM('implementation_fee', 'after_sales_service_fee', 'sps_annual_fee'),
    allowNull: true,
    comment: '对应资产表中的价格字段（不包括product_standard_price，因为产品价格在产品订单表体中处理）'
  },
  related_order_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '关联产品订单号'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'order_service_item',
  timestamps: false, // 服务订单明细不需要时间戳
  underscored: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  validate: {
    // 验证服务名称与价格字段的对应关系
    servicePriceMapping() {
      const mappings = {
        '实施服务': 'implementation_fee',
        '售后服务': 'after_sales_service_fee',
        'sps服务': 'sps_annual_fee'
      };

      if (this.asset_price_field && mappings[this.service_name] !== this.asset_price_field) {
        throw new Error(`服务名称 "${this.service_name}" 与价格字段 "${this.asset_price_field}" 不匹配`);
      }
    }
  }
});

module.exports = OrderServiceItem;