import service from '@/utils/request_extra.js';

const API_PATH = '/features';

/**
 * [新增] 获取下一个可用的功能ID
 */
export const getNextFeatureId = () => {
  return service.get(`${API_PATH}/next-id`);
}

/**
 * 获取所有产品功能列表
 */
export const getFeatures = () => {
  return service.get(API_PATH);
};

/**
 * 创建一个新功能
 */
export const createFeature = (featureData) => {
  return service.post(API_PATH, featureData);
};

/**
 * 更新一个功能
 */
export const updateFeature = (id, featureData) => {
  return service.put(`${API_PATH}/${id}`, featureData);
};

/**
 * 删除一个功能
 */
export const deleteFeature = (id) => {
  return service.delete(`${API_PATH}/${id}`);
}; 