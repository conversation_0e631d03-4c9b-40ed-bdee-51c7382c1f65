/*
 * @pdydz.wxss
 * 重构和重新排序以匹配 pdydz.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
@import '/static/fonts/iconfont.wxss';

page {
  --primary-color: #1890ff;
  --secondary-color: #40a9ff;
  --accent-color: #0070f3;
  --dark-blue: #0050b3;
  --deeper-blue: #003a8c;
  --darkest-blue: #002766;
  --highlight-red: #f5222d;
  --white: #fff;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-light: #bfbfbf;
  --background-light: #f0f2f5;
  --shadow-color: rgba(0, 0, 0, 0.08);
  --gradient-primary: linear-gradient(135deg, #1890ff 0%, #096dd9 50%, #0050b3 100%);
  --gradient-blue: linear-gradient(135deg, #40a9ff, #096dd9);

  /* background-color, margin, padding 应该由 .container 控制，此处移除 */
}

/* 全局重置 */
view, image, text, button, scroll-view {
  border: none;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: transparent; /* 明确设置为透明，这是关键 */
  box-sizing: border-box; /* 明确添加，与 pdhkj.wxss 保持一致 */
  margin: 0; /* 明确添加，与 pdhkj.wxss 保持一致 */
  padding: 0; /* 明确添加，与 pdhkj.wxss 保持一致 */
  border: none; /* 明确添加，与 pdhkj.wxss 保持一致 */
}

.full-width-nav {
  width: 100% !important;
  left: 0;
  right: 0;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100%;
  height: 66vh;
  position: relative;
  overflow: hidden;
  /* 移除 background: transparent，让 WXML 中的内联样式生效 */
  color: var(--white);
}

/* --- 背景动效 --- */
.poster-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
  z-index: 1;
}

.bg-element {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -100rpx;
  left: -50rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 100rpx;
  right: -50rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
}

.square-1 {
  width: 180rpx;
  height: 180rpx;
  top: 25%;
  right: 15%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  transform: rotate(30deg);
}

.triangle-1 {
  width: 0;
  height: 0;
  border-left: 100rpx solid transparent;
  border-right: 100rpx solid transparent;
  border-bottom: 180rpx solid rgba(255, 255, 255, 0.08);
  background: transparent;
  border-radius: 0;
  top: 60%;
  left: 20%;
  transform: rotate(-15deg);
}

.dot-pattern-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  background-image: radial-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px);
  background-size: 15rpx 15rpx;
  border-radius: 0;
}

.dot-pattern-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 15%;
  left: 15%;
  background-image: radial-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 10rpx 10rpx;
  border-radius: 0;
}

.line-pattern-1 {
  width: 150rpx;
  height: 3rpx;
  top: 30%;
  left: 5%;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  border-radius: 0;
}

.line-pattern-2 {
  width: 200rpx;
  height: 3rpx;
  bottom: 25%;
  right: 10%;
  background: rgba(255, 255, 255, 0.15);
  transform: rotate(-30deg);
  border-radius: 0;
}

.glow-1 {
  width: 250rpx;
  height: 250rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 4s infinite ease-in-out;
}

/* --- 粒子效果 --- */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
}

.p1 { top: 10%; left: 20%; animation: float 15s infinite ease-in-out; }
.p2 { top: 20%; left: 80%; animation: float 18s infinite ease-in-out -2s; }
.p3 { top: 40%; left: 10%; animation: float 20s infinite ease-in-out -5s; }
.p4 { top: 60%; left: 30%; animation: float 22s infinite ease-in-out -8s; }
.p5 { top: 80%; left: 60%; animation: float 25s infinite ease-in-out -12s; }
.p6 { top: 30%; left: 50%; animation: float 17s infinite ease-in-out -3s; }
.p7 { top: 70%; left: 90%; animation: float 19s infinite ease-in-out -7s; }
.p8 { top: 50%; left: 70%; animation: float 21s infinite ease-in-out -10s; }

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 100rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 0%, #fff8e0);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.5);
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.7) 100%);
  border-radius: 3rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.3);
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
}

/* --- 促销卡片 --- */
.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0 40rpx;
  animation: fadeInUp 0.8s ease-out;
  filter: drop-shadow(0 6rpx 16rpx rgba(0, 0, 0, 0.1));
}

/* --- 底部羽化 --- */
.poster-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 85%, #ffffff 100%);
  z-index: 5;
  pointer-events: none;
}

/* ==================================
   详情内容区域
   ================================== */
.detail-content {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-color: #f7faff;
  padding: 30rpx;
  margin-top: -80rpx;
  z-index: 20;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
}

/* --- 通用区块头 --- */
.section-header {
  text-align: center;
  margin-bottom: 50rpx;
  position: relative;
}

.section-title-main {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 10rpx;
  position: relative;
  display: inline-block;
}

.section-title-sub {
  font-size: 28rpx;
  color: var(--text-light);
}

.title-decoration {
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 20rpx auto 0;
  border-radius: 3rpx;
}

.section-header.light .section-title-main {
  color: var(--white);
}

.section-header.light .section-title-sub {
  color: rgba(255,255,255,0.8);
}

.section-header.light .title-decoration {
  background: linear-gradient(to right, var(--white), rgba(255,255,255,0.7));
}

/* --- 核心价值 --- */
.value-proposition {
  /* 这是一个包装元素，无特定样式 */
}

.value-banner {
  padding: 60rpx 30rpx;
  background: var(--gradient-blue);
  position: relative;
  overflow: hidden;
  color: var(--white);
  text-align: center;
  border-radius: 40rpx;
}

.rotating-text-container {
  height: auto;
  overflow: hidden;
  margin-bottom: 20rpx;
  position: relative;
  padding: 10rpx 0;
}

.rotating-text {
  display: block;
}

.rotating-text text {
  display: block;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 46rpx;
  font-weight: bold;
  text-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.value-subtitle {
  font-size: 30rpx;
  opacity: 0.9;
  margin-bottom: 40rpx;
}

.animated-numbers {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 0;
}

.number-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.number-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.number {
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1;
  background: linear-gradient(to top, var(--white), #c4d8ff);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
  display: inline-block;
}

.unit {
  font-size: 30rpx;
  color: var(--white);
  opacity: 0.9;
  margin-left: 4rpx;
  align-self: flex-start;
  padding-top: 8rpx;
}

.label {
  font-size: 26rpx;
  margin-top: 10rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* --- 痛点与解决方案 --- */
.problem-solution {
  padding: 60rpx 0;
  background-color: var(--white);
}

/* 痛点卡片 */
.pain-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.pain-card {
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateY(0);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.pain-card:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.08);
}

.pain-image {
  width: 100%;
  height: 260rpx;
  overflow: hidden;
  position: relative;
}

.pain-image:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
  z-index: 1;
}

.pain-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.pain-card:active .pain-image image {
  transform: scale(1.05);
}

.pain-content {
  padding: 34rpx;
  position: relative;
}

.pain-content:before {
  content: '';
  position: absolute;
  left: 0;
  top: 34rpx;
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, #0078ff, #52a9ff);
  border-radius: 3rpx;
}

.pain-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 20rpx;
}

.pain-desc {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.pain-desc text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.pain-desc text:before {
  content: '';
  position: absolute;
  left: 0;
  top: 14rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #0078ff;
}

/* 解决方案卡片 */
.solution-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin: 40rpx 0;
}

.solution-card {
  background-color: var(--white);
  border-radius: 20rpx;
  padding: 0;
  box-shadow: 0 8rpx 24rpx var(--shadow-color);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.05);
}

.solution-header {
  background: linear-gradient(135deg, var(--primary-color), var(--deeper-blue));
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.solution-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.solution-icon .iconfont {
  font-size: 40rpx;
  color: var(--white);
}

.solution-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--white);
  flex: 1;
}

.solution-content {
  padding: 30rpx;
}

.solution-image {
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.feature-details {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 15rpx;
  line-height: 1.6;
  position: relative;
  padding-left: 30rpx;
}

.feature-details::before {
  content: '';
  position: absolute;
  left: 10rpx;
  top: 15rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* --- 核心功能 --- */
.feature-showcase {
  padding: 60rpx 30rpx;
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
}

.feature-showcase::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0.05;
  z-index: 0;
}

.feature-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  perspective: 1000px;
  position: relative;
  z-index: 1;
}

.feature-card {
  width: 48%;
  height: 300rpx;
  margin-bottom: 30rpx;
  perspective: 1000px;
}

.feature-card-inner {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.8s;
  border-radius: 15rpx;
  box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.1);
}

.feature-card-inner.flipped {
  transform: rotateY(180deg);
}

.feature-front, .feature-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  box-sizing: border-box;
}

.feature-front {
  background-color: rgba(255,255,255,0.1);
  border: 1rpx solid rgba(255,255,255,0.2);
}

.feature-back {
  background-color: var(--white);
  transform: rotateY(180deg);
}

.feature-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon .iconfont {
  font-size: 50rpx;
  color: var(--white);
}

.feature-name {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--white);
  text-align: center;
}

.feature-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.feature-detail text {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-bottom: 10rpx;
  position: relative;
  padding-left: 20rpx;
}

.feature-detail text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: var(--dark-blue);
  border-radius: 50%;
}

/* --- 智能化流程 --- */
.workflow-section {
  padding: 60rpx 30rpx;
  background: var(--gradient-blue);
  position: relative;
  overflow: hidden;
}

.workflow-display {
  margin-top: 40rpx;
  position: relative;
  z-index: 1;
}

.workflow-timeline {
  margin-bottom: 40rpx;
}

.timeline-scroll {
  width: 100%;
  white-space: nowrap;
  position: relative;
}

.timeline-horizontal {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 30rpx;
  width: auto;
}

.timeline-node-h {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.7;
  transition: all 0.5s ease;
  padding: 10rpx;
  width: 160rpx;
  flex-shrink: 0;
  position: relative;
}

.timeline-node-h.active {
  opacity: 1;
  transform: translateY(-5rpx);
}

.node-dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: var(--white);
  /* margin-right is not needed for horizontal alignment */
  position: relative;
  flex-shrink: 0;
  transition: all 0.5s ease;
}

.node-dot::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: var(--highlight-red);
  top: 5rpx;
  left: 5rpx;
  transform: scale(0);
  transition: transform 0.5s ease;
}

.timeline-node-h.active .node-dot {
  box-shadow: 0 0 10rpx rgba(255,255,255,0.7);
}

.timeline-node-h.active .node-dot::after {
  transform: scale(1);
  animation: pulse-dot 2s infinite;
}

.node-content-h {
  text-align: center;
  margin-top: 15rpx;
  width: 100%;
}

.timeline-node-h .node-title {
  font-size: 26rpx;
  white-space: normal;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.3s ease;
  color: var(--white);
  margin-bottom: 5rpx;
}

.timeline-node-h .node-desc {
  font-size: 22rpx;
  white-space: normal;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.timeline-node-h.active .node-title {
  font-size: 28rpx;
  text-shadow: 0 0 10rpx rgba(255,255,255,0.5);
  font-weight: bold;
}

.timeline-node-h.active .node-desc {
  color: rgba(255,255,255,0.9);
}

.timeline-line-h {
  height: 3rpx;
  width: 40rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 5rpx;
  flex-shrink: 0;
}

.workflow-detail {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.workflow-image {
  width: 100%;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.workflow-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--white);
  line-height: 1;
}

.stat-unit {
  font-size: 28rpx;
  margin-left: 2rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10rpx;
}

/* --- 产品优势 --- */
.advantage-section {
  padding: 60rpx 10rpx;
  background-color: var(--white);
  position: relative;
  overflow: hidden;
}

.advantage-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(236, 246, 255, 0.8), rgba(245, 250, 255, 0.8));
  opacity: 1;
  z-index: 0;
}

.advantage-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 30rpx;
  position: relative;
  z-index: 1;
}

.advantage-card {
  background-color: var(--white);
  border-radius: 20rpx;
  padding: 0;
  box-shadow: 0 8rpx 24rpx var(--shadow-color);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
}

.advantage-card-header {
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(236, 246, 255, 1), rgba(245, 250, 255, 1));
}

.advantage-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.advantage-icon .iconfont {
  font-size: 40rpx;
  color: var(--white);
}

.advantage-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  text-align: center;
}

.advantage-divider {
  height: 2rpx;
  background: linear-gradient(to right, transparent, var(--primary-color), transparent);
  width: 80%;
  margin: 0 auto;
}

.advantage-list {
  padding: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.advantage-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.check-icon {
  width: 30rpx;
  height: 30rpx;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  margin-right: 10rpx;
  position: relative;
  flex-shrink: 0;
}

.check-icon::before {
  content: '';
  position: absolute;
  width: 14rpx;
  height: 7rpx;
  border-left: 3rpx solid var(--primary-color);
  border-bottom: 3rpx solid var(--primary-color);
  transform: rotate(-45deg);
  top: 10rpx;
  left: 7rpx;
}

.advantage-item text {
  font-size: 26rpx;
  color: var(--text-secondary);
  flex: 1;
}

/* --- 咨询/行动号召(CTA) --- */
.cta-section {
  margin: 10rpx 10rpx;
  margin-bottom: 100rpx;
  padding: 50rpx 30rpx;
  background: var(--gradient-blue);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 102, 204, 0.3);
}

.cta-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--white);
}

.cta-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}

.cta-desc {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  text-align: center;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 30rpx;
  width: 100%;
  justify-content: center;
}

.cta-btn {
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}

.cta-btn:active {
  transform: scale(0.97) translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(24, 144, 255, 0.3);
}

.cta-btn.outline {
  background-color: transparent;
  border: 2rpx solid var(--white);
  color: var(--white);
}

.cta-btn.primary {
  background-color: var(--white);
  color: #0052cc;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.btn-text {
  z-index: 2;
}

/* ==================================
   动画
   ================================== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(40rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes float {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(20rpx, -30rpx); }
  50% { transform: translate(-20rpx, 20rpx); }
  75% { transform: translate(30rpx, 10rpx); }
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
  50% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.3; }
}

@keyframes pulse-dot {
  0% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
  70% { box-shadow: 0 0 0 10rpx rgba(255, 71, 87, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0); }
}

/* ==================================
   媒体查询
   ================================== */
@media screen and (min-width: 768px) {
  .advantage-cards {
    grid-template-columns: repeat(4, 1fr);
  }
}


