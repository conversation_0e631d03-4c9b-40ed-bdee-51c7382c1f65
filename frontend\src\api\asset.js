// /frontend/src/api/asset.js
import service from '@/utils/request_extra.js';

const API_PATH = '/assets';

/**
 * [修改] 获取下一个可用的资产ID
 */
export function getNextAssetId() {
  return service({
    url: `${API_PATH}/next-id`,
    method: 'get',
  });
}

/**
 * [新增] 获取下一个可用的资产变更ID
 */
export function getNextAssetChangeId() {
  return service({
    url: `${API_PATH}/changes/next-id`,
    method: 'get',
  });
}

/**
 * 获取所有资产列表（支持分页和搜索）
 */
export function getAssets(params = {}) {
  return service({
    url: API_PATH,
    method: 'get',
    params
  });
};

/**
 * 根据ID获取单个资产的详细信息 (包括变更记录)
 */
export function getAssetById(id) {
  return service({
    url: `${API_PATH}/${id}`,
    method: 'get',
  });
};

/**
 * 创建一个新资产
 */
export function createAsset(data) {
  // 分离资产数据和关联订单ID
  const { order_ids, ...assetData } = data;

  return service({
    url: API_PATH,
    method: 'post',
    // [重构] 遵守后端统一的数据结构
    data: {
      asset_data: assetData,
      related_order_ids: order_ids || []
    },
  });
};

/**
 * "修改"资产 (普通更新，不记录log)
 */
export function updateAsset(id, assetData) {
  return service({
    url: `${API_PATH}/${id}`,
    method: 'put',
    data: assetData,
  });
};

/**
 * 根据资产ID获取其所有变更记录（支持分页）
 * @param {string | number} assetId - 资产的主键ID
 * @param {object} params - 分页和搜索参数
 */
export function getAssetChangeRecords(assetId, params = {}) {
    return service({
      url: `${API_PATH}/${assetId}/changes`,
      method: 'get',
      params
    });
};

/**
 * 获取所有资产变更记录列表（支持分页和搜索）
 * @param {object} params - 分页和搜索参数
 */
export function getAllAssetChanges(params = {}) {
    return service({
      url: `${API_PATH}/changes`,
      method: 'get',
      params
    });
};

/**
 * 根据变更记录ID获取单个变更记录详情
 * @param {string | number} changeId - 变更记录的主键ID
 */
export function getAssetChangeDetail(changeId) {
    return service({
      url: `${API_PATH}/changes/${changeId}`,
      method: 'get',
    });
};

/**
 * 创建资产变更记录
 * @param {object} changeData - 变更数据
 */
export function createAssetChange(changeData) {
    return service({
      url: `${API_PATH}/changes`,
      method: 'post',
      data: changeData
    });
};

/**
 * 回滚资产变更记录（删除变更记录）
 * @param {string | number} assetId - 资产ID（暂时不使用，保持接口兼容）
 * @param {string | number} changeId - 变更记录ID
 */
export function rollbackAsset(assetId, changeId) {
    return service({
      url: `${API_PATH}/changes/${changeId}`,
      method: 'delete'
    });
};

/**
 * 删除一个资产
 */
export function deleteAsset(id) {
  return service({
    url: `${API_PATH}/${id}`,
    method: 'delete',
  });
}; 