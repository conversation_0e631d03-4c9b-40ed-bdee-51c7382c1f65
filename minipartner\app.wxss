/**
 * 全局样式
 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  background-color: #f8f8f8;
}

.container {
  min-height: 100vh;
  background: #F8F8F8;
}

.btn-primary {
  background: #0FB9B1;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}

.btn-primary:active {
  opacity: 0.8;
}

.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #0FB9B1;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty {
  text-align: center;
  padding: 100rpx 0;
}

.empty image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty text {
  font-size: 28rpx;
  color: #999;
} 