const express = require('express');
const router = express.Router();
const { employee } = require('../middleware/auth');
const dashboardController = require('../controllers/dashboard.controller');

// 获取仪表板统计数据
router.get('/stats', employee.verifyEmployee, dashboardController.getDashboardStats);

// 获取用户增长趋势数据
router.get('/user-growth-trend', employee.verifyEmployee, dashboardController.getUserGrowthTrend);

// 获取订单状态分布数据
router.get('/order-status-distribution', employee.verifyEmployee, dashboardController.getOrderStatusDistribution);

// 获取最近活动记录
router.get('/recent-activities', employee.verifyEmployee, dashboardController.getRecentActivities);

module.exports = router;
