/**
 * 用户认证中间件
 * 专门处理小程序端用户的认证和权限控制
 */

const { verifyToken, requireUserType } = require('./base');

/**
 * 用户认证中间件
 * 验证token并确保是用户类型
 */
const verifyUser = [
  verifyToken,
  requireUserType('user')
];

/**
 * 检查用户是否只能访问自己的资源
 * @param {string} userIdField - 用户ID字段名，默认为'user_id'
 */
const requireSelfAccess = (userIdField = 'user_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未认证用户' });
    }
    
    if (req.user.type !== 'user') {
      return res.status(403).json({ message: '需要用户身份' });
    }
    
    const resourceUserId = req.params.id || req.params.userId;
    
    // 用户只能访问自己的资源
    if (parseInt(resourceUserId) !== req.user.id) {
      return res.status(403).json({ message: '只能访问自己的资源' });
    }
    
    next();
  };
};

/**
 * 检查用户是否可以访问指定企业的资源
 * 这个中间件需要配合数据库查询来验证用户是否绑定了该企业
 */
const requireEnterpriseAccess = () => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未认证用户' });
    }
    
    if (req.user.type !== 'user') {
      return res.status(403).json({ message: '需要用户身份' });
    }
    
    // 将企业访问检查标记传递给后续的控制器
    // 具体的企业权限检查应该在控制器中进行
    req.requireEnterpriseCheck = true;
    next();
  };
};

/**
 * 合伙人权限验证中间件
 * 检查用户是否为合伙人
 */
const requirePartner = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: '未认证用户' });
  }
  
  if (req.user.type !== 'user') {
    return res.status(403).json({ message: '需要用户身份' });
  }
  
  // 这里需要查询数据库确认用户是否为合伙人
  // 由于这是中间件，我们将检查标记传递给控制器
  req.requirePartnerCheck = true;
  next();
};

/**
 * 刷新token验证中间件
 * 专门用于验证刷新token
 */
const verifyRefreshToken = (req, res, next) => {
  const { refresh_token } = req.body;
  
  if (!refresh_token) {
    return res.status(403).json({ message: '缺少刷新token' });
  }
  
  const jwt = require('jsonwebtoken');
  const { JWT_SECRET } = require('./base');
  
  jwt.verify(refresh_token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(401).json({ message: '刷新token无效或已过期' });
    }
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({ message: '无效的刷新token类型' });
    }
    
    req.refreshTokenData = decoded;
    next();
  });
};

module.exports = {
  verifyUser,
  requireSelfAccess,
  requireEnterpriseAccess,
  requirePartner,
  verifyRefreshToken
};
