const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserPassword = sequelize.define('UserPassword', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true, // 一个用户只有一条密码记录
    comment: '关联user.id'
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '加密后的密码'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'user_password',
  timestamps: true,
  comment: '用户密码表',
  charset: 'utf8mb4',
  collate: 'utf8mb4_0900_ai_ci'
});

module.exports = UserPassword; 