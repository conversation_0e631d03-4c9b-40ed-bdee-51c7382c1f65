/**
 * 常见问题组件样式
 */

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: calc(100% - 60rpx);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  height: 32rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #FF3333, #FF6A6A);
  border-radius: 4rpx;
}

/* 常见问题 */
.faq-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.faq-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  width: 100%;
  word-wrap: break-word;
}

.faq-answer {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  width: 100%;
  word-wrap: break-word;
  word-break: break-all;
}
