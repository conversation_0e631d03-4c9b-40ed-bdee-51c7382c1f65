/**
 * 微信小程序服务
 * 处理微信登录相关的API调用
 */

const axios = require('axios');

class WechatService {
  constructor() {
    this.appId = process.env.WECHAT_APPID;
    this.appSecret = process.env.WECHAT_SECRET;
    
    if (!this.appId || !this.appSecret) {
      console.warn('微信小程序配置不完整，请检查环境变量 WECHAT_APPID 和 WECHAT_SECRET');
    }
  }

  /**
   * 通过code获取微信用户的openid和session_key
   * @param {string} code - 微信登录code
   * @returns {Promise<Object>} 包含openid、session_key、unionid等信息
   */
  async getWechatUserInfo(code) {
    try {
      const url = 'https://api.weixin.qq.com/sns/jscode2session';
      const params = {
        appid: this.appId,
        secret: this.appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      };

      console.log('调用微信API获取用户信息，参数:', { 
        appid: this.appId, 
        js_code: code,
        grant_type: 'authorization_code'
      });

      const response = await axios.get(url, { params });
      const data = response.data;

      console.log('微信API响应:', data);

      // 检查是否有错误
      if (data.errcode) {
        throw new Error(`微信API错误: ${data.errcode} - ${data.errmsg}`);
      }

      // 返回用户信息
      return {
        openid: data.openid,
        session_key: data.session_key,
        unionid: data.unionid || null // unionid可能不存在
      };

    } catch (error) {
      console.error('获取微信用户信息失败:', error);
      throw new Error('获取微信用户信息失败: ' + error.message);
    }
  }

  /**
   * 验证微信小程序数据签名
   * @param {string} rawData - 原始数据
   * @param {string} signature - 签名
   * @param {string} sessionKey - 会话密钥
   * @returns {boolean} 验证结果
   */
  verifySignature(rawData, signature, sessionKey) {
    const crypto = require('crypto');
    const sha1 = crypto.createHash('sha1');
    sha1.update(rawData + sessionKey);
    const calculatedSignature = sha1.digest('hex');
    
    return calculatedSignature === signature;
  }

  /**
   * 解密微信小程序加密数据
   * @param {string} encryptedData - 加密数据
   * @param {string} iv - 初始向量
   * @param {string} sessionKey - 会话密钥
   * @returns {Object} 解密后的数据
   */
  decryptData(encryptedData, iv, sessionKey) {
    const crypto = require('crypto');
    
    try {
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      
      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);
      
      let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('解密微信数据失败:', error);
      throw new Error('解密微信数据失败');
    }
  }
}

module.exports = new WechatService();
