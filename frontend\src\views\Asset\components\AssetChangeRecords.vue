<template>
  <div class="asset-change-records">
    <!-- 变更记录组件 - 处理资产变更记录列表，支持查看变更详情和回滚功能 -->
    
    <div class="form-section">
      <h4 class="section-title">变更记录</h4>
      
      <!-- 操作栏 -->
      <div class="action-bar" v-if="!readonly">
        <el-button 
          type="primary" 
          @click="refreshRecords"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新记录
        </el-button>
      </div>

      <!-- 变更记录表格 -->
      <el-table 
        :data="changeRecords" 
        v-loading="loading"
        border 
        style="width: 100%"
        empty-text="暂无变更记录"
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
      >
        <!-- 资产变更ID -->
        <el-table-column prop="asset_change_id" label="变更ID" width="120">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="viewChangeDetail(row.id)"
            >
              {{ row.asset_change_id }}
            </el-button>
          </template>
        </el-table-column>
        
        <!-- 变更时间 -->
        <el-table-column prop="change_date" label="变更日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.change_date) }}
          </template>
        </el-table-column>
        
        <!-- 制单人 -->
        <el-table-column prop="creator.name" label="制单人" width="100">
          <template #default="{ row }">
            {{ getCreatorName(row.creator) }}
          </template>
        </el-table-column>
        
        <!-- 创建时间 -->
        <el-table-column prop="createdAt" label="创建时间" width="160" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <!-- 变更摘要 -->
        <el-table-column prop="remark" label="变更摘要" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="change-summary">
              <!-- 显示变更摘要的前两行 -->
              <div class="summary-text">
                {{ getChangeSummary(row.remark) }}
              </div>
              
              <!-- 变更字段标签 -->
              <div class="change-tags" v-if="getChangeFields(row).length > 0">
                <el-tag
                  v-for="field in getChangeFields(row).slice(0, 3)"
                  :key="field"
                  size="small"
                  type="info"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
                <el-tag
                  v-if="getChangeFields(row).length > 3"
                  size="small"
                  type="info"
                  class="field-tag"
                >
                  +{{ getChangeFields(row).length - 3 }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="warning"
              @click="rollbackToChange(row)"
              :disabled="readonly"
            >
              回滚
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 回滚确认对话框 -->
    <RollbackDialog
      v-model="showRollbackDialog"
      :rollback-record="rollbackRecord"
      :loading="rollbackLoading"
      @confirm="confirmRollback"
      @cancel="cancelRollback"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { getAssetChangeRecords } from '@/api/asset.js'
import { useAssetData } from '../composables/useAssetData.js'
import { formatDateTime } from '@/utils/format.js'
import RollbackDialog from './RollbackDialog.vue'

// Props定义
const props = defineProps({
  // 资产ID
  assetId: {
    type: [Number, String],
    required: true
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits(['rollback-success'])

const router = useRouter()

// 使用 composable
const {
  rollbackLoading,
  showRollbackDialog,
  rollbackRecord,
  showRollbackConfirm,
  executeRollback,
  cancelRollback
} = useAssetData()

// 状态数据
const loading = ref(false)
const changeRecords = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 加载变更记录
const loadChangeRecords = async () => {
  if (!props.assetId) return
  
  loading.value = true
  try {
    const response = await getAssetChangeRecords(props.assetId, {
      page: currentPage.value,
      pageSize: pageSize.value
    })
    
    changeRecords.value = response.records || []
    total.value = response.total || 0
  } catch (error) {
    console.error('加载变更记录失败:', error)
    ElMessage.error('加载变更记录失败')
  } finally {
    loading.value = false
  }
}

// 刷新记录
const refreshRecords = () => {
  loadChangeRecords()
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

// 获取制单人姓名
const getCreatorName = (creator) => {
  if (!creator) return '未知'
  return creator.name || creator.employee_name || '未知'
}

// 获取变更摘要（取备注的前50个字符）
const getChangeSummary = (remark) => {
  if (!remark) return '无备注'
  
  // 如果备注包含自动生成的变更信息，提取第一行作为摘要
  const lines = remark.split('\n')
  const firstLine = lines[0]
  
  if (firstLine.length > 50) {
    return firstLine.substring(0, 50) + '...'
  }
  
  return firstLine
}

// 从备注中提取变更字段
const getChangeFields = (record) => {
  if (!record.remark) return []
  
  const fields = []
  const lines = record.remark.split('\n')
  
  lines.forEach(line => {
    // 匹配 "字段名：原值 → 新值" 的格式
    const match = line.match(/^(.+?)：.+?→.+?$/)
    if (match) {
      fields.push(match[1].trim())
    }
  })
  
  return fields
}

// 查看变更详情
const viewChangeDetail = (changeId) => {
  router.push({ 
    name: 'asset-change-detail', 
    params: { id: changeId } 
  })
}

// 回滚到指定变更
const rollbackToChange = (record) => {
  // 构造回滚记录对象，匹配 RollbackDialog 期望的字段格式
  const rollbackRecordData = {
    id: record.id,
    asset_change_id: record.asset_change_id,
    created_at: record.createdAt || record.change_date, // 优先使用 createdAt，fallback 到 change_date
    created_by: record.creator?.name || '未知'
  }

  showRollbackConfirm(rollbackRecordData)
}

// 确认回滚操作
const confirmRollback = async () => {
  await executeRollback(props.assetId, async () => {
    // 刷新变更记录
    await loadChangeRecords()
    // 通知父组件回滚成功
    emit('rollback-success')
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadChangeRecords()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadChangeRecords()
}

// 监听资产ID变化
watch(() => props.assetId, (newAssetId) => {
  if (newAssetId) {
    currentPage.value = 1
    loadChangeRecords()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.assetId) {
    loadChangeRecords()
  }
})
</script>

<style scoped>
.asset-change-records {
  padding: 20px;
}

.form-section {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.action-bar {
  margin-bottom: 16px;
  text-align: right;
}

.change-summary {
  max-width: 300px;
}

.summary-text {
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.change-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.field-tag {
  font-size: 12px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.rollback-content {
  padding: 10px 0;
}

.rollback-info {
  margin: 10px 0;
  padding-left: 20px;
}

.rollback-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.warning-text {
  margin-top: 15px;
  color: #e6a23c;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }
  
  .change-summary {
    max-width: 200px;
  }
  
  .el-dialog {
    width: 95% !important;
  }
}
</style>
