/**
 * 员工认证中间件
 * 专门处理web端员工的认证和权限控制
 */

const { verifyToken, requireUserType } = require('./base');

/**
 * 员工认证中间件
 * 验证token并确保是员工类型
 */
const verifyEmployee = [
  verifyToken,
  requireUserType('employee')
];

/**
 * 管理员权限验证中间件
 * 确保是管理员角色的员工
 */
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: '未认证用户' });
  }
  
  if (req.user.type !== 'employee') {
    return res.status(403).json({ message: '需要员工身份' });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: '需要管理员权限' });
  }
  
  next();
};

/**
 * 员工或管理员权限验证中间件
 * 确保是员工身份（包括普通员工和管理员）
 */
const requireEmployeeOrAdmin = [
  verifyToken,
  requireUserType('employee')
];

/**
 * 管理员完整验证中间件
 * 包含token验证和管理员权限验证
 */
const verifyAdmin = [
  verifyToken,
  requireUserType('employee'),
  requireAdmin
];

/**
 * 检查是否为资源所有者或管理员
 * @param {string} resourceOwnerField - 资源所有者字段名，默认为'employee_id'
 */
const requireOwnershipOrAdmin = (resourceOwnerField = 'employee_id') => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未认证用户' });
    }
    
    // 管理员可以访问所有资源
    if (req.user.type === 'employee' && req.user.role === 'admin') {
      return next();
    }
    
    // 普通员工只能访问自己的资源
    const resourceId = req.params.id;
    if (!resourceId) {
      return res.status(400).json({ message: '缺少资源ID' });
    }
    
    // 这里需要根据具体的资源模型来检查所有权
    // 由于这是通用中间件，具体的检查逻辑应该在使用时传入
    req.resourceOwnerField = resourceOwnerField;
    next();
  };
};

module.exports = {
  verifyEmployee,
  requireAdmin,
  requireEmployeeOrAdmin,
  verifyAdmin,
  requireOwnershipOrAdmin
};
