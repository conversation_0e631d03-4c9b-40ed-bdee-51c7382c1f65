// pages/asset-detail/asset-detail.js
const api = require('../../utils/api');

Page({
  data: {
    assetId: null,
    asset: null,
    loading: true,
    error: null
  },

  onLoad(options) {
    console.log('资产详情页加载，参数:', options);
    
    if (options.id) {
      this.setData({
        assetId: options.id
      });
      this.loadAssetDetail();
    } else {
      this.showError('缺少资产ID参数');
    }
  },

  onShow() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '资产详情'
    });
  },

  /**
   * 加载资产详情
   */
  async loadAssetDetail() {
    try {
      this.setData({ loading: true, error: null });
      
      console.log('开始加载资产详情，ID:', this.data.assetId);
      
      const response = await api.get(`/assets/${this.data.assetId}`);
      console.log('资产详情响应:', response);

      // 修复：API返回的数据可能直接是资产对象，也可能包装在data字段中
      const assetData = response.data || response;

      console.log('处理后的资产数据:', assetData);
      console.log('资产数据类型:', typeof assetData);
      console.log('资产ID存在:', !!assetData.id);
      console.log('产品信息:', assetData.product);
      if (assetData.product) {
        console.log('产品功能:', assetData.product.features);
        console.log('产品功能数量:', assetData.product.features ? assetData.product.features.length : 0);
        console.log('产品功能详情:', JSON.stringify(assetData.product.features, null, 2));
      } else {
        console.log('没有产品信息');
      }
      console.log('价格信息:', {
        product_standard_price: assetData.product_standard_price,
        sps_annual_fee: assetData.sps_annual_fee,
        after_sales_service_fee: assetData.after_sales_service_fee,
        implementation_fee: assetData.implementation_fee
      });

      if (assetData && assetData.id) {
        this.setData({
          asset: assetData,
          loading: false
        });

        console.log('资产数据设置成功');

        // 更新页面标题
        if (assetData.product && assetData.product.product_name) {
          wx.setNavigationBarTitle({
            title: assetData.product.product_name
          });
          console.log('页面标题更新为:', assetData.product.product_name);
        }
      } else {
        console.error('资产数据验证失败:', {
          hasData: !!assetData,
          hasId: !!(assetData && assetData.id),
          dataKeys: assetData ? Object.keys(assetData) : 'no data'
        });
        this.showError('资产数据格式错误');
      }
      
    } catch (error) {
      console.error('加载资产详情失败:', error);
      this.showError(error.message || '加载资产详情失败');
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({
      loading: false,
      error: message
    });
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/me/me'
      });
    }
  },

  /**
   * 显示更多操作
   */
  showMoreActions() {
    const itemList = ['刷新数据', '分享资产', '导出信息'];
    
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.refreshData();
            break;
          case 1:
            this.shareAsset();
            break;
          case 2:
            this.exportInfo();
            break;
        }
      }
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    wx.showToast({
      title: '刷新中...',
      icon: 'loading'
    });
    this.loadAssetDetail();
  },

  /**
   * 分享资产
   */
  shareAsset() {
    if (!this.data.asset) return;
    
    const asset = this.data.asset;
    const productName = asset.product ? asset.product.product_name : '资产';
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 这里可以实现分享逻辑
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  /**
   * 导出信息
   */
  exportInfo() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  /**
   * 复制文本
   */
  copyText(e) {
    const text = e.currentTarget.dataset.text;
    if (!text) return;
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '是否拨打客服电话：************？',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************',
            fail: () => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 查看变更记录
   */
  viewChangeLogs() {
    if (!this.data.assetId) return;
    
    wx.navigateTo({
      url: `/pages/change-logs/change-logs?assetId=${this.data.assetId}`
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadAssetDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const asset = this.data.asset;
    if (!asset) {
      return {
        title: '资产详情',
        path: '/pages/asset-detail/asset-detail'
      };
    }
    
    const productName = asset.product ? asset.product.product_name : '资产';
    return {
      title: `我的${productName}资产详情`,
      path: `/pages/asset-detail/asset-detail?id=${this.data.assetId}`,
      imageUrl: '' // 可以设置分享图片
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const asset = this.data.asset;
    const productName = asset && asset.product ? asset.product.product_name : '资产';
    
    return {
      title: `我的${productName}资产详情`,
      query: `id=${this.data.assetId}`,
      imageUrl: '' // 可以设置分享图片
    };
  }
});
