/* 引入iconfont图标库 */
@import '/static/fonts/iconfont.wxss';

/* 悬浮咨询按钮容器 */
.float-consult {
  position: relative;
  z-index: 999;
}

/* 毛玻璃背景遮罩 */
.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0); 
  backdrop-filter: blur(0px);
  -webkit-backdrop-filter: blur(0px);
  z-index: 998;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.backdrop.active {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  pointer-events: auto;
}

/* 主按钮样式 */
.main-button {
  position: fixed;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  z-index: 1000;
}

.main-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4), transparent 70%);
  animation: pulse 3s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.5;
  }
}

.main-button.active {
  transform: scale(0.92);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.2);
  background: #ffffff;
}

.icon-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-button .iconfont {
  font-size: 48rpx;
  color: #ffffff;
  transition: all 0.3s;
}

.main-button.active .iconfont {
  color: #4facfe;
  font-size: 40rpx;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.avatar-container {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #ffffff;
  z-index: 2;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 菜单容器样式 */
.menu-container {
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 999;
}

.menu-container.expanded {
  opacity: 1;
  pointer-events: auto;
}

/* 菜单项容器 */
.menu-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

/* 菜单项样式 */
.menu-items .menu-item {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transform: scale(0) translateY(30rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 100%;
  background-color: transparent;
  padding: 0;
  margin: 0;
  border: none;
  border-radius: 0;
  box-sizing: border-box;
  line-height: normal;
  overflow: visible;
  text-align: left;
}

/* 移除按钮点击时的默认边框 */
.menu-items .menu-item::after {
  display: none;
}

.menu-items .menu-item.visible {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.menu-items .menu-item:nth-child(1) {
  transition-delay: 0.2s;
}

.menu-items .menu-item:nth-child(2) {
  transition-delay: 0.15s;
}

.menu-items .menu-item:nth-child(3) {
  transition-delay: 0.1s;
}

.menu-items .menu-item.active .menu-icon {
  transform: scale(1.15);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  z-index: 2;
}

.menu-icon .iconfont {
  font-size: 40rpx;
  color: #ffffff;
}

/* 菜单标签样式 */
.menu-label {
  position: relative;
  opacity: 0;
  transform: translateX(20rpx);
  transition: all 0.3s;
  max-width: 0;
  overflow: hidden;
  margin-right: 12rpx;
}

.menu-label::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -8rpx;
  transform: translateY(-50%) rotate(45deg);
  width: 16rpx;
  height: 16rpx;
  border-radius: 2rpx;
  z-index: 1;
  transition: all 0.3s;
}

.menu-label.visible {
  opacity: 1;
  transform: translateX(0);
  max-width: 200rpx;
}

.menu-label .menu-text {
  position: relative;
  z-index: 2;
  display: block;
  padding: 8rpx 16rpx;
  color: white;
  font-size: 24rpx;
  white-space: nowrap;
  font-weight: 500;
  border-radius: 8rpx;
}

.menu-items .menu-item:nth-child(1) .menu-label .menu-text {
  background: rgba(7, 193, 96, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.menu-items .menu-item:nth-child(1) .menu-label::after {
  background: rgba(7, 193, 96, 0.9);
}

.menu-items .menu-item:nth-child(2) .menu-label .menu-text {
  background: rgba(59, 148, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(59, 148, 255, 0.3);
}

.menu-items .menu-item:nth-child(2) .menu-label::after {
  background: rgba(59, 148, 255, 0.9);
}

.menu-items .menu-item:nth-child(3) .menu-label .menu-text {
  background: rgba(255, 106, 0, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(255, 106, 0, 0.3);
}

.menu-items .menu-item:nth-child(3) .menu-label::after {
  background: rgba(255, 106, 0, 0.9);
}

/* 关闭按钮样式 */
.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: scale(0) rotate(-90deg);
  opacity: 0;
  transition: all 0.3s;
  position: absolute;
  right: 20rpx;
  top: -80rpx;
}

.close-btn.visible {
  transform: scale(1) rotate(0deg);
  opacity: 1;
  transition-delay: 0.3s;
}

.close-icon {
  font-size: 40rpx;
  color: #666;
  font-weight: 300;
}

/* 底部弹出表单容器样式 */
.callback-form-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.36, 0.66, 0.04, 1);
}

.callback-form-container.show {
  transform: translateY(0);
}

.callback-form-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.4); */
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
}

.callback-form {
  position: relative;
  background: #fff;
  border-top-left-radius: 28rpx;
  border-top-right-radius: 28rpx;
  overflow: hidden;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  max-height: 70vh;
}

@keyframes slideUp {
  from {
    transform: translateY(60rpx);
    opacity: 0.8;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.callback-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
}

.callback-form-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #f5f5f5;
}

.callback-form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.callback-form-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #4facfe, #00f2fe);
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.callback-form-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 50%;
  transition: all 0.2s;
}

.callback-form-close:active {
  background: #eef0f4;
  transform: scale(0.92);
}

.callback-form-close .iconfont {
  font-size: 32rpx;
  color: #999;
}

.callback-form-content {
  padding: 20rpx 30rpx;
}

.form-item {
  margin-bottom: 24rpx;
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  margin-left: 4rpx;
}

.form-label .required {
  color: #ff4d4f;
  margin-right: 4rpx;
  font-size: 24rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 1px solid #f0f0f0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.2s;
}

.form-input:focus {
  background: #fff;
  border-color: #4facfe;
  box-shadow: 0 0 0 2rpx rgba(79, 172, 254, 0.1);
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background: #f8f9fa;
  border: 1px solid #f0f0f0;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.2s;
}

.form-textarea:focus {
  background: #fff;
  border-color: #4facfe;
  box-shadow: 0 0 0 2rpx rgba(79, 172, 254, 0.1);
}

.textarea-counter {
  position: absolute;
  right: 14rpx;
  bottom: 8rpx;
  font-size: 22rpx;
  color: #bbb;
}

.callback-form-footer {
  padding: 8rpx 30rpx 20rpx;
}

.submit-btn {
  width: 100%;
  height: 84rpx;
  line-height: 84rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 42rpx;
  text-align: center;
  box-shadow: 0 8rpx 16rpx rgba(79, 172, 254, 0.2);
  transition: all 0.2s;
  margin: 0;
  letter-spacing: 2rpx;
}

.submit-btn:active {
  opacity: 0.88;
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(79, 172, 254, 0.15);
}

.form-tips {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-top: 16rpx;
} 