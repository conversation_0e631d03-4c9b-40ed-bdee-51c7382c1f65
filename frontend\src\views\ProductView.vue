<script setup>
import { ref, computed } from 'vue';
import CrudPage from '@/components/CrudPage.vue';
import { getProducts, createProduct, updateProduct, deleteProduct, getNextProductId } from '@/api/product.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDateTime } from '@/utils/format.js';
import ProductFeatureDialog from '@/components/Product/ProductFeatureDialog.vue';
import ProductUserAddonTierDialog from '@/components/Product/ProductUserAddonTierDialog.vue'; // [新增] 1. 引入阶梯价格弹窗组件

// 1. 定义表格列
const columns = ref([
  { prop: 'id', label: '自增ID', width: 80, fixed: true },
  { prop: 'product_id', label: '产品ID', width: 120, fixed: true },
  { prop: 'product_name', label: '产品名称', width: 150 },
  { prop: 'version_name', label: '版本名称', width: 120 },
  { prop: 'base_price', label: '基础价格', width: 100 },
  { prop: 'base_account_count', label: '基础账套数', width: 110 },
  { prop: 'base_user_count', label: '基础用户数', width: 110 },
  { prop: 'allow_user_addon', label: '允许增购用户', width: 130, isSlot: true },
  { prop: 'allow_account_addon', label: '允许增购账套', width: 130, isSlot: true },
  { prop: 'features', label: '关联功能', minWidth: 250, isSlot: true },
  { prop: 'remark', label: '备注', minWidth: 150, showOverflowTooltip: true },
  { prop: 'createdAt', label: '创建时间', width: 180, isSlot: true },
  { prop: 'updatedAt', label: '修改时间', width: 180, isSlot: true },
]);

// 2. 定义API
const api = {
  list: getProducts, create: createProduct, update: updateProduct, delete: deleteProduct
};

// 3. 状态管理
const crudPageRef = ref(null);
const selectedItems = ref([]);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const form = ref({});

// 3.1 功能管理弹窗状态 (完全保留)
const featureDialogVisible = ref(false); 
const currentProduct = ref(null);

// [新增] 3.2 阶梯价格弹窗状态
const tierDialogVisible = ref(false); 

// 4. 计算属性
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);

// 5. 方法
const getInitialForm = () => ({
  product_id: '',
  product_name: '',
  version_name: '',
  base_price: 0,
  base_account_count: 1,
  base_user_count: 1,
  allow_user_addon: false,
  allow_account_addon: false,
  // [新增] 为增购价格设置初始值
  addons: {
    user_addon_price: 0,
    account_addon_price: 0
  },
  remark: ''
});

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const handleOpenDialog = async (editMode = false, copyMode = false, rowData = null) => {
  dialogVisible.value = true; // [修复] 立即打开弹窗以提供即时反馈
  isEditMode.value = editMode;

  // [新增] 定义一个统一处理函数，确保数据类型正确
  const processData = (data) => {
    const processed = { ...data };
    // 显式将字符串转为数字
    processed.base_price = Number(data.base_price) || 0;
    processed.base_account_count = Number(data.base_account_count) || 1;
    processed.base_user_count = Number(data.base_user_count) || 1;
    
    // 确保addons对象及其内部属性也为数字
    processed.addons = data.addons && typeof data.addons === 'object' 
      ? { account_addon_price: Number(data.addons.account_addon_price) || 0 }
      : { account_addon_price: 0 };
      
    return processed;
  };

  if (editMode && rowData) {
    // [修复] 使用处理函数确保类型正确
    form.value = processData(rowData);
  } else if (copyMode && rowData) {
    // [修复] 复制时同样要确保类型正确
    const copiedData = processData(rowData);
    delete copiedData.id;
    try {
      const res = await getNextProductId();
      copiedData.product_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新产品ID失败，请为复制的记录输入一个唯一的ID。');
      copiedData.product_id = '';
    }
    form.value = copiedData;
    isEditMode.value = false;
  } else {
    form.value = getInitialForm();
    try {
      const res = await getNextProductId();
      form.value.product_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新产品ID失败，请输入一个唯一的ID。');
    }
  }
};

const handleBatchDelete = async () => {
  if (isDeleteDisabled.value) return;
  const idsToDelete = selectedItems.value.map(item => item.id);
  await ElMessageBox.confirm(`确定删除选中的 ${idsToDelete.length} 个产品吗？此操作不可逆！`, '警告', { type: 'warning' });
  try {
    await Promise.all(idsToDelete.map(id => api.delete(id)));
    ElMessage.success('删除成功');
    crudPageRef.value?.loadData();
    selectedItems.value = [];
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

const handleSubmit = async () => {
  try {
    let dataToSend = { ...form.value };
    dataToSend.base_price = Number(dataToSend.base_price);
    dataToSend.base_account_count = Number(dataToSend.base_account_count);
    dataToSend.base_user_count = Number(dataToSend.base_user_count);

    // [修改] 确保 addons 里的值也是数字类型
    if (dataToSend.addons) {
      dataToSend.addons.user_addon_price = Number(dataToSend.addons.user_addon_price);
      dataToSend.addons.account_addon_price = Number(dataToSend.addons.account_addon_price);
    }

    if (isEditMode.value) {
      await api.update(form.value.id, dataToSend);
      ElMessage.success('更新成功');
    } else {
      await api.create(dataToSend);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
    const action = isEditMode.value ? '更新' : '创建';
    ElMessage.error(`${action}失败: ${error.response?.data?.message || error.message}`);
  }
};

// 6. 特殊逻辑方法 (完全保留)
const handleManageFeatures = (product) => {
  currentProduct.value = product;
  featureDialogVisible.value = true;
};

// [新增] 6.1 打开阶梯价格管理弹窗
const handleManageTiers = (product) => {
  currentProduct.value = product;
  tierDialogVisible.value = true;
};


const handleFeaturesUpdated = async () => {
  // [修复] 统一调用刷新逻辑并更新当前产品
  handleDialogClose(true);
};

// [修改] 弹窗关闭后的统一处理逻辑，增加一个参数决定是否要更新currentProduct
const handleDialogClose = async (shouldUpdateCurrent = false) => {
  const latestData = await crudPageRef.value?.loadData();
  // 如果需要，就从刷新后的列表中找到最新的产品数据，并更新currentProduct
  if (shouldUpdateCurrent && currentProduct.value && latestData) {
    const updatedProduct = latestData.find(p => p.id === currentProduct.value.id);
    if (updatedProduct) {
      currentProduct.value = updatedProduct; // 重新赋值，触发子组件prop更新
    }
  }
}
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="产品"
    :columns="columns"
    :api-list="api.list"
    :api-create="api.create"
    :api-update="api.update"
    :api-delete="api.delete"
    :hide-row-actions="false"
    :row-actions-width="120"
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="handleOpenDialog(false, false, null)">新增产品</el-button>
      <el-tooltip content="请选择一个产品进行修改" :disabled="!isEditDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button :disabled="isEditDisabled" @click="handleOpenDialog(true, false, selectedItems[0])">修改</el-button></div>
      </el-tooltip>
      <el-tooltip content="请选择一个产品进行复制" :disabled="!isCopyDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="success" :disabled="isCopyDisabled" @click="handleOpenDialog(false, true, selectedItems[0])">复制</el-button></div>
      </el-tooltip>
      <el-tooltip content="请至少选择一个产品进行删除" :disabled="!isDeleteDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="danger" :disabled="isDeleteDisabled" @click="handleBatchDelete">删除</el-button></div>
      </el-tooltip>
    </template>
    
    <!-- 2. 自定义列 -->
    <template #col-allow_user_addon="{ row }"><el-tag :type="row.allow_user_addon ? 'success' : 'danger'">{{ row.allow_user_addon ? '是' : '否' }}</el-tag></template>
    <template #col-allow_account_addon="{ row }"><el-tag :type="row.allow_account_addon ? 'success' : 'danger'">{{ row.allow_account_addon ? '是' : '否' }}</el-tag></template>
    <template #col-features="{ row }">
      <div v-if="row.features && row.features.length > 0">
        <el-tag v-for="feature in row.features" :key="feature.feature_id" style="margin: 2px;">{{ feature.feature_name }} (￥{{ feature.ProductFeatureRelation.feature_price }})</el-tag>
      </div>
      <span v-else>无</span>
    </template>
    <template #col-createdAt="{ row }">{{ formatDateTime(row.createdAt) }}</template>
    <template #col-updatedAt="{ row }">{{ formatDateTime(row.updatedAt) }}</template>

    <!-- 3. 行内操作 -->
    <template #row-actions="{ row }">
      <div class="row-actions-container">
        <el-button size="small" type="primary" @click="handleManageFeatures(row)">功能管理</el-button>
        <el-button v-if="row.allow_user_addon" size="small" @click="handleManageTiers(row)">用户增价</el-button>
      </div>
    </template>

    <!-- 4. 新增/编辑弹窗 -->
    <template #dialog>
      <el-dialog :model-value="dialogVisible" :title="isEditMode ? '编辑产品' : '新增产品'" width="600px" :close-on-click-modal="false">
        <el-form :model="form" label-width="120px">
          <el-form-item label="产品ID"><el-input v-model="form.product_id"></el-input></el-form-item>
          <el-form-item label="产品名称"><el-input v-model="form.product_name"></el-input></el-form-item>
          <el-form-item label="版本名称"><el-input v-model="form.version_name"></el-input></el-form-item>
          <el-form-item label="基础价格"><el-input-number v-model="form.base_price" :min="0" style="width: 100%;"></el-input-number></el-form-item>
          <el-form-item label="基础账套数"><el-input-number v-model="form.base_account_count" :min="1" style="width: 100%;"></el-input-number></el-form-item>
          <el-form-item label="基础用户数"><el-input-number v-model="form.base_user_count" :min="1" style="width: 100%;"></el-input-number></el-form-item>
          <el-form-item label="允许增购用户"><el-switch v-model="form.allow_user_addon"></el-switch></el-form-item>
          <!-- [移除] 不再需要固定的增购用户单价 -->
          <el-form-item label="允许增购账套"><el-switch v-model="form.allow_account_addon"></el-switch></el-form-item>
          <!-- [保留] 增购账套单价输入框 -->
          <el-form-item v-if="form.allow_account_addon" label="增购账套单价">
            <el-input-number v-model="form.addons.account_addon_price" :min="0" :precision="2" style="width: 100%;"></el-input-number>
          </el-form-item>
          <el-form-item label="备注"><el-input v-model="form.remark" type="textarea"></el-input></el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>

  <!-- 功能管理弹窗 (完全保留) -->
  <ProductFeatureDialog 
    v-if="featureDialogVisible"
    v-model:visible="featureDialogVisible"
    :product="currentProduct"
    @features-updated="handleFeaturesUpdated"
  />
  <!-- [新增] 阶梯价格管理弹窗 -->
  <ProductUserAddonTierDialog
    v-if="tierDialogVisible"
    v-model:visible="tierDialogVisible"
    :product="currentProduct"
    @tiers-updated="handleDialogClose"
  />
</template>

<style scoped>
.page-container {
  padding: 20px;
}
.action-bar {
  margin-bottom: 20px;
}
.row-actions-container {
  display: flex;
  flex-direction: column;
  gap: 5px; /* Adds space between buttons */
}

.row-actions-container .el-button {
  margin-left: 0 !important; /* Override element-plus default margin */
}
</style> 