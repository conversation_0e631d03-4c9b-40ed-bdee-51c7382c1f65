# Web登录与简单权限设置开发文档

本文档旨在详细阐述本客户管理系统的用户认证（登录）和授权（权限设置）机制，方便新加入的开发者快速理解项目的核心安全逻辑。

## 1. 核心理念

系统采用基于 **JWT (JSON Web Token)** 的认证方式和基于 **角色 (Role-Based)** 的授权模式。

### 1.1. JWT 认证机制
- **登录**：用户通过用户名和密码调用 `POST /api/auth/signin` 接口。
- **签发 Token**：验证成功后，后端会生成一个 JWT，其中包含了用户的核心信息，如 `id`, `username`, `role` (角色)等，并将此 Token 返回给前端。
- **前端存储**：前端获取到 Token 后，会将其存储在本地（如 `localStorage`），并在后续的每一次 API 请求中，通过 HTTP 请求头 `x-access-token` 将其发送给后端。
- **后端验证**：后端收到请求后，通过 `authJwt.verifyToken` 中间件来验证 Token 的有效性。验证通过则允许访问，否则拒绝。

### 1.2. 角色划分
系统内置了两种核心角色，权限严格区分：
- `admin` (管理员)：拥有系统的最高权限，可以查看和操作所有数据。
- `user` (普通员工)：只能操作与其自身相关的数据，权限受限。

---

## 2. 后端实现

后端的权限控制主要通过 Express 的中间件（Middleware）来实现。

### 2.1. 认证中间件 (`middleware/authJwt.js`)
这是权限系统的第一道门，负责检查用户是否登录以及其角色。
- `verifyToken`: 检查请求头中是否存在有效 Token。这是所有需要登录才能访问的接口的**必备**中间件。
- `isAdmin`: 检查 `verifyToken` 解析出的用户角色是否为 `admin`。用于保护只有管理员才能访问的接口。

### 2.2. 权限中间件 (`middleware/permissionChecks.js`)
这是为实现"普通用户只能操作自己的数据"这一核心需求而创建的自定义中间件。
- `checkOwnership(Model)`: 
    - **作用**：检查当前登录的用户是否有权操作目标资源（如某条跟进记录、某个企业）。
    - **逻辑**：它首先通过 `verifyToken` 获取当前用户ID，然后根据请求的资源ID（如 `:id`）在指定的 `Model`（数据库模型）中查找该条记录。最后，比较记录的 `employee_id` 是否与当前用户ID匹配。如果是管理员，则直接放行。
    - **优点**：这是一个高度可复用的中间件，只需要传入不同的数据库模型，就可以在任何需要检查所有权的路由上使用。

### 2.3. 路由保护示例
通过组合使用上述中间件，我们可以灵活地保护各个API路由。
```javascript
// backend/src/routes/followup.routes.js

// 只有登录用户才能创建
router.post("/", [authJwt.verifyToken, ...], followups.create);

// 只有登录用户，且是该条记录的所有者（或管理员）才能更新
router.put("/:id", [authJwt.verifyToken, checkOwnership(Followup), ...], followups.update);

// backend/src/routes/asset.routes.js

// 只有管理员才能回滚
router.delete('/logs/:logId', [authJwt.verifyToken, authJwt.isAdmin], assetController.revertAssetChange);
```

---

## 3. 前端实现

前端的权限控制主要体现在 **UI元素的动态渲染** 和 **API请求的发送**上。

### 3.1. 登录与Token存储 (`store/auth.js`)
- **登录**：调用登录API，成功后将返回的 `user` 对象和 `accessToken` 存入 `pinia` 状态和 `localStorage`。
- **全局状态**：通过 `useAuth()` hook，任何 Vue 组件都可以方便地获取当前用户的登录状态、信息和角色。
  ```javascript
  import { useAuth } from '@/store/auth.js';
  const { state: authState } = useAuth();
  const isAdmin = computed(() => authState.user?.role === 'admin');
  ```

### 3.2. API请求封装 (`utils/request_extra.js`)
所有 API 请求都通过 Axios 实例发出。该实例配置了一个请求拦截器，会自动从 `localStorage` 中读取 Token，并将其放入每个请求的 `x-access-token` 请求头中。

### 3.3. 基于角色的UI控制
在 Vue 组件的模板中，大量使用 `v-if` 或 `v-show` 指令配合 `isAdmin` 这个计算属性，来控制界面的显示。
- **按钮显隐**：管理员可见的删除、回滚等高危按钮，对普通员工直接不渲染。
  ```html
  <!-- 资产变更记录中的回滚按钮 -->
  <el-button v-if="isAdmin" size="small" type="danger" @click="handleRevert(log.id)">回滚</el-button>
  ```
- **表单项禁用**：在新增/编辑时，涉及"操作员工"的字段，对普通员工会自动填充为自己且禁用，防止其选择他人。
  ```html
  <!-- 跟进记录中的跟进员工下拉框 -->
  <el-select v-model="newFollowup.employee_id" :disabled="!isAdmin">
    ...
  </el-select>
  ```

---

## 4. 具体模块权限逻辑

| 模块 | 操作 | 管理员 (admin) | 普通员工 (user) | 实现方式 |
|---|---|---|---|---|
| **员工管理** | 查看列表 | ✅ | ❌ | 后端路由 `isAdmin` 保护 |
| | 修改密码 | ✅ (需二次验证) | 只能改自己的 | 前端UI判断 + 后端逻辑 |
| **企业管理** | 创建 | ✅ | ✅ | 后端路由 `verifyToken` |
| | 修改/删除 | ✅ (可操作所有) | 只能操作自己负责的 | 后端 `checkOwnership` 中间件 |
| | 负责人字段 | 下拉框可选任意员工 | 文本框显示自己(不可改) | 前端 `v-if="isAdmin"` |
| **企业跟进** | 创建 | ✅ | ✅ | `verifyToken` + 前端禁用员工选择框 |
| | 修改/删除 | ✅ (可操作所有) | 只能操作自己创建的 | 后端 `checkOwnership` 中间件 |
| **资产管理** | 创建/修改 | ✅ | ✅ | `verifyToken` |
| | 变更 | ✅ | ✅ | `verifyToken` + 前端禁用员工选择框 |
| | 删除资产 | ✅ | ❌ | 后端路由 `isAdmin` 保护 |
| | 回滚变更 | ✅ | ❌ | 后端 `isAdmin` + 前端 `v-if="isAdmin"` |

这份文档涵盖了我们目前权限系统的主要设计和实现细节。 