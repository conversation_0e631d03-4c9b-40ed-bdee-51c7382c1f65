const db = require('../models');
const path = require('path'); // [新增] 引入path模块
const Followup = db.Followup;
const Enterprise = db.Enterprise;
const Employee = db.Employee;

// 创建并保存一个新的跟进记录
exports.create = async (req, res) => {
  // 从请求体中获取数据
  const { enterprise_id, followup_time, situation, employee_id, remark } = req.body;

  // 基本的验证
  if (!enterprise_id || !followup_time || !situation || !employee_id) {
    return res.status(400).send({
      message: "企业ID、跟进时间、跟进情况和员工ID不能为空!"
    });
  }

  try {
    // 创建跟进记录对象
    const followup = {
      enterprise_id,
      followup_time,
      situation,
      employee_id,
      remark,
      // 如果有文件上传，保存其相对路径，例如 "uploads/attachment-166...jpg"
      attachment: req.file ? 'uploads/' + req.file.filename : null
    };

    const newFollowup = await Followup.create(followup);
    res.status(201).send(newFollowup);
  } catch (error) {
    res.status(500).send({
      message: error.message || "创建跟进记录时发生错误。"
    });
  }
};

// 根据企业ID获取所有跟进记录
exports.findAllByEnterprise = async (req, res) => {
  const { enterpriseId } = req.params;

  try {
    const data = await Followup.findAll({
      where: { enterprise_id: enterpriseId },
      // 连表查询，同时获取员工信息
      include: [{
        model: Employee,
        as: 'employee',
        attributes: ['name'] // 只获取员工的姓名
      }],
      order: [['followup_time', 'DESC']] // 按跟进时间降序排列
    });
    res.send(data);
  } catch (error) {
    res.status(500).send({
      message: error.message || `获取企业ID=${enterpriseId}的跟进记录时发生错误。`
    });
  }
};

// 更新一个指定ID的跟进记录
exports.update = async (req, res) => {
  const { id } = req.params;

  try {
    const num = await Followup.update(req.body, {
      where: { id: id }
    });

    if (num == 1) {
      res.send({
        message: "跟进记录更新成功。"
      });
    } else {
      res.status(404).send({
        message: `找不到ID=${id}的跟进记录，无法更新。`
      });
    }
  } catch (error) {
    res.status(500).send({
      message: `更新ID=${id}的跟进记录时发生错误。`
    });
  }
};

// 删除一个指定ID的跟进记录
exports.delete = async (req, res) => {
  const { id } = req.params;

  try {
    const num = await Followup.destroy({
      where: { id: id }
    });

    if (num == 1) {
      res.send({
        message: "跟进记录删除成功。"
      });
    } else {
      res.status(404).send({
        message: `找不到ID=${id}的跟进记录，无法删除。`
      });
    }
  } catch (error) {
    res.status(500).send({
      message: `删除ID=${id}的跟进记录时发生错误。`
    });
  }
};

/**
 * [新增] 下载跟进记录的附件
 */
exports.downloadAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const followup = await Followup.findByPk(id, { attributes: ['attachment'] });

    if (!followup || !followup.attachment) {
      return res.status(404).send({ message: '未找到附件文件。' });
    }

    const filename = path.basename(followup.attachment);
    const uploadsDir = path.join(__dirname, '..', '..', 'uploads');
    const filePath = path.join(uploadsDir, filename);

    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('下载跟进记录附件时出错:', err);
        if (!res.headersSent) {
          res.status(500).send({ message: '下载文件失败。' });
        }
      }
    });

  } catch (error) {
    console.error('处理跟进附件下载请求时出错:', error);
    if (!res.headersSent) {
      res.status(500).send({ message: '服务器内部错误。' });
    }
  }
}; 