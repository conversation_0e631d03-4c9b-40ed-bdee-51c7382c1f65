const followups = require("../controllers/followup.controller.js");
const router = require("express").Router();
const upload = require("../middleware/upload"); // 引入上传中间件
const { employee } = require('../middleware/auth'); // [!] 1. 引入员工认证中间件
const { checkOwnership } = require('../middleware/permissionChecks'); // [!] 1. 引入授权中间件
const db = require('../models'); // [!] 1. 引入数据库模型
const Followup = db.Followup;

// 创建一个新的跟进记录，需要登录
router.post("/", [employee.verifyEmployee, upload.single('attachment')], followups.create);

// 根据企业ID获取所有跟进记录，需要登录
router.get("/enterprise/:enterpriseId", employee.verifyEmployee, followups.findAllByEnterprise);

// 更新一个指定ID的跟进记录，需要登录且是所有者
router.put("/:id", [employee.verifyEmployee, checkOwnership(Followup), upload.single('attachment')], followups.update);

// 删除一个指定ID的跟进记录，需要登录且是所有者
router.delete("/:id", [employee.verifyEmployee, checkOwnership(Followup)], followups.delete);

// [新增] 下载指定ID的跟进记录附件
router.get('/:id/attachment/download', employee.verifyEmployee, followups.downloadAttachment);

module.exports = router; 