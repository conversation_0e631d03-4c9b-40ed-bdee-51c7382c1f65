<view class="container">
  <!-- 使用顶部导航栏组件 -->
  <nav-bar activeTab="{{activeTab}}" bindtabchange="handleTabChange" class="full-width-nav"></nav-bar>

  <!-- 产品海报区域 -->
  <view class="poster" 
        style="background: linear-gradient(135deg, #5e35b1 0%, #3949ab 60%, #1e88e5 100%);"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd">
    
    <!-- 背景元素 -->
    <view class="poster-background">
      <!-- 网格背景 -->
      <view class="bg-grid"></view>
      
      <!-- 连接线 -->
      <view class="connection-line line-1"></view>
      <view class="connection-line line-2"></view>
      <view class="connection-line line-3"></view>
      <view class="connection-line line-4"></view>
      
      <!-- 几何动效元素 -->
      <view class="bg-element circle-1"></view>
      <view class="bg-element circle-2"></view>
      <view class="bg-element square-1"></view>
      <view class="bg-element triangle-1"></view>
      <view class="bg-element dot-pattern-1"></view>
      <view class="bg-element dot-pattern-2"></view>
      <view class="bg-element line-pattern-1"></view>
      <view class="bg-element line-pattern-2"></view>
      <view class="bg-element glow-1"></view>
      
      <!-- 粒子效果 -->
      <view class="particles-container">
        <view class="particle p-1"></view>
        <view class="particle p-2"></view>
        <view class="particle p-3"></view>
        <view class="particle p-4"></view>
        <view class="particle p-5"></view>
        <view class="particle p-6"></view>
        <view class="particle p-7"></view>
        <view class="particle p-8"></view>
      </view>
    </view>
    
    <!-- 产品标题 -->
    <view class="product-title">
      <view class="title-main">{{posterTitle}}</view>
      <view class="title-divider"></view>
      <view class="title-sub">{{posterSubtitle}}</view>
      <view class="slogan">{{posterSlogan}}</view>
    </view>
    
    <!-- 促销卡片 -->
    <view class="promo-card-position">
      <promo-card initialShow="{{true}}" pageKey="{{productKey}}" bindjoinpromo="handleJoinPromo" themeColor="blue"></promo-card>
    </view>
    
    <!-- 底部羽化效果 -->
    <view class="poster-bottom-fade"></view>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content" id="detail">
    <!-- 主标题区域 -->
    <view class="hero-banner">
      <view class="hero-content">
        <view class="hero-title">从管控到赋能，创造商业价值</view>
        <view class="divider"></view>
        <view class="hero-subtitle">好业财面向成长型企业，以数智化经营管理为核心，通过数据化精准运营，为企业提供全面信息化解决方案的在线云应用</view>
      </view>
    </view>
    
    <!-- 价值主张区域 -->
    <view class="value-proposition">
      
      <!-- 角色展示 -->
      <view class="roles-showcase">
        <scroll-view class="roles-scroll" scroll-x="true">
          <view class="role-card">
            <view class="role-avatar boss">
              <text class="iconfont icon-gongwenbao"></text>
            </view>
            <view class="role-name">企业老板</view>
            <view class="role-desc">全局掌控企业经营情况，实时查看销售、库存、应收应付等关键指标，辅助经营决策</view>
          </view>
          <view class="role-card">
            <view class="role-avatar finance">
              <text class="iconfont icon-renminbi"></text>
            </view>
            <view class="role-name">财务人员</view>
            <view class="role-desc">告别繁琐的手工录入，业务数据自动流转到财务，凭证自动生成，报表一键导出</view>
          </view>
          <view class="role-card">
            <view class="role-avatar warehouse">
              <text class="iconfont icon-shangyun"></text>
            </view>
            <view class="role-name">仓库管理员</view>
            <view class="role-desc">实时掌控库存动态，手持PDA出入库操作简单高效，智能库存预警，避免缺货积压</view>
          </view>
          <view class="role-card">
            <view class="role-avatar salesman">
              <text class="iconfont icon-piaoju"></text>
            </view>
            <view class="role-name">销售人员</view>
            <view class="role-desc">随时随地移动端开单，灵活报价，客户信息一目了然，提升成交效率</view>
          </view>
          <view class="role-card">
            <view class="role-avatar project">
              <text class="iconfont icon-tubiaozhutu"></text>
            </view>
            <view class="role-name">项目经理</view>
            <view class="role-desc">全程跟踪项目进度，成本控制精准到位，确保项目按时按质按预算完成</view>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 一套软件搞定 -->
    <view class="all-in-one">
      <view class="all-in-one-title">一套软件满足全方位需求，解决多套软件并行的弊端</view>
      <!-- <view class="all-in-one-subtitle">业财一体化解决方案，助力企业数字化转型</view> -->
    </view>
    
    <!-- 功能模块展示 -->
    <view class="modules-showcase">
      <!-- <view class="modules-title">业财票税一体化管理</view>
      <view class="modules-subtitle">进销存云+财务云+项目云满足业务需求</view> -->
      <view class="modules-grid">
        <view class="module-item">
          <view class="module-icon purchase">
            <text class="iconfont icon-goods_light"></text>
          </view>
          <view class="module-name">采购管理</view>
          <view class="module-desc">采购单、收货单、采购退货单</view>
        </view>
        <view class="module-item">
          <view class="module-icon sales">
            <text class="iconfont icon-hetong"></text>
          </view>
          <view class="module-name">销售管理</view>
          <view class="module-desc">销售单、发货单、销售退货单</view>
        </view>
        <view class="module-item">
          <view class="module-icon inventory">
            <text class="iconfont icon-cangchucangku"></text>
          </view>
          <view class="module-name">库存管理</view>
          <view class="module-desc">库存预警、库存盘点、调拨单</view>
        </view>
        <view class="module-item">
          <view class="module-icon receivable">
            <text class="iconfont icon-renminbi"></text>
          </view>
          <view class="module-name">往来资金</view>
          <view class="module-desc">应收应付、收付款单、费用单</view>
        </view>
        <view class="module-item">
          <view class="module-icon business">
            <text class="iconfont icon-tubiaozhutu"></text>
          </view>
          <view class="module-name">经营管理</view>
          <view class="module-desc">销售分析、利润分析、客户分析</view>
        </view>
        <view class="module-item">
          <view class="module-icon report">
            <text class="iconfont icon-shuzihua"></text>
          </view>
          <view class="module-name">总账报表</view>
          <view class="module-desc">会计凭证、财务报表、成本核算</view>
        </view>
        <view class="module-item">
          <view class="module-icon invoice">
            <text class="iconfont icon-piaoju"></text>
          </view>
          <view class="module-name">发票税务</view>
          <view class="module-desc">开票管理、税务申报、进项发票</view>
        </view>
        <view class="module-item">
          <view class="module-icon collaboration">
            <text class="iconfont icon-lianmenglian"></text>
          </view>
          <view class="module-name">企业协同</view>
          <view class="module-desc">审批流程、消息通知、权限管理</view>
        </view>
        <view class="module-item">
          <view class="module-icon expense">
            <text class="iconfont icon-fapiao"></text>
          </view>
          <view class="module-name">小微报销</view>
          <view class="module-desc">费用报销、发票管理、预算管控</view>
        </view>
      </view>
    </view>
    
    <!-- 业财融合区域 -->
    <view class="all-in-one">
      <view class="all-in-one-title">实现财务、进销存、线上商城、零售门店、项目合同、工贸生产全面管理</view>
      <!-- <view class="all-in-one-subtitle">业财一体化解决方案，助力企业数字化转型</view> -->
    </view>

    <view class="feature-section integration">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">业财融合</view>
        <view class="section-desc">提升企业经营效率</view>
      </view>
      <view class="feature-content">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">业务和财务不再孤立，解决业财跨数据孤立问题</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">业务单据实时智能生成凭证，减少手工录入</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">业务单据智能流转到财务，数据无缝对接</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">银企云联，无需登录网银，自动对账</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">三大报表自动生成，财务状况一目了然</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature1.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 进销存/供应链区域 -->
    <view class="feature-section supply-chain">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">进销存/供应链</view>
        <view class="section-desc">高效做生意</view>
      </view>
      <view class="feature-content reverse">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">聚焦供应链管理，适配多行业特性</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">随时随地，多端报价开单做生意</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">灵活的价格体系化，快速响应市场变化</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">实时可视化经营图表，辅助经营决策</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">车销访销，随时随地开展业务</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature2.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 线上商城区域 -->
    <view class="feature-section online-store">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">专属在线订货商城</view>
        <view class="section-desc">进销存一体化</view>
      </view>
      <view class="feature-content">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">模板轻松搭建，支持多种行业模板</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">告别错单漏单，支持审单、改价、改单</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">营销促销帮手，支持满赠、满减、打折等</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">客户差异化管理，支持客户等级、客户折扣、协议价</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">应收款处理更便捷，支持先货后款，按月结算</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature3.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 项目合同管理区域 -->
    <view class="feature-section project-contract">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">项目合同管理</view>
        <view class="section-desc">项目全过程管理一体化</view>
      </view>
      <view class="feature-content reverse">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">按项目核算收入、成本、费用、利润，严格管理项目往来、资金、库存</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">预算与成本管理，预算执行跟踪表、预算成本分析对比表、预算跟踪表</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">项目进度及结算回款一体化，掌控放心</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">人料机费核算一体化，成本控制，心中有数</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">账单收付款在线确认，业务与财务数据互通</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature4.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 费控报销区域 -->
    <view class="feature-section expense-control">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">费控报销</view>
        <view class="section-desc">智能报销风险易管控</view>
      </view>
      <view class="feature-content">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">打通报销-支付-记账全流程，发票电子化背景下，为企业报销业务提效率</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">通过扫一扫、拍照识别、电票上传、滴滴取票等七种方式添加发票</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">报销发票上传时自动验真查重</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">报销单根据发票自动生成，免去手工填写报销单繁琐</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">企业费用管理全在线，费用精细管控，支出合规、透明</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature5.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 零售/连锁门店区域 -->
    <view class="feature-section retail-chain">
      <view class="feature-header">
        <view class="section-icon"></view>
        <view class="section-title">零售/连锁门店</view>
        <view class="section-desc">智慧经营一体化</view>
      </view>
      <view class="feature-content reverse">
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">采购、库存、门店POS收银、全面财务核算，为零售及连锁门店经营赋能</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">多端POS收银，提升收银效率</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">线上线下一体，线上商城24小时接单</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">实体店与线上商城，会员、积分、储值、优惠券等权益互通</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">提销量，搞促销，30多种营销手段，随需选择不重样</view>
          </view>
          <view class="feature-item">
            <view class="feature-dot"></view>
            <view class="feature-text">门店经营利润分析，库存、欠款数据异常预警</view>
          </view>
        </view>
        <view class="feature-image">
          <image src="https://mshop.bogoo.net/hyc_feature6.png" mode="widthFix"></image>
        </view>
      </view>
    </view>
    
    <!-- 行业解决方案 -->
    <view class="industry-solutions">
      <view class="industry-title">行业解决方案</view>
      
      <!-- 优化后的行业选项卡布局 -->
      <view class="industry-tabs-container">
        <scroll-view scroll-x="true" class="industry-tabs-scroll" show-scrollbar="false" enhanced="true">
          <view class="industry-tabs">
            <view class="industry-tab {{activeIndustry == 0 ? 'active' : ''}}" bindtap="switchIndustry" data-index="0">食品行业</view>
            <view class="industry-tab {{activeIndustry == 1 ? 'active' : ''}}" bindtap="switchIndustry" data-index="1">日用百货</view>
            <view class="industry-tab {{activeIndustry == 2 ? 'active' : ''}}" bindtap="switchIndustry" data-index="2">设备配件</view>
            <view class="industry-tab {{activeIndustry == 3 ? 'active' : ''}}" bindtap="switchIndustry" data-index="3">建筑安装</view>
            <view class="industry-tab {{activeIndustry == 4 ? 'active' : ''}}" bindtap="switchIndustry" data-index="4">五金建材</view>
            <view class="industry-tab {{activeIndustry == 5 ? 'active' : ''}}" bindtap="switchIndustry" data-index="5">酒水饮料</view>
            <view class="industry-tab {{activeIndustry == 6 ? 'active' : ''}}" bindtap="switchIndustry" data-index="6">医疗器械</view>
            <view class="industry-tab {{activeIndustry == 7 ? 'active' : ''}}" bindtap="switchIndustry" data-index="7">服装鞋帽</view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 优化行业详情卡片 -->
      <view class="industry-detail">
        <view class="industry-header">
          <text class="industry-name">{{industryData[activeIndustry].name}}</text>
          <view class="industry-badge"></view>
        </view>
        <view class="industry-desc">{{industryData[activeIndustry].desc}}</view>
        
        <!-- 挑战与特点 -->
        <view class="industry-section-title">行业痛点与特点</view>
        <view class="industry-challenges">
          <view class="challenge-item" wx:for="{{industryData[activeIndustry].challenges}}" wx:key="index">
            <view class="check-icon"></view>
            <text>{{item}}</text>
          </view>
        </view>
        
        <!-- 解决方案 -->
        <view class="industry-section-title">好业财解决方案</view>
        <view class="industry-solution-desc">{{industryData[activeIndustry].solution}}</view>
        
        <!-- 咨询按钮 -->
        <view class="industry-action">
          <button class="consult-btn" open-type="contact">咨询行业方案</button>
        </view>
      </view>
    </view>
    
    <!-- 选择理由 -->
    <view class="choose-reason">
      <view class="reason-title">为什么选择好业财</view>
      <view class="reason-cards">
        <view class="reason-card">
          <view class="reason-icon brand">
            <text class="iconfont icon-wendingkekao"></text>
          </view>
          <view class="reason-content">
            <view class="reason-name">用友品牌品质双保障</view>
            <view class="reason-desc">用友集团畅捷通，致力于为小微企业提供专业管理软件20年，凭借对小微企业深刻理解和过硬产品品质，成为国内小微企业首选品牌</view>
          </view>
        </view>
        <view class="reason-card">
          <view class="reason-icon savings">
            <text class="iconfont icon-renminbi"></text>
          </view>
          <view class="reason-content">
            <view class="reason-name">省心省钱</view>
            <view class="reason-desc">按需订阅，按年付费,最低每天仅需8.16元；产品即买即用,无需安装下载，用户快速实现上云，产品自动更新到最新版本</view>
          </view>
        </view>
        <view class="reason-card">
          <view class="reason-icon service">
            <text class="iconfont icon-peoplefill"></text>
          </view>
          <view class="reason-content">
            <view class="reason-name">7*24小时智能云服务</view>
            <view class="reason-desc">好业财提供全天候客户服务支持，确保您的业务运营不受中断，专业团队随时待命解决您遇到的各种问题</view>
          </view>
        </view>
        <view class="reason-card">
          <view class="reason-icon security">
            <text class="iconfont icon-yinzhangrenzheng"></text>
          </view>
          <view class="reason-content">
            <view class="reason-name">"银行级"数据安全</view>
            <view class="reason-desc">采用多重加密技术保障数据传输安全，定期备份确保数据不丢失，严格的访问控制和权限管理，保障您的商业数据绝对安全</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- FAQ部分 -->
    <view class="faq-section">
      <view class="faq-title">常见问题</view>
      <view class="faq-item" wx:for="{{faqData}}" wx:key="index">
        <view class="faq-question" bindtap="toggleFaq" data-index="{{index}}">
          <text>{{item.question}}</text>
          <view class="arrow {{faqExpanded[index] ? 'up' : 'down'}}"></view>
        </view>
        <view class="faq-answer" wx:if="{{faqExpanded[index]}}">
          {{item.answer}}
        </view>
      </view>
    </view>
    
    <!-- 体验咨询区域 -->
    <view class="cta-section">
      <view class="cta-content">
        <view class="cta-title">立即开启数智化经营管理</view>
        <view class="cta-desc">好业财，帮助企业实现业财一体化管理</view>
        <view class="cta-buttons">
          <button class="cta-btn outline" bindtap="makePhoneCall">
            <text class="btn-text">电话咨询</text>
          </button>
          <button class="cta-btn primary" open-type="contact">微信咨询</button>
        </view>
      </view>
    </view>
    <!-- 底部留白 -->
    <view style="height: 40rpx;"></view>

  </view>

  <!-- 使用底部导航栏组件 -->
  <bottom-nav currentTab="hot" theme="blue" bindbottomnav="onBottomNavEvent"></bottom-nav>
  
  <!-- 添加悬浮咨询按钮 -->
  <float-consult positionKey="pdhyc"></float-consult>
</view> 