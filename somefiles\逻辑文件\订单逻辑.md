
# 一级菜单：订单管理
# 二级菜单：新增产品订单、新增服务订单、订单审核、产品订单（审核后的订单）、服务订单（审核后的订单）

- 不管什么订单，不管是web端员工新增的订单，还是小程序商城端用户自己下的订单，新增购都先流转到“订单审核”的界面。
- 订单审核（实际是集中多个操作功能的页面）：提供补全订单信息、修改订单信息、删除订单等各种各样的功能。
- 只有审核通过后才会分别流转到“产品订单”和“服务订单”界面。
- 一般是付款前的订单都会停留在审核订单的页面里，付款后的订单，没有任何问题的订单，会通过审核。付款的状态，目前也是人工修改。
- 目前都是人工审核，后期可能添加自动审核的功能。
- 产品订单和服务订单,主要是展示已经支付完结的订单的页面，提供各种统计的功能，不提供订单的修改、删除等功能，这个页面属于统计端口！

## 服务订单（一般都是先付款，后服务）
### 表头
- **订单ID**：SO开头
- **企业ID**：
- **资产ID**：
- **用户ID**：
- **创建方式**：手工创建、用户创建（即后期用户在小程序商城里自己下的订单）
- **订单类型**：普通订单、续费订单、变更订单（针对增购账套、人数、功能、版本的订单）
- **订单标准金额**：
- **订单实付金额**：
- **是否合伙人订单**：是
- **合伙人ID**：
- **分润比例**
- **分润金额**
- **分润状态**：已发放/未发放
### 表体（两个页签/表体）
#### 服务明细（可多行服务明细）
- **服务名**：下拉选择：实施服务、售后服务、sps服务
- **金额**：
- **关联产品订单号**：手工维护
- **备注**：
#### 订单附件（可多行附件明细）
- **附件名称**：
- **文件类型**：合同、发票、其他
- **文件大小**：
- **上传人**：当前登录的员工
- **上传时间**：
- **备注**：
### 表尾
- **备注**：
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？

## 产品订单（首次购买、续费）
### 表头
- **订单ID**：PO开头
- **企业ID**：
- **资产ID**：
- **用户ID**：
- **创建方式**：手工创建、用户创建
- **订单类型**：普通订单、续费订单、变更订单（针对增购账套、人数、功能、版本的订单）
- **订单标准金额**：
- **订单实付金额**：
- **税额**：手工维护
- **发票类型**：专票、普票、不开票
- **支付状态**：已支付、待支付
- **支付方式**：在线、对公
- **支付时间**：手工维护
- **是否合伙人订单**：是
- **合伙人ID**：
- **分润比例**
- **分润金额**
- **分润状态**：已发放/未发放
### 表体（两个页签/表体）
#### 产品信息(只有个一个产品相关的信息，不会出现一个订单里多个产品的情况！)
- **产品ID-版本号**：
- **使用人数**：
- **账套数**：
- **购买时长**：单位为月
- **勾选产品功能**：根据“产品模块里维护好的：每个产品对应的功能的价格”计算
- **价格信息-产品标准价**：可手工修改
- **价格信息-折扣**：百分比
- **价格信息-其他优惠**：金额
- **价格信息-实付价格**：
- **其他活动**：手工维护，如：送礼品
#### 订单附件（可多行附件明细）
- **附件名称**：
- **文件类型**：合同、发票、其他
- **文件大小**：
- **上传人**：当前登录的员工
- **上传时间**：
- **备注**：
### 表尾
- **备注**：
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？

## 产品订单（变更/增购（账套、人数、功能、版本）-资产需同时手工变更）
### 表头
- **订单ID**：
- **企业ID**：
- **资产ID**：
- **用户ID**：
- **创建方式**：手工创建、用户创建
- **订单类型**：普通订单、续费订单、变更订单（选这个）
- **订单标准金额**：
- **订单实付金额**：
- **税额**：
- **发票类型**：
- **支付状态**：
- **支付方式**：
- **支付时间**：

- **是否合伙人订单**：是
- **合伙人ID**
- **分润比例**
- **分润金额**
- **分润状态**：已发放/未发放
### 表体（两个页签/表体）
#### 产品信息(填写增购后的最终的完整信息)
- **产品ID-版本号**：
- **使用人数**：增购前1人，本次增加2人，那这个字段填写：1+2 = 3
- **账套数**：增购前3个账套，本次增加2个账套，那这个字段填写：3+2 = 5
- **购买时长**：单位为月（增购后总共的时长）
- **勾选产品功能**：增购后总共的功能
- **价格信息-产品标准价**：=产品维护的默认的标准价+（总的增购账套价格+总的增购用户价格+总的增购功能价格）可手工修改
- **价格信息-折扣**：百分比
- **价格信息-其他优惠**：金额
- **价格信息-实付价格**：产品标准价*折扣-其他优惠
- **其他活动**：手工维护，如：送礼品
#### 订单附件（可多行附件明细）
- **附件名称**：
- **文件类型**：合同、发票、其他
- **文件大小**：
- **上传人**：当前登录的员工
- **上传时间**：
- **备注**：
### 表尾
- **备注**：手工维护变更的字段，以及变更前后的信息
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？

# 注意事项：
- 后期对接小程序商城，小程序中主要提供：用户首次购买（产品订单）、续购产品（产品订单）、续购sps的服务订单。主要是处理价格相对固定的操作。
- web端主要提供：变更/增购（账套、人数、功能、版本）订单的操作。主要是处理价格不固定的操作，web端只提供给自己公司员工管理订单、资产、用户、产品、客户等操作使用。

# 新增订单时：
## 填了企业ID，若有与之关联的用户ID、资产ID信息可过滤出，若没有则用户ID、资产ID可填可不填
## 填了资产ID，企业ID自动带出（因为新增资产时必须绑定企业），若企业关联了用户ID可过滤出，没有可填可不填
## 填了用户ID，若该用户没有关联任何企业和资产，则（企业ID、资产ID）可填可不填
## 注意：企业ID、用户ID，不可同时为空

逻辑是：
填了企业ID → 过滤出关联的用户ID、资产ID
填了资产ID → 自动带出企业ID，过滤出关联的用户ID
填了用户ID → 过滤出关联的企业ID、资产ID，如果用户没有关联企业和资产，则企业ID、资产ID可填可不填
约束条件：企业ID、用户ID不可同时为空
显示要求：带出的是名字，不是ID
注意：填写订单信息时，企业、资产、用户3个字段的填写没有先后顺序。


src/
  views/
    order/
      pages/           
        OrderReviewList.vue    
        ProductOrderList.vue  
        ProductOrderForm.vue  
        ServiceOrderList.vue
        ServiceOrderForm.vue
      components/          
        OrderHeader.vue      # 订单通用表头
        ProductInfo.vue      # 产品订单信息组件
        ServiceInfo.vue      # 服务订单信息组件
        OrderAttachment.vue  # 附件管理组件
        OrderCommission.vue
        ReviewActions.vue     # 审核页面订单操作组件

# 页面：      
## 订单审核列表页面（OrderReviewList.vue）
    - 作用：展示所有待审核的订单（包括产品订单和服务订单），提供修改、删除、审核操作入口。
    - 字段：订单号、创建方式（手工创建/用户创建）、订单类型（产品订单/服务订单）、企业ID、用户ID、资产ID、订单标准金额、实付金额、支付状态、制单时间。
    - 操作：点击“审核”进入对应的订单审核详情页面。
    - 特殊功能：按订单类型筛选、快速搜索订单、批量审核操作。
## 产品订单列表页面（ProductOrderList.vue）
    - 作用：展示所有已审核通过的产品订单（已支付完结），用于统计和查看，不提供修改功能。
    - 字段：订单号、企业ID、资产ID、用户ID、创建方式、订单类型、订单标准金额、实付金额、产品名称、使用人数、账套数、支付状态、支付方式、支付时间、是否合伙人订单、分润状态、制单人、制单时间。
## 产品订单表单页面（ProductOrderForm.vue）
    - 作用：用于创建/编辑/查看产品订单
    - 在订单审核流程中，编辑操作也在这个页面，但是处于审核模式。
    - 三种模式：创建模式：员工手工创建新订单。审核模式：审核待处理的订单（可编辑）。查看模式：查看已完成的订单。
    - 特殊功能：产品选择器、功能模块选择、价格自动计算。
    - 这个页面会包含表头、产品信息、附件等组件。
## 服务订单列表页面（ServiceOrderList.vue）
    - 作用：展示所有已审核通过的服务订单（已支付完结），用于统计和查看，不提供修改功能。
    - 字段：订单号、企业ID、资产ID、用户ID、创建方式、订单类型、订单标准金额、实付金额、服务类型、服务金额、关联订单号、是否合伙人订单、分润状态、制单人、制单时间。
## 服务订单表单页面（ServiceOrderForm.vue）
    - 作用：用于创建/编辑/查看服务订单
    - 在订单审核流程中，编辑操作也在这个页面，但是处于审核模式。
    - 三种模式：创建模式：员工手工创建新服务订单。审核模式：审核待处理的订单（可编辑）。查看模式：查看已完成的订单。
    - 这个页面会包含表头、服务明细、附件等组件。
 
 # 组件：
## 订单表头组件（OrderHeader.vue）
    - 作用：展示和编辑订单的公共表头信息（企业ID、资产ID、用户ID、创建方式、订单类型等）。
    - 产品订单和服务订单都会使用这个组件。
    - 功能：企业ID/资产ID/用户ID联动选择。自动填充关联信息（如选择资产自动带出企业）。订单类型选择（普通/续费/变更）。支付信息录入。
## 产品信息组件（ProductInfo.vue）
    - 作用：用于产品订单的表体，包含产品选择（带版本信息）、使用人数（含基础人数显示）、账套数（含基础账套显示）、购买时长、功能模块选择（带价格）、价格计算等。这个组件的布局要严格按照我提供的图片。
## 服务明细组件（ServiceInfo.vue）
    - 作用：用于服务订单的表体，包含服务明细行（服务名、金额、关联产品订单号、备注）的管理（增删改）。服务类型选择（实施/售后/SPS）。手动关联产品订单。
## 订单附件组件（OrderAttachments.vue）
    - 作用：管理订单的附件（可多行），包括上传、查看、删除附件。产品订单和服务订单都会使用。文件类型分类（合同/发票/其他）。附件支持预览和下载！
## 订单分润信息组件（OrderCommission.vue）（可选，如果表头中分润信息复杂可以独立出来）
    - 作用：展示和编辑分润相关信息（是否合伙人订单、分润比例、分润金额、分润状态）。产品订单和服务订单都会使用。
## 订单操作栏组件（ReviewActions.vue）
    - 作用：提供审核相关的操作按钮
    - 功能：审核通过按钮。拒绝审核按钮。补全信息按钮。删除订单按钮
    - 使用场景：只在审核模式下显示。

# 页面和组件的关系：
 - （ProductOrderList.vue）、（ServiceOrderList.vue）和（OrderReviewList.vue）是独立的页面，它不需要嵌套组件，直接展示订单列表。
 - 产品订单表单页面（ProductOrderForm.vue）由以下组件构成：
      <OrderHeader>  // 表头
      <OrderCommission.vue>
      <ProductInfo> // 产品信息
      <OrderAttachments> // 附件
      <ReviewActions> // 操作按钮（如果是审核模式 → 使用审核操作栏组件）
 - 服务订单表单页面（ServiceOrderForm.vue）由以下组件构成：
      <OrderHeader>  // 表头
      <OrderCommission.vue>
      <ServiceInfo> // 服务明细
      <OrderAttachments> // 附件
      <ReviewActions> // 操作按钮（如果是审核模式 → 使用审核操作栏组件）

# 关于订单审核列表页面如何同时展示产品订单和服务订单？
 我们可以在订单审核列表页面中，通过一个接口获取所有待审核的订单（包括产品订单和服务订单）。后端需要提供一个统一的视图或者联合查询。
 展示字段（共有的字段）：
   - 订单号（order_id）
   - 创建方式（creation_method）：手工创建/用户创建
   - 订单类型（order_type）：产品订单/服务订单
   - 企业ID（enterprise_id）
   - 用户ID（user_id）
   - 订单标准金额（standard_amount）
   - 订单实付金额（actual_amount）
   - 制单时间（created_at）
 注意：产品订单和服务订单的表结构不同，如果服务订单没有某些字段（比如税额），那么在服务订单中这些字段就为空。
 在订单审核列表页面，我们只展示两种订单都有的公共字段。这样就能在一个列表中展示。

 总结：
 1. 订单审核列表页面：展示待审核的两种订单（公共字段）。
 2. 订单审核详情页面：根据订单类型加载产品订单表单页面或服务订单表单页面，并设置为审核模式（只能补全信息、修改，不能删除，除非拒绝审核）。
 3. 产品订单表单页面：用于新增、编辑和审核产品订单，包含产品信息组件。
 4. 服务订单表单页面：用于新增、编辑和审核服务订单，包含服务明细组件。

 订单审核列表页面 (OrderReview.vue)
├── 点击"审核"按钮
│   ├── 如果是产品订单 → 打开产品订单表单页面 (ProductOrderForm.vue)
│   │   ├── 使用订单表头组件 (OrderHeader)
│   │   ├── 使用产品信息组件 (ProductInfo)
│   │   ├── 使用订单附件组件 (OrderAttachment)
│   │   └── 如果是审核模式 → 使用审核操作栏组件 (ReviewActions)
│   │
│   └── 如果是服务订单 → 打开服务订单表单页面 (ServiceOrderForm.vue)
│       ├── 使用订单表头组件 (OrderHeader)
│       ├── 使用服务明细组件 (ServiceInfo)
│       ├── 使用订单附件组件 (OrderAttachment)
│       └── 如果是审核模式 → 使用审核操作栏组件 (ReviewActions)
│
├── 审核通过的产品订单 → 出现在产品订单列表页面 (ProductOrderList.vue)
└── 审核通过的服务订单 → 出现在服务订单列表页面 (ServiceOrderList.vue)

# 订单审核列表如何展示不同订单类型
在订单审核列表页面，可以同时展示产品订单和服务订单，通过以下方式区分：
## 共用字段展示：
- 公共字段为：订单ID、企业ID、资产ID、用户ID、创建方式、订单类型、订单标准金额、订单实付金额、是否合伙人订单、合伙人ID、分润比例、分润金额、分润状态。

例如：
字段	产品订单	服务订单
订单号	OR20230001	SR20230001
订单类型	产品订单	服务订单
企业名称	某某科技有限公司	某某科技有限公司
创建方式	手工创建/用户创建	手工创建/用户创建
订单金额	¥5,800.00	¥2,000.00
支付状态	待支付/已支付	待支付/已支付
......更多公共字段......
创建时间	2023-05-10 14:30	2023-05-11 10:15
## 视觉区分：
使用不同图标：📦 表示产品订单，🔧 表示服务订单
行背景色轻微差异：产品订单浅蓝色，服务订单浅绿色
订单类型列明确标注"产品"或"服务"
## 筛选功能：
顶部筛选栏可以按订单类型筛选
可以按支付状态筛选
可以按创建方式筛选


# 订单流转示意图
新订单创建（小程序或手工）
       ↓
[订单审核列表] → 待审核状态
       ↓
员工审核处理 → 通过审核        → 拒绝审核（返回修改或删除）
       ↓
  ┌───────┴───────┐
  ↓               ↓
[产品订单列表]   [服务订单列表] → 已完成状态（只读展示）

# 产品订单 vs 服务订单差异：
特性：	产品订单	服务订单
核心内容：	产品、人数、账套、功能	服务类型、金额、关联订单
价格计算：	复杂（基于配置自动计算）	简单（直接输入金额）
表体结构：	产品信息+附件	服务明细+附件
变更处理：	需要更新资产信息	不需要更新资产

# OrderHeader.vue 组件如何处理不同订单类型？
graph TD
    A[OrderHeader.vue] --> B{订单类型判断}
    B -->|产品订单| C[显示产品订单特有字段]
    B -->|服务订单| D[显示服务订单特有字段]
    C --> E[显示税额/发票类型等]
    D --> F[隐藏税额/发票类型等]
    A --> G[公共字段]
    G --> H[企业ID/资产ID/用户ID]
    G --> I[创建方式/订单类型]
    G --> J[订单金额/合伙人信息]
    G --> K[更多公共字段......]
通过 props 传入 order_category 参数（'product' 或 'service'）
