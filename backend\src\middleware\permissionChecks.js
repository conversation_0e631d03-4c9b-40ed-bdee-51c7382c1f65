//这个文件将专门存放我们所有关于权限判断的逻辑。
//这个中间件的核心思想是创建一个“工厂函数”，我们称之为 checkOwnership。
// 它会接收一个数据库模型（比如“企业模型”）作为参数，然后生成一个定制化的中间件函数。
// 这个中间件会自动检查发出请求的用户是否是该条数据的所有者（负责人）。

const db = require('../models');

/**
 * 权限检查中间件工厂函数
 * @param {Model} Model - Sequelize 模型 (例如: db.Enterprise, db.Order)
 * @param {string} [ownerForeignKey='employee_id'] - 模型中代表所有者的外键字段名
 * @returns {function} - 返回一个Express中间件函数
 */
const checkOwnership = (Model, ownerForeignKey = 'employee_id') => {
  return async (req, res, next) => {
    try {
      // 规则1：如果是管理员，直接拥有所有权限，跳过检查
      if (req.user && req.user.role === 'admin') {
        console.log('管理员权限，跳过所有权检查');
        return next();
      }

      const resourceId = req.params.id;
      const userId = req.user.id;

      console.log('权限检查:', {
        resourceId,
        userId,
        userRole: req.user.role,
        ownerForeignKey,
        modelName: Model.name
      });

      // 检查请求中是否包含必要的ID
      if (!resourceId || !userId) {
        console.log('缺少必要的ID信息');
        return res.status(400).json({ message: '错误：请求中缺少必要的ID信息。' });
      }

      // 规则2：检查操作的资源是否存在
      const resource = await Model.findByPk(resourceId);
      if (!resource) {
        console.log('资源不存在:', resourceId);
        return res.status(404).json({ message: '错误：找不到需要操作的资源。' });
      }

      console.log('资源信息:', {
        resourceId,
        ownerId: resource[ownerForeignKey],
        currentUserId: userId,
        ownerForeignKey
      });

      // 规则3：检查当前用户是否是资源的所有者
      if (resource[ownerForeignKey] === userId) {
        console.log('权限检查通过：用户是资源所有者');
        return next(); // 是所有者，允许操作
      }

      // 如果以上规则都不满足，则判定为无权限
      console.log('权限检查失败：用户不是资源所有者');
      return res.status(403).json({ message: '禁止访问：您没有权限执行此操作。' });

    } catch (error) {
      console.error('权限检查中间件发生错误:', error);
      return res.status(500).json({ message: '服务器内部错误' });
    }
  };
};

module.exports = {
  checkOwnership
}; 

//(负责授权)：它的工作是“检查门禁卡”。
// 在确认了你的身份之后，当你尝试进入一个具体的办公室（比如修改某条企业数据）时，checkOwnership 中间件就会启动。
// 它会检查你手里的“门禁卡”（req.user.id）是否有权限打开这间“办公室”（这条数据）的门。
// 它关心的是“你有没有权限做这件事？”。
// authJwt.js 回答 “你是谁？”
// permissionChecks.js 回答 “你能做什么？”