// 引入我们新的模型调度中心，它包含了所有模型和关联关系
const db = require('../models');
const { generateSequentialId, generateEnterpriseId } = require('../utils/id_helper'); // 引入新函数
const { Op } = require('sequelize');
const path = require('path'); // [新增] 引入path模块
const Enterprise = db.Enterprise;
const Employee = db.Employee;
const User = db.User;
const Followup = db.Followup; // [!] 1. 引入 Followup 模型
const Asset = db.Asset;       // [!] 2. 引入 Asset 模型
const OrderHead = db.OrderHead; // [新增] 引入 OrderHead 模型
const Product = db.Product;   // [新增] 引入 Product 模型
const upload = require('../middleware/upload');

/**
 * [新增] 获取下一个可用的企业ID
 */
exports.getNextEnterpriseId = async (req, res) => {
  try {
    const nextId = await generateEnterpriseId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个企业ID时出错:', error);
    res.status(500).json({ message: '生成企业ID失败', error: error.message });
  }
};

/**
 * 创建一个新的企业
 */
exports.createEnterprise = async (req, res) => {
  try {
    const enterpriseData = req.body;

    // 如果用户没有手动提供 enterprise_id，我们就自动生成一个
    if (!enterpriseData.enterprise_id) {
      enterpriseData.enterprise_id = await generateEnterpriseId();
    } else {
      // 如果用户手动提供了，我们要检查它是否唯一
      const existing = await Enterprise.findOne({ where: { enterprise_id: enterpriseData.enterprise_id } });
      if (existing) {
        return res.status(409).json({ message: `企业ID '${enterpriseData.enterprise_id}' 已存在，请使用其他ID。` });
      }
    }

    // 如果有文件上传，则保存文件路径
    if (req.file) {
      // 核心修复：使用 'uploads/' + 文件名 的方式保存相对URL路径
      enterpriseData.license_image = 'uploads/' + req.file.filename;
    }
    
    const enterprise = await Enterprise.create(enterpriseData);
    res.status(201).json(enterprise);
  } catch (error) {
    console.error('创建企业时出错:', error);
    // [增强] 处理 Sequelize 验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '创建企业失败', error: error.message });
  }
};

/**
 * 获取所有企业信息，并包含关联的员工和用户信息
 */
exports.getAllEnterprises = async (req, res) => {
  try {
    const { q, userId } = req.query;
    let where = {};

    // 权限控制：根据用户类型过滤企业
    if (req.user) {
      if (req.user.type === 'user') {
        // 普通用户只能看到自己绑定的企业
        where.user_id = req.user.id;
      } else if (req.user.type === 'employee' && req.user.role !== 'admin') {
        // 非管理员员工只能看到自己负责的企业
        where.employee_id = req.user.id;
      }
      // 管理员可以看到所有企业，不添加额外过滤条件
    }

    if (q) {
      // 1. [优化] 支持按企业名称或联系人模糊搜索
      where[Op.or] = [
        { name: { [Op.like]: `%${q}%` } },
        { contact_person: { [Op.like]: `%${q}%` } }
      ];
    }

    // 如果查询参数中指定了userId，则覆盖权限控制（管理员和员工都可以查询）
    if (userId && req.user && (req.user.role === 'admin' || req.user.type === 'employee')) {
      where.user_id = userId;
    }

    const enterprises = await Enterprise.findAll({
      where,
      // 2. [核心修复] 同时关联查询 Employee 和 User 模型
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'name'] // 只查询员工的id和name
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name'] // 只查询用户的id和name
        }
      ],
      order: [['createdAt', 'DESC']] // 按创建时间降序排序
    });
    res.status(200).json(enterprises);
  } catch (error) {
    res.status(500).json({ message: "获取企业列表失败", error: error.message });
  }
};

/**
 * 根据ID获取单个企业信息
 */
exports.getEnterpriseById = async (req, res) => {
  try {
    const { id } = req.params; // 从URL参数中获取企业ID (例如 /api/enterprises/1)
    const enterprise = await Enterprise.findByPk(id, { // 使用findByPk通过主键查找
      include: [ // 同样包含关联信息
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'name', 'mobile']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'name', 'nickname'] // 包含user.id
        }
      ]
    });

    if (enterprise) {
      res.status(200).json(enterprise);
    } else {
      // 如果根据ID找不到对应的企业
      res.status(404).json({ message: '未找到指定ID的企业' });
    }
  } catch (error) {
    console.error('获取单个企业信息时出错:', error);
    res.status(500).json({ message: '获取企业信息失败，请查看服务器日志。' });
  }
};

/**
 * 根据ID更新一个企业的信息
 */
exports.updateEnterprise = async (req, res) => {
  try {
    const { id } = req.params;
    const enterpriseData = req.body;

    // 如果用户试图修改 enterprise_id，我们要检查新ID的唯一性
    if (enterpriseData.enterprise_id) {
      const existing = await Enterprise.findOne({
        where: {
          enterprise_id: enterpriseData.enterprise_id,
          id: { [Op.ne]: id } // 排除当前正在编辑的这条记录
        }
      });
      if (existing) {
        return res.status(409).json({ message: `企业ID '${enterpriseData.enterprise_id}' 已被其他企业占用。` });
      }
    }

    // 如果有新文件上传，则更新文件路径
    if (req.file) {
      // 核心修复：使用 'uploads/' + 文件名 的方式保存相对URL路径
      enterpriseData.license_image = 'uploads/' + req.file.filename;
    }

    const [updated] = await Enterprise.update(enterpriseData, { where: { id: id } });

    if (updated) {
      // [核心修复] 更新成功后，返回和查询单个接口一致的完整数据结构
      const updatedEnterprise = await Enterprise.findByPk(id, {
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'name', 'mobile']
          },
          {
            model: User,
            as: 'user',
            attributes: ['id', 'user_id', 'name', 'nickname']
          }
        ]
      });
      res.status(200).json(updatedEnterprise);
    } else {
      res.status(404).json({ message: `未找到ID为 ${id} 的企业` });
    }
  } catch (error) {
    console.error(`更新企业ID ${req.params.id} 时出错:`, error);
    // [增强] 处理 Sequelize 验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新企业失败', error: error.message });
  }
};

/**
 * [!] 核心修复：重写删除逻辑，增加事务处理，确保关联数据被一并删除
 * 根据ID删除一个企业，并删除所有关联的跟进记录和资产
 */
exports.deleteEnterprise = async (req, res) => {
  const { id } = req.params; // 从URL参数中获取企业ID
  const transaction = await db.sequelize.transaction(); // 启动事务

  try {
    const enterprise = await Enterprise.findByPk(id);
    if (!enterprise) {
      await transaction.rollback(); // 回滚事务
      return res.status(404).json({ message: '未找到指定ID的企业' });
    }

    // 1. 在事务中删除所有关联的跟进记录
    await Followup.destroy({
      where: { enterprise_id: id },
      transaction: transaction
    });

    // 2. 在事务中删除所有关联的资产记录
    await Asset.destroy({
      where: { enterprise_id: id },
      transaction: transaction
    });

    // 3. 最后在事务中删除企业自身
    await Enterprise.destroy({
      where: { id: id },
      transaction: transaction
    });

    // 4. 如果所有操作都成功，提交事务
    await transaction.commit();

    // 返回状态码 204 (No Content)，表示成功删除，无需返回内容
    res.status(204).send();

  } catch (error) {
    // 5. 如果有任何错误发生，回滚所有操作
    await transaction.rollback();
    console.error('删除企业时出错:', error);
    res.status(500).json({ message: '删除企业失败，请查看服务器日志。' });
  }
};

/**
 * [新增] 下载营业执照
 */
exports.downloadLicense = async (req, res) => {
  try {
    const { id } = req.params;
    const enterprise = await Enterprise.findByPk(id, { attributes: ['license_image'] });

    if (!enterprise || !enterprise.license_image) {
      return res.status(404).send({ message: '未找到营业执照文件。' });
    }

    // 核心修复：采用更健壮的路径拼接方式
    const filename = path.basename(enterprise.license_image);
    const uploadsDir = path.join(__dirname, '..', '..', 'uploads');
    const filePath = path.join(uploadsDir, filename);

    // 使用res.download()来发送文件，它会自动设置合适的响应头
    res.download(filePath, filename, (err) => {
      if (err) {
        // 通常，如果文件不存在或路径错误，会在这里捕获到错误
        console.error('下载文件时出错:', err);
        if (!res.headersSent) {
          res.status(500).send({ message: '下载文件失败。' });
        }
      }
    });

  } catch (error) {
    console.error('处理下载请求时出错:', error);
    if (!res.headersSent) {
      res.status(500).send({ message: '服务器内部错误。' });
    }
  }
};

/**
 * [新增] 获取指定企业的关联订单
 * @param {object} req - Express的请求对象
 * @param {object} res - Express的响应对象
 */
exports.getOrdersByEnterprise = async (req, res) => {
  try {
    const { id } = req.params; // 企业ID
    const { unbound } = req.query; // 查询参数：是否只获取未绑定资产的订单

    // 构建查询条件
    let where = { enterprise_id: id };

    // 如果指定了 unbound=true，则只查询未绑定资产的订单
    if (unbound === 'true') {
      where.asset_id = null;
    }

    const orders = await OrderHead.findAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'mobile']
        },
        {
          model: Enterprise,
          as: 'enterprise',
          attributes: ['id', 'name']
        },
        {
          model: Asset,
          as: 'asset',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_name']
            }
          ]
        },
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json(orders);
  } catch (error) {
    console.error('获取企业关联订单时出错:', error);
    res.status(500).json({
      message: '获取企业关联订单失败，请查看服务器日志。',
      error: error.message
    });
  }
};