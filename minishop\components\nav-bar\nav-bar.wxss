/* 顶部悬浮导航栏 */
.floating-nav {
  position: fixed;
  top: 95rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background-color: transparent;
  z-index: 100;
  overflow: hidden;
  padding: 0;
  width: 100%;
}

/* 导航滚动容器 - 玻璃效果 */
.nav-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  background-color: rgba(255, 255, 255, 0.2);
  /* border-radius: 30rpx; */
  box-shadow: 0 4rpx 12rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 导航滚动容器 - 液态玻璃效果激活状态 */
.nav-scroll.glass-active {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 8rpx 32rpx rgba(31, 38, 135, 0.25),
    inset 0 0 4rpx rgba(255, 255, 255, 0.9),
    0 0 15rpx rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.7);
  /* 增强玻璃效果 */
  backdrop-filter: blur(20px) contrast(1.2) saturate(1.1);
  -webkit-backdrop-filter: blur(20px) contrast(1.2) saturate(1.1);
}

/* 简单玻璃背景效果 */
.glass-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%; /* 确保宽度是100% */
  min-width: 100%; /* 确保最小宽度覆盖可视区域 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.25) 100%
  );
  z-index: 0;
  /* border-radius: 30rpx; */
  opacity: 0.8;
}

/* 简单反射效果 */
.glass-reflections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
  pointer-events: none;
}

/* 简单反射点 */
.reflection {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.3) 30%,
    rgba(255, 255, 255, 0) 70%
  );
  filter: blur(2px);
  mix-blend-mode: overlay;
}

.reflection-1 {
  top: -30rpx;
  left: 20%;
  width: 150rpx;
  height: 150rpx;
  opacity: 0.7;
}

.reflection-2 {
  bottom: -50rpx;
  right: 30%;
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.nav-tabs {
  display: inline-flex;
  flex-direction: row;
  padding: 0 20rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 5;
  width: 100%;
}

.nav-item {
  padding: 0 20rpx;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 40rpx;
  color: #ffffff;
  position: relative;
  transition: all 0.3s;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.nav-item.active {
  color: #fcf9f9;
  font-weight: bold;
  transform: scale(1.05);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #fff;
  box-shadow: 0 0 12rpx rgba(255, 255, 255, 1);
  transition: all 0.3s;
} 