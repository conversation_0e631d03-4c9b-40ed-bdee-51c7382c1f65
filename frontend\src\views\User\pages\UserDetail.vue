<template>
  <div class="user-detail-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>

        <div class="header-actions">
          <el-button type="primary" @click="handleEdit" v-if="!isEditMode">
            <el-icon><Edit /></el-icon>
            修改
          </el-button>
          <el-button type="danger" @click="handleDelete" v-if="!isEditMode">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>

          <!-- 编辑模式下的操作按钮 -->
          <el-button type="primary" @click="handleSave" :loading="saving" v-if="isEditMode">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="handleCancel" v-if="isEditMode">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="user-detail-content" v-if="userData">
      <!-- 用户表单表头 -->
      <UserFormHeader
        :formData="userData"
        :readonly="!isEditMode"
        :isEditMode="isEditMode"
        @partner-change="handlePartnerChange"
        @manage-auth="handleManageAuth"
        ref="headerFormRef"
      />

      <!-- 标签页内容 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="detail-tabs" type="border-card">
          <!-- 关联企业 -->
          <el-tab-pane name="enterprises">
            <template #label>
              <span class="tab-label">
                <el-icon><OfficeBuilding /></el-icon>
                关联企业
              </span>
            </template>
            <UserRelatedEnterprises
              v-if="userData"
              :userId="userData.id"
              :readonly="false"
              :relatedEnterprises="relatedEnterprises"
              @enterprise-linked="handleEnterpriseLinked"
              @enterprise-removed="handleEnterpriseRemoved"
            />
          </el-tab-pane>

          <!-- 关联资产 -->
          <el-tab-pane name="assets">
            <template #label>
              <span class="tab-label">
                <el-icon><Box /></el-icon>
                关联资产
              </span>
            </template>
            <UserRelatedAssets
              v-if="userData"
              :userId="userData.id"
              :readonly="false"
              :relatedAssets="relatedAssets"
              @asset-linked="handleAssetLinked"
              @asset-removed="handleAssetRemoved"
            />
          </el-tab-pane>

          <!-- 关联订单 -->
          <el-tab-pane name="orders">
            <template #label>
              <span class="tab-label">
                <el-icon><Document /></el-icon>
                关联订单
              </span>
            </template>
            <div class="orders-tab-content">
              <OrderTable
                v-if="userData"
                :orders="relatedOrders"
                :loading="ordersLoading"
                :emptyText="'该用户暂无关联订单'"
                :selectable="false"
                :showActions="true"
                :actionType="'view'"
              />
            </div>
          </el-tab-pane>


        </el-tabs>
      </div>

      <!-- 认证管理对话框 -->
      <UserAuthenticationDialog
        v-model="authDialogVisible"
        :userId="userData?.id"
        @success="handleAuthUpdated"
      />

      <!-- 表尾信息 -->
      <div class="footer-section">
        <el-card shadow="never" class="footer-card">
          <template #header>
            <span class="card-header">其他信息</span>
          </template>

          <el-form :model="userData" label-width="120px" :disabled="!isEditMode">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制单人">
                  <el-input
                    :value="getCreatorName()"
                    readonly
                    placeholder="制单员工姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="创建时间">
                  <el-input :value="formatDateTime(userData.createdAt)" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="修改时间">
                  <el-input :value="formatDateTime(userData.updatedAt)" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, Delete, Check, Close,
  Box, Document, OfficeBuilding
} from '@element-plus/icons-vue'

// 组件导入
import UserFormHeader from '../components/UserFormHeader.vue'
import UserRelatedEnterprises from '../components/UserRelatedEnterprises.vue'
import UserRelatedAssets from '../components/UserRelatedAssets.vue'
import UserAuthenticationDialog from '../components/UserAuthenticationDialog.vue'
import OrderTable from '@/views/Asset/components/OrderTable.vue'

// 工具函数
import { formatDateTime } from '@/utils/format.js'

// API
import { getUserById, updateUser, deleteUser, getNextPartnerId } from '@/api/user.js'

import { getEnterprises } from '@/api/enterprise.js'
import { getAssets } from '@/api/asset.js'
import { getOrders } from '@/api/order.js'

// 认证状态
import { useAuth } from '@/store/auth.js'

// 路由
const route = useRoute()
const router = useRouter()

// 认证状态
const { state: authState } = useAuth()

// 状态
const loading = ref(false)
const saving = ref(false)
const activeTab = ref('enterprises')
const isEditMode = ref(false)
const authDialogVisible = ref(false)
const ordersLoading = ref(false)

// 数据
const userData = ref(null)
const originalData = ref(null) // 保存原始数据用于取消操作

const relatedEnterprises = ref([]) // 关联企业数据
const relatedAssets = ref([]) // 关联资产数据
const relatedOrders = ref([]) // 关联订单数据

// 表单引用
const headerFormRef = ref(null)

// 计算属性
const userId = computed(() => route.params.id)
const pageTitle = computed(() => {
  if (isEditMode.value) return '修改用户'
  if (userData.value) {
    return `用户详情 - ${userData.value.name || ''}`
  }
  return '用户详情'
})

// 工具函数
const getCreatorName = () => {
  // 如果是新建用户，显示当前登录员工的姓名
  if (!userData.value?.id) {
    return authState.employee?.name || ''
  }
  // 如果是已存在的用户，显示创建者姓名
  return userData.value?.creator?.name || ''
}

// 加载用户数据
const loadUserData = async () => {
  if (!userId.value) return

  loading.value = true
  try {
    const response = await getUserById(userId.value)
    userData.value = response
    originalData.value = JSON.parse(JSON.stringify(response)) // 深拷贝

    // 加载关联数据
    await loadRelatedData()
  } catch (error) {
    ElMessage.error('加载用户数据失败')
    router.push({ name: 'users' })
  } finally {
    loading.value = false
  }
}



// 加载关联数据
const loadRelatedData = async () => {
  if (!userId.value) return

  try {
    // 并行加载关联数据
    const [enterprisesRes, assetsRes, ordersRes] = await Promise.allSettled([
      getEnterprises({ userId: userId.value }),
      getAssets({ userId: userId.value }),
      getOrders({ userId: userId.value })
    ])

    // 处理企业数据
    if (enterprisesRes.status === 'fulfilled') {
      relatedEnterprises.value = enterprisesRes.value || []
    } else {
      relatedEnterprises.value = []
    }

    // 处理资产数据
    if (assetsRes.status === 'fulfilled') {
      relatedAssets.value = assetsRes.value || []
    } else {
      relatedAssets.value = []
    }

    // 处理订单数据
    if (ordersRes.status === 'fulfilled') {
      relatedOrders.value = ordersRes.value || []
    } else {
      relatedOrders.value = []
    }
  } catch (error) {
    console.error('加载关联数据失败:', error)
  }
}

// 事件处理
const handleEdit = () => {
  isEditMode.value = true
}

const handleCancel = () => {
  // 恢复原始数据
  userData.value = JSON.parse(JSON.stringify(originalData.value))
  isEditMode.value = false
  ElMessage.info('已取消操作')
}

const handleSave = async () => {
  try {
    // 表单验证
    const isValid = await headerFormRef.value?.validate()
    if (!isValid) return

    saving.value = true

    const dataToSend = { ...userData.value }

    // 移除关联对象
    delete dataToSend.enterprises
    delete dataToSend.assets
    delete dataToSend.orders

    // 如果密码为空，则不更新密码
    if (!dataToSend.password) {
      delete dataToSend.password
    }

    await updateUser(userId.value, dataToSend)

    // 重新加载数据
    await loadUserData()

    isEditMode.value = false
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleDelete = async () => {
  if (!userId.value) return

  try {
    await ElMessageBox.confirm(
      `确定要删除用户 [${userData.value.name}] 吗？`,
      '警告',
      { type: 'warning' }
    )

    await deleteUser(userId.value)
    ElMessage.success('删除成功')
    router.push({ name: 'users' })
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 处理合伙人状态变化
const handlePartnerChange = async (isPartner) => {
  if (isPartner && !userData.value.partner_id) {
    try {
      const res = await getNextPartnerId()
      userData.value.partner_id = res.next_id
      // 自动生成ID后，清除可能的验证错误
      nextTick(() => {
        formHeaderRef.value?.formRef?.clearValidate('partner_id')
      })
    } catch (error) {
      ElMessage.error('获取新合伙人ID失败，请手动输入一个唯一的ID。')
    }
  }
}

// 处理管理认证
const handleManageAuth = () => {
  authDialogVisible.value = true
}

// 处理企业关联
const handleEnterpriseLinked = (enterprises) => {
  // 这里应该调用API来关联企业
  ElMessage.success('企业关联成功')
  // 重新加载用户数据
  loadUserData()
}

// 处理企业移除
const handleEnterpriseRemoved = (enterpriseIds) => {
  // 这里应该调用API来移除企业关联
  ElMessage.success('企业移除成功')
  // 重新加载用户数据
  loadUserData()
}

// 处理资产关联
const handleAssetLinked = (assets) => {
  // 这里应该调用API来关联资产
  ElMessage.success('资产关联成功')
  // 重新加载用户数据
  loadUserData()
}

// 处理资产移除
const handleAssetRemoved = (assetIds) => {
  // 这里应该调用API来移除资产关联
  ElMessage.success('资产移除成功')
  // 重新加载用户数据
  loadUserData()
}

// 处理认证信息更新
const handleAuthUpdated = () => {
  ElMessage.success('认证信息更新成功')
}

// 生命周期
onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.user-detail-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 主要内容 */
.user-detail-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标签页 */
.tabs-section {
  margin: 20px 0;
}

.detail-tabs {
  background: white;
}

.detail-tabs :deep(.el-tabs__content) {
  padding-top: 20px;
}

.detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.detail-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.orders-tab-content {
  padding: 20px;
}

.authentication-tab-content {
  padding: 20px;
}

.auth-info-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auth-placeholder {
  text-align: center;
  padding: 40px;
}

/* 卡片样式 */
.footer-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.footer-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-detail-page {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>
