<script setup>
import { ref, computed } from 'vue';
import CrudPage from '@/components/CrudPage.vue';
import { getFeatures, createFeature, updateFeature, deleteFeature, getNextFeatureId } from '@/api/feature.js';
import { ElMessage, ElMessageBox } from 'element-plus';

// 1. 定义表格的列
const columns = ref([
  { prop: 'id', label: '自增ID', width: 120 },
  { prop: 'feature_id', label: '功能ID', width: 180 },
  { prop: 'feature_name', label: '功能名称', width: 250 },
  { prop: 'description', label: '功能描述', showOverflowTooltip: true },
]);

// 2. 定义API函数
const api = {
  list: getFeatures,
  create: createFeature,
  update: updateFeature,
  delete: deleteFeature,
};

// 3. 状态管理
const dialogVisible = ref(false);
const isEditMode = ref(false);
const formRef = ref(null);
const crudPageRef = ref(null);
const selectedItems = ref([]); // 用于存储选中的行

// 表单数据模型
const getInitialForm = () => ({ id: null, feature_id: '', feature_name: '', description: '' });
const form = ref(getInitialForm());

// 4. 计算属性，控制按钮禁用状态
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);

// --- 5. 方法 ---

// CrudPage组件行选择变化时触发
const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

// 打开弹窗（新增、编辑、复制共用）
const handleOpenDialog = async (editMode = false, copyMode = false, rowData = null) => {
  isEditMode.value = editMode;
  if (editMode && rowData) {
    form.value = { ...rowData };
  } else if (copyMode && rowData) {
    const copiedData = { ...rowData };
    delete copiedData.id;
    try {
      const res = await getNextFeatureId();
      copiedData.feature_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新功能ID失败，请为复制的记录输入一个唯一的ID。');
      copiedData.feature_id = '';
    }
    form.value = copiedData;
    isEditMode.value = false;
  } else {
    form.value = getInitialForm();
    try {
      const res = await getNextFeatureId();
      form.value.feature_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新功能ID失败，请输入一个唯一的ID。');
    }
  }
  dialogVisible.value = true;
};

// 批量删除
const handleDelete = () => {
  if (isDeleteDisabled.value) return;
  const idsToDelete = selectedItems.value.map(item => item.id);
  const names = selectedItems.value.map(item => item.feature_name).join(', ');

  ElMessageBox.confirm(`确定删除选中的 ${idsToDelete.length} 项功能 (${names}) 吗？`, '警告', { type: 'warning' })
    .then(async () => {
      await Promise.all(idsToDelete.map(id => api.delete(id)));
      ElMessage.success('删除成功');
      crudPageRef.value?.loadData(); // 刷新数据
      selectedItems.value = [];
    })
    .catch(() => ElMessage.info('已取消删除'));
};

// 提交表单
const handleSubmit = async () => {
  try {
    if (isEditMode.value) {
      await api.update(form.value.id, form.value);
      ElMessage.success('更新成功');
    } else {
      await api.create(form.value);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
     const action = isEditMode.value ? '更新' : '创建';
     ElMessage.error(`${action}失败: ${error.response?.data?.message || '未知错误'}`);
  }
};
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="功能"
    :columns="columns"
    :api-list="api.list"
    :api-create="api.create"
    :api-update="api.update"
    :api-delete="api.delete"
    :hide-row-actions="true"
    @selection-change="handleSelectionChange"
  >
    <!-- 使用 #actions 插槽自定义顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="handleOpenDialog(false, false, null)">新增功能</el-button>
      
      <el-tooltip content="请选择一项进行修改" :disabled="!isEditDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"> <!-- Tooltip Wrapper -->
          <el-button 
            type="default" 
            :disabled="isEditDisabled"
            @click="handleOpenDialog(true, false, selectedItems[0])">
            修改
          </el-button>
        </div>
      </el-tooltip>
      
      <el-tooltip content="请选择一项进行复制" :disabled="!isCopyDisabled" placement="top">
         <div style="display: inline-block; margin: 0 6px;"> <!-- Tooltip Wrapper -->
          <el-button 
            type="success" 
            :disabled="isCopyDisabled"
            @click="handleOpenDialog(false, true, selectedItems[0])">
            复制
          </el-button>
        </div>
      </el-tooltip>
      
       <el-tooltip content="请至少选择一项进行删除" :disabled="!isDeleteDisabled" placement="top">
         <div style="display: inline-block; margin: 0 6px;"> <!-- Tooltip Wrapper -->
          <el-button 
            type="danger" 
            :disabled="isDeleteDisabled"
            @click="!isDeleteDisabled && handleDelete()">
            删除
          </el-button>
        </div>
      </el-tooltip>
    </template>
    
    <!-- 使用 #dialog 插槽提供弹窗实现 -->
    <template #dialog>
      <el-dialog 
        v-model="dialogVisible" 
        :title="isEditMode ? '编辑功能' : '新增功能'" 
        width="500px" 
        :close-on-click-modal="false"
      >
        <el-form :model="form" ref="formRef" label-width="80px">
          <el-form-item label="功能ID" prop="feature_id">
            <el-input v-model="form.feature_id" placeholder="例如: FT004"></el-input>
          </el-form-item>
          <el-form-item label="功能名称" prop="feature_name">
            <el-input v-model="form.feature_name"></el-input>
          </el-form-item>
          <el-form-item label="功能描述" prop="description">
            <el-input v-model="form.description" type="textarea"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </template>
      </el-dialog>
    </template>

  </CrudPage>
</template>

<style scoped>
/* 之前的自定义禁用样式可以移除了 */
</style> 