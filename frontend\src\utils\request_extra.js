import axios from 'axios';
import { ElMessage, ElNotification } from 'element-plus';
import { useAuth } from '@/store/auth';

// 防止重复登出的标志
let isLoggingOut = false;
// 防止重复权限提示的标志
let hasShownPermissionError = false;

// 创建一个新的 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://service.bogoo.net/api', // API 的 base_url
  timeout: 5000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么，例如添加token
    const token = localStorage.getItem('authToken'); // 从localStorage获取token
    if (token) {
      // 如果token存在，则将其添加到请求头中
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;
    // 如果是blob二进制文件，则直接返回
    if (response.request.responseType === 'blob') {
      return response;
    }
    return res;
  },
  error => {
    // 对响应错误做点什么
    console.error('API Error: ', error.response?.data?.message || error.message); // 简化日志输出

    const status = error.response?.status;
    const { logout } = useAuth();

    if (status === 401) {
      // 对于登录接口本身的401错误，只提示密码错误，不直接登出
      if (error.config.url.endsWith('/auth/employee/login')) {
        ElMessage({
          message: error.response?.data?.message || '用户名或密码错误',
          type: 'error',
          duration: 3000
        });
      } else {
        // 其他所有接口的401错误，意味着token失效
        handleAuthExpired(logout);
      }
    } else if (status === 403) {
      // 403权限不足错误，统一处理
      handlePermissionDenied();
    } else if (status >= 500) {
      // 服务器错误
      ElMessage({
        message: '服务器暂时无法响应，请稍后重试',
        type: 'error',
        duration: 3000
      });
    } else {
      // 其他错误，显示具体错误信息
      const message = error.response?.data?.message || error.message || '请求失败';
      ElMessage({
        message: message,
        type: 'error',
        duration: 3000
      });
    }

    return Promise.reject(error);
  }
);

/**
 * 处理认证过期
 */
function handleAuthExpired(logout) {
  if (isLoggingOut) {
    return; // 如果已经在登出过程中，避免重复处理
  }

  isLoggingOut = true;

  // 使用通知而不是消息，更加醒目且不会重复
  ElNotification({
    title: '登录已过期',
    message: '您的登录会话已过期，请重新登录',
    type: 'warning',
    duration: 4000,
    position: 'top-right'
  });

  // 延迟执行登出，给用户看到提示的时间
  setTimeout(() => {
    logout();
    isLoggingOut = false; // 重置标志
  }, 1000);
}

/**
 * 处理权限不足错误
 */
function handlePermissionDenied() {
  // 防止在短时间内重复显示权限错误
  if (hasShownPermissionError) {
    return;
  }

  hasShownPermissionError = true;

  ElNotification({
    title: '权限不足',
    message: '您没有权限访问此资源，请联系管理员',
    type: 'error',
    duration: 4000,
    position: 'top-right'
  });

  // 5秒后重置标志，允许再次显示权限错误
  setTimeout(() => {
    hasShownPermissionError = false;
  }, 5000);
}

export default service; 