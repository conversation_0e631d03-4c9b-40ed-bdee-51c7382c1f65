/* 页面容器 */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8ffff, #f0fffd);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 科技感背景装饰 */
.tech-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  opacity: 0.7;
}

.tech-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.08;
}

.circle-1 {
  top: -150rpx;
  right: -150rpx;
  width: 500rpx;
  height: 500rpx;
  background: linear-gradient(135deg, #0FB9B1, #38e7df);
}

.circle-2 {
  bottom: -200rpx;
  left: -200rpx;
  width: 600rpx;
  height: 600rpx;
  background: linear-gradient(135deg, #38e7df, #0FB9B1);
}

.tech-line {
  position: absolute;
  background: rgba(15, 185, 177, 0.08);
}

.line-1 {
  top: 30%;
  left: -5%;
  width: 110%;
  height: 2px;
  transform: rotate(15deg);
}

.line-2 {
  top: 60%;
  left: -5%;
  width: 110%;
  height: 2px;
  transform: rotate(-10deg);
}

.line-3 {
  top: 20%;
  left: -5%;
  width: 110%;
  height: 1px;
  transform: rotate(-5deg);
}

/* 顶部区域 */
.profile-header {
  position: relative;
  background: linear-gradient(135deg, #0FB9B1, #38e7df);
  color: #fff;
  padding: 60rpx 40rpx 80rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 10rpx 25rpx rgba(15, 185, 177, 0.3);
  overflow: hidden;
  z-index: 2;
}

.profile-header:after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  right: -20rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.profile-header:before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: -80rpx;
  width: 250rpx;
  height: 250rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  z-index: 1;
}

.header-content {
  position: relative;
  z-index: 2;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
  background-color: #fff;
  margin-right: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  color: #fff;
  text-shadow: 0 4rpx 4rpx rgba(0, 0, 0, 0.1);
}

.partner-code-container {
  display: flex;
  align-items: center;
}

.partner-code {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.copy-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.auth-button {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 14rpx 24rpx;
  border-radius: 30rpx;
  align-self: flex-start;
  font-size: 26rpx;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.auth-button text {
  margin-right: 16rpx;
}

.icon-arrow {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
}

/* 卡片区域 */
.cards-section {
  margin-top: -40rpx;
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  z-index: 10;
  position: relative;
  padding-bottom: 30rpx;
}

/* 通用卡片样式 */
.earnings-card, .rules-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  padding: 30rpx;
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(15, 185, 177, 0.1);
}

/* 卡片标题样式 */
.card-title, .earnings-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.card-title text, .earnings-title text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.section-icon {
  margin-right: 18rpx;
  font-size: 36rpx;
}

/* 我的收益卡片 - 特殊突出样式 */
.earnings-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 255, 255, 0.9));
  border: 1px solid rgba(15, 185, 177, 0.2);
  box-shadow: 0 15rpx 35rpx rgba(15, 185, 177, 0.15);
  padding: 40rpx;
  margin-bottom: 10rpx;
  position: relative;
  overflow: hidden;
}

.earnings-glow {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(15, 185, 177, 0.2) 0%, rgba(15, 185, 177, 0) 70%);
  top: -50rpx;
  right: -50rpx;
  border-radius: 50%;
  z-index: -1;
}

.earnings-title {
  font-size: 38rpx;
  color: #0FB9B1;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.earnings-data {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 30rpx 5rpx 40rpx;
  background: rgba(248, 255, 255, 0.8);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: inset 0 0 10rpx rgba(15, 185, 177, 0.05);
}

.earnings-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.earnings-value-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.earnings-currency {
  font-size: 28rpx;
  font-weight: bold;
  color: #0FB9B1;
  margin-right: 4rpx;
}

.earnings-value {
  font-size: 38rpx;
  font-weight: bold;
  color: #0FB9B1;
  line-height: 1;
}

.earnings-label {
  font-size: 28rpx;
  color: #666;
}

.earnings-divider {
  width: 2rpx;
  height: 80rpx;
  background: rgba(15, 185, 177, 0.2);
  margin: 0 20rpx;
}

.detail-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #0FB9B1, #38e7df);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 16rpx rgba(15, 185, 177, 0.25);
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  position: relative;
  overflow: hidden;
}

.detail-button:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.5;
}

.arrow-icon {
  margin-left: 15rpx;
  font-weight: bold;
}

.detail-button:active {
  opacity: 0.9;
  transform: translateY(2rpx);
}

/* 佣金比例卡片 */
.commission-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(15, 185, 177, 0.1);
}

.commission-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.commission-title .section-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.commission-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16rpx;
}

.commission-item {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
}

.commission-item.total {
  background: linear-gradient(135deg, rgba(15, 185, 177, 0.1), rgba(15, 185, 177, 0.05));
  border-radius: 12rpx;
  padding: 16rpx 8rpx;
  border: 1rpx solid rgba(15, 185, 177, 0.2);
}

.commission-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.commission-label {
  font-size: 24rpx;
  color: #666;
  line-height: 1.2;
}

.commission-value {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.2;
}

.commission-value.base {
  color: #FF9800;
}

.commission-value.extra {
  color: #2196F3;
}

.commission-value.total {
  color: #0FB9B1;
  font-size: 32rpx;
}

.commission-divider {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
  margin: 0 8rpx;
  flex-shrink: 0;
}

/* 规则卡片 */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.rule-item {
  display: flex;
  align-items: flex-start;
}

.rule-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  background: #0FB9B1;
  margin-top: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.rule-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  flex: 1;
}

/* 底部按钮 */
.bottom-section {
  margin-top: 20rpx;
  padding: 0 30rpx 40rpx;
}

.logout-button {
  background: rgba(255, 255, 255, 0.9);
  color: #ff6b6b;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.12);
  border: 1px solid rgba(255, 107, 107, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.logout-button:active {
  background: rgba(255, 107, 107, 0.05);
}

/* 功能按钮区域 */
.action-section {
  padding: 30rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  height: 100rpx;
  background: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.action-button:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.button-icon {
  font-size: 32rpx;
}

.button-text {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

/* 安全区域 */
.safe-area-bottom {
  height: 50rpx;
  width: 100%;
}
