/*
 * @pdhsy.wxss
 * 重构和重新排序以匹配 pdhsy.wxml 的结构。
 * 删除了未使用的 CSS 规则。
 */

/* ==================================
   全局和根样式
   ================================== */
@import '/static/fonts/iconfont.wxss';

page {
  margin: 0;
  padding: 0;
  border: none;
}

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
}

.full-width-nav {
  width: 100vw !important;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
  border: none;
}

/* 全局元素边框消除 */
view, scroll-view, text, image, button {
  box-sizing: border-box;
  border: none;
}

/* ==================================
   滚动容器
   ================================== */
.scroll-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  -webkit-overflow-scrolling: touch;
}

/* ==================================
   海报区域
   ================================== */
.poster {
  width: 100%;
  height: 66vh;
  position: relative;
  overflow: hidden;
  color: #fff;
  border: none;
  margin: 0;
  padding: 0;
}

/* --- 背景动效 --- */
.poster-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  z-index: 1;
}

.p-1 { width: 20rpx; height: 20rpx; top: 10%; left: 15%; animation: float 8s infinite ease-in-out; }
.p-2 { width: 15rpx; height: 15rpx; top: 20%; left: 25%; animation: float 7s infinite ease-in-out; }
.p-3 { width: 25rpx; height: 25rpx; top: 15%; left: 60%; animation: float 9s infinite ease-in-out; }
.p-4 { width: 18rpx; height: 18rpx; top: 30%; left: 80%; animation: float 6s infinite ease-in-out; }
.p-5 { width: 22rpx; height: 22rpx; top: 40%; left: 10%; animation: float 10s infinite ease-in-out; }
.p-6 { width: 16rpx; height: 16rpx; top: 50%; left: 30%; animation: float 8s infinite ease-in-out; }
.p-7 { width: 24rpx; height: 24rpx; top: 60%; left: 70%; animation: float 7s infinite ease-in-out; }
.p-8 { width: 14rpx; height: 14rpx; top: 70%; left: 20%; animation: float 9s infinite ease-in-out; }
.p-9 { width: 20rpx; height: 20rpx; top: 80%; left: 50%; animation: float 6s infinite ease-in-out; }
.p-10 { width: 18rpx; height: 18rpx; top: 25%; left: 40%; animation: float 8s infinite ease-in-out; }
.p-11 { width: 22rpx; height: 22rpx; top: 35%; left: 65%; animation: float 7s infinite ease-in-out; }
.p-12 { width: 16rpx; height: 16rpx; top: 45%; left: 85%; animation: float 10s infinite ease-in-out; }
.p-13 { width: 24rpx; height: 24rpx; top: 55%; left: 5%; animation: float 9s infinite ease-in-out; }
.p-14 { width: 14rpx; height: 14rpx; top: 65%; left: 35%; animation: float 6s infinite ease-in-out; }
.p-15 { width: 20rpx; height: 20rpx; top: 75%; left: 75%; animation: float 8s infinite ease-in-out; }
.p-16 { width: 18rpx; height: 18rpx; top: 85%; left: 15%; animation: float 7s infinite ease-in-out; }
.p-17 { width: 22rpx; height: 22rpx; top: 12%; left: 45%; animation: float 9s infinite ease-in-out; }
.p-18 { width: 16rpx; height: 16rpx; top: 22%; left: 75%; animation: float 10s infinite ease-in-out; }
.p-19 { width: 24rpx; height: 24rpx; top: 32%; left: 25%; animation: float 6s infinite ease-in-out; }
.p-20 { width: 14rpx; height: 14rpx; top: 42%; left: 55%; animation: float 8s infinite ease-in-out; }
.p-21 { width: 20rpx; height: 20rpx; top: 52%; left: 15%; animation: float 7s infinite ease-in-out; }
.p-22 { width: 18rpx; height: 18rpx; top: 62%; left: 45%; animation: float 9s infinite ease-in-out; }
.p-23 { width: 22rpx; height: 22rpx; top: 72%; left: 85%; animation: float 10s infinite ease-in-out; }
.p-24 { width: 16rpx; height: 16rpx; top: 82%; left: 35%; animation: float 6s infinite ease-in-out; }
.p-25 { width: 24rpx; height: 24rpx; top: 92%; left: 65%; animation: float 8s infinite ease-in-out; }

.connection-line { position: absolute; background-color: rgba(255, 255, 255, 0.1); z-index: 1; }
.line-1 { width: 1rpx; height: 200rpx; top: 10%; left: 30%; transform: rotate(45deg); }
.line-2 { width: 1rpx; height: 250rpx; top: 30%; left: 60%; transform: rotate(-30deg); }
.line-3 { width: 1rpx; height: 180rpx; top: 50%; left: 20%; transform: rotate(60deg); }
.line-4 { width: 1rpx; height: 220rpx; top: 60%; left: 70%; transform: rotate(20deg); }
.line-5 { width: 1rpx; height: 200rpx; top: 80%; left: 40%; transform: rotate(-60deg); }

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 50rpx 50rpx;
  z-index: 1;
}

.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  height: 500rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  animation: pulse 4s infinite ease-in-out;
}

/* --- 海报内容 --- */
.product-title {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 10;
  text-align: center;
  width: 80%;
  animation: fadeIn 1s ease-out;
}

.title-main {
  font-size: 100rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  background: linear-gradient(90deg, #fff 20%, #7dd3fc 80%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 6rpx 24rpx rgba(0, 64, 255, 0.18);
  margin-bottom: 18rpx;
  animation: fadeInUp 1s;
}

.title-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #38bdf8 0%, #fff 100%);
  border-radius: 4rpx;
  margin: 20rpx auto;
  box-shadow: 0 2rpx 8rpx rgba(56, 189, 248, 0.5);
  animation: fadeIn 1.5s;
}

.title-sub {
  font-size: 36rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  animation: fadeInUp 1.2s;
}

.slogan {
  font-size: 32rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 1rpx;
  line-height: 1.5;
  margin-top: 16rpx;
  animation: fadeInUp 1.4s;
}

/* --- 促销卡片 --- */
.promo-card-position {
  position: absolute;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  z-index: 10;
  filter: drop-shadow(0 6rpx 16rpx rgba(0, 0, 0, 0.1));
}

/* --- 底部羽化 --- */
.poster-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35%;
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 85%, #ffffff 100%);
  z-index: 2;
  pointer-events: none;
}

/* ==================================
   详情内容区域
   ================================== */
.detail-content {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  padding: 40rpx 30rpx 0;
  margin-top: -80rpx;
  z-index: 10;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.05);
}

/* --- 主标题区域 --- */
.hero-banner {
  padding: 50rpx 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(180deg, #f8fafc 0%, rgba(255, 255, 255, 0.9) 100%);
  position: relative;
  overflow: hidden;
  border-radius: 40rpx 40rpx 0 0;
}

.hero-banner:before {
  content: '';
  position: absolute;
  top: -30%;
  right: -20%;
  width: 250rpx;
  height: 250rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 120, 255, 0.05), transparent 70%);
}

.hero-banner:after {
  content: '';
  position: absolute;
  bottom: -10%;
  left: -10%;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 120, 255, 0.05), transparent 70%);
}

.hero-content {
  text-align: center;
  max-width: 90%;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  background: linear-gradient(90deg, #0052d9 0%, #3a90ff 100%);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
}

.hero-subtitle {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  max-width: 90%;
  margin: 0 auto;
}

.divider {
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #0078ff 0%, #52a9ff 100%);
  margin: 30rpx auto;
  border-radius: 3rpx;
  position: relative;
  overflow: hidden;
}

.divider:after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  animation: shimmer 2s infinite;
}

/* --- 通用区块头 --- */
.section-header {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
}

.main-title {
  font-size: 42rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.3;
  letter-spacing: 1rpx;
}

.sub-title {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.highlight {
  color: #0078ff;
  position: relative;
  display: inline-block;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: rgba(0, 120, 255, 0.2);
  border-radius: 3rpx;
  transform: translateY(8rpx);
}

/* --- 企业痛点 --- */
.pain-points-section {
  background-color: #fff;
  padding-bottom: 70rpx;
  position: relative;
}

.pain-cards {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.pain-card {
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateY(0);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.pain-card:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.08);
}

.pain-image {
  width: 100%;
  height: 260rpx;
  overflow: hidden;
  position: relative;
}

.pain-image:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
  z-index: 1;
}

.pain-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.pain-card:active .pain-image image {
  transform: scale(1.05);
}

.pain-content {
  padding: 34rpx;
  position: relative;
}

.pain-content:before {
  content: '';
  position: absolute;
  left: 0;
  top: 34rpx;
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, #0078ff, #52a9ff);
  border-radius: 3rpx;
}

.pain-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 20rpx;
}

.pain-desc {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.pain-desc text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.pain-desc text:before {
  content: '';
  position: absolute;
  left: 0;
  top: 14rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #0078ff;
}

/* --- 全面解决方案 --- */
.solution-section {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  padding-bottom: 70rpx;
  position: relative;
}

.solution-section:before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 120, 255, 0.2), transparent);
}

.solution-overview {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 20rpx 40rpx;
  gap: 20rpx;
  background: rgba(0, 120, 255, 0.03);
  border-radius: 20rpx;
  margin: 0 30rpx;
}

.solution-item {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  padding: 10rpx 15rpx;
  position: relative;
}

.solution-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: rgba(0, 120, 255, 0.2);
}

/* --- 功能区块 --- */
.feature-block {
  padding: 70rpx 0 50rpx;
  margin: 0 30rpx 40rpx;
  border-radius: 24rpx;
  background-color: #fff;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.feature-block:active {
  transform: translateY(-5rpx);
}

.feature-block:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #0078ff, #52a9ff);
}

.feature-header {
  padding: 0 40rpx 40rpx;
  text-align: center;
}

.feature-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  display: inline-block;
}

.feature-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  max-width: 90%;
  margin: 20rpx auto 0;
}

.feature-content {
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
  gap: 40rpx;
}

.feature-left, .feature-right {
  width: 100%;
}

.feature-left image, .feature-right image {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  object-fit: cover;
  border-radius: 16rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  transform: translateY(0);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-left image:active, .feature-right image:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.12);
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #f0f7ff, #e6f0ff);
  border-radius: 16rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 12rpx rgba(0, 120, 255, 0.08);
}

.feature-icon .iconfont {
  font-size: 32rpx;
  color: #0078ff;
}

.feature-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}

/* --- 行业解决方案 --- */
.industry-section {
  padding-bottom: 70rpx;
  position: relative;
}

.industry-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
}

.industry-cards {
  display: inline-flex;
  padding: 0 30rpx;
}

.industry-card {
  width: 300rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.industry-card:active {
  transform: translateY(-5rpx);
}

.industry-card-inner {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx 20rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.industry-icon {
  width: 90rpx;
  height: 90rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #f0f7ff, #e6f0ff);
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 120, 255, 0.1);
}

.industry-icon .iconfont {
  font-size: 46rpx;
  color: #0078ff;
}

.industry-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}

.industry-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

.industry-tag {
  margin-top: 20rpx;
  font-size: 22rpx;
  color: #0078ff;
  background: rgba(0, 120, 255, 0.08);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.industry-tag text {
  font-weight: bold;
  font-size: 24rpx;
}

.scroll-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.hint-line {
  height: 1px;
  flex: 1;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.hint-text {
  font-size: 24rpx;
  color: #999;
  margin: 0 20rpx;
}

/* --- 服务保障 --- */
.service-section {
  padding-bottom: 70rpx;
  position: relative;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 0 30rpx;
}

.service-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.service-item:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.08);
}

.service-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f0ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0, 120, 255, 0.1);
}

.service-icon .iconfont {
  font-size: 50rpx;
  color: #0078ff;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* --- 用户评价 --- */
.testimonial-section {
  padding-bottom: 70rpx;
  position: relative;
}

.testimonial-swiper {
  height: 320rpx;
  padding: 20rpx 0;
}

.testimonial-card {
  margin: 10rpx;
  height: 280rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.testimonial-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 30rpx;
  position: relative;
}

.testimonial-content:before {
  content: '"';
  position: absolute;
  left: -20rpx;
  top: -20rpx;
  font-size: 60rpx;
  color: rgba(0, 120, 255, 0.1);
  font-weight: bold;
}

.testimonial-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #0078ff, #52a9ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  margin-right: 16rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.user-company {
  font-size: 22rpx;
  color: #999;
}

/* --- 体验咨询 --- */
.cta-section {
  margin: 0 30rpx 60rpx;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
}

.cta-section:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0078ff 0%, #3b94ff 100%);
  z-index: 1;
}

.cta-section:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 40%), 
                    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 40%);
  z-index: 2;
}

.cta-content {
  padding: 70rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  position: relative;
  z-index: 3;
}

.cta-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
  letter-spacing: 1rpx;
}

.cta-desc {
  font-size: 30rpx;
  margin-bottom: 50rpx;
  text-align: center;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 40rpx;
  width: 100%;
  justify-content: center;
}

.cta-btn {
  min-width: 240rpx;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.cta-btn:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.2);
}

.cta-btn.outline {
  background-color: transparent;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  color: #fff;
}

.cta-btn.outline:before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cta-btn.outline:active:before {
  opacity: 1;
}

.cta-btn.primary {
  background-color: #fff;
  color: #0078ff;
}

.cta-btn.primary:after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 3s infinite;
  z-index: 1;
}

.btn-text {
  position: relative;
  z-index: 2;
}


/* ==================================
   动画
   ================================== */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 新增样式：616大厦悬浮球 */
.floating-btn {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  z-index: 100;
  transform: translateZ(0);
}

.floating-circle {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #0078ff, #3b94ff);
  border-radius: 50%;
  box-shadow: 0 6rpx 20rpx rgba(0, 120, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-weight: bold;
  font-size: 28rpx;
  transition: all 0.3s ease;
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 6rpx 20rpx rgba(0, 120, 255, 0.3);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 8rpx 30rpx rgba(0, 120, 255, 0.5);
  }
}

.floating-label {
  position: absolute;
  bottom: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #ff6a00;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

/* 行业卡片滚动区域 */
.industry-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
}

.industry-cards {
  display: inline-flex;
  padding: 0 30rpx;
}

.industry-card {
  width: 300rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.industry-card:active {
  transform: translateY(-5rpx);
}

.industry-card-inner {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx 20rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.industry-tag {
  margin-top: 20rpx;
  font-size: 22rpx;
  color: #0078ff;
  background: rgba(0, 120, 255, 0.08);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.industry-tag text {
  font-weight: bold;
  font-size: 24rpx;
}

.scroll-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.hint-line {
  height: 1px;
  flex: 1;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.hint-text {
  font-size: 24rpx;
  color: #999;
  margin: 0 20rpx;
}

/* 特色功能卡片组 */
.feature-cards-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.feature-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.feature-card:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.08);
}

.feature-card-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
  background: linear-gradient(135deg, #f0f7ff, #e6f0ff);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 12rpx rgba(0, 120, 255, 0.1);
}

.feature-card-icon .iconfont {
  font-size: 40rpx;
  color: #0078ff;
}

.feature-card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
} 