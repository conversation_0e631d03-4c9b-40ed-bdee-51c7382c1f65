<view class="container">
  <!-- 导航栏 -->
  <nav-bar activeTab="{{activeTab}}" bindtabchange="handleTabChange" class="full-width-nav"></nav-bar>

  <!-- 产品海报区域 -->
  <view class="poster" 
        style="background: linear-gradient(135deg, #4643d3 0%, #3987ea 50%, #4ac6ec 100%);"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd">
    
    <!-- 背景元素 -->
    <view class="poster-background">
      <!-- 几何元素 -->
      <view class="geo-circle geo-small"></view>
      <view class="geo-circle geo-medium"></view>
      <view class="geo-circle geo-large"></view>
      <view class="geo-square"></view>
      <view class="geo-triangle"></view>
      
      <!-- 装饰线条 -->
      <view class="geo-line line-1"></view>
      <view class="geo-line line-2"></view>
      
      <!-- 全息投影效果 -->
      <view class="hologram-effect"></view>
    </view>
    
    <!-- 产品标题 -->
    <view class="product-title">
      <view class="title-main">{{posterTitle}}</view>
      <view class="title-divider"></view>
      <view class="title-sub">{{posterSubtitle}}</view>
      <view class="slogan">{{posterSlogan}}</view>
    </view>
    
    <!-- 促销卡片 -->
    <!-- <view class="promo-card-position">
      <promo-card initialShow="{{true}}" pageKey="{{productKey}}" bindjoinpromo="handleJoinPromo"></promo-card>
    </view> -->
    
    <!-- 底部羽化效果 -->
    <view class="poster-bottom-fade"></view>
  </view>

  <!-- 详情内容区域 -->
  <view class="detail-content">

    <!-- 企业常见问题 -->
    <view class="problem-section">
      <view class="section-header">
        <view class="section-title">企业常见问题</view>
      </view>
      <view class="subtitle-title">T+Online为您解决企业管理难题</view>
      
      <view class="problem-cards">
        <view class="problem-card">
          <view class="problem-icon">
            <text class="iconfont icon-kuaisugaoxiao"></text>
          </view>
          <view class="problem-title">业务操作繁琐</view>
          <view class="problem-desc">T+Online提供便捷操作界面，让您轻松管理业务流程</view>
        </view>
        <view class="problem-card">
          <view class="problem-icon">
            <text class="iconfont icon-tubiaozhutu"></text>
          </view>
          <view class="problem-title">财务数据不透明</view>
          <view class="problem-desc">T+Online实时同步数据，随时掌握企业财务状况</view>
        </view>
        <view class="problem-card">
          <view class="problem-icon">
            <text class="iconfont icon-dianzixinxi"></text>
          </view>
          <view class="problem-title">设备使用限制</view>
          <view class="problem-desc">T+Online支持多端访问，随时随地处理业务</view>
        </view>
        <view class="problem-card">
          <view class="problem-icon">
            <text class="iconfont icon-renminbi"></text>
          </view>
          <view class="problem-title">软件成本高昂</view>
          <view class="problem-desc">T+Online按需付费，降低企业IT成本</view>
        </view>
      </view>
    </view>

    <!-- 云财税部分 -->
    <view class="finance-comparison-container">
      <view class="section-header">
        <view class="section-title">云财税服务</view>
      </view>

      <view class="subtitle-title">全面提升企业财务管理效率</view>
      
      <view class="finance-card-container">
        <view class="finance-card">
          <view class="finance-card-title">智能管理</view>
          <view class="finance-card-subtitle">自动化财务处理</view>
          <view class="finance-card-desc">
            采用智能识别技术，自动处理票据信息，减少人工录入错误，提高财务工作效率。
          </view>
        </view>
        
        <view class="finance-card">
          <view class="finance-card-title">一体化集成</view>
          <view class="finance-card-subtitle">业财税一体化</view>
          <view class="finance-card-desc">
            将业务数据与财务数据无缝对接，实现从业务到财务再到税务的全流程管理。
          </view>
        </view>
      </view>
    </view>
    
    <!-- 创新企业价值 -->
    <view class="innovation-section">
      <view class="innovation-slogan">
        <text class="innovation-text">创新企业</text>
        <text class="innovation-dot">·</text>
        <text class="innovation-text">智慧管理</text>
        <text class="innovation-dot">·</text>
        <text class="innovation-text">未来已来</text>
      </view>
      <view class="innovation-desc">我们致力于帮助创新企业实现智能化财务管理</view>
    </view>
    

    
    <!-- T+Online功能对比 -->
    <view class="t-function-section">

      <view class="section-header">
        <view class="section-title">功能对比</view>
      </view>
      
      <view class="t-functions">
        <view class="t-func-row" wx:for="{{allFunctions}}" wx:key="name">
          <view class="t-func-col blue">
            <view class="t-func-icon">
              <text class="iconfont {{item.icon}}"></text>
            </view>
            <view class="t-func-content">
              <view class="t-func-name">{{item.name}}</view>
              <view class="t-small">{{item.desc}}</view>
            </view>
          </view>
          <view class="t-func-col gray">
            <view class="t-small">{{item.traditionalDesc}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 核心亮点部分 -->
    <view class="core-highlights-section">
      <view class="section-header">
        <view class="section-title">核心亮点</view>
      </view>
      
      <view class="highlight-list">
        <!-- 自动做账 -->
        <view class="highlight-item blue">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.autoAccounting}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">自动做账</view>
              <view class="highlight-desc">400多种凭证模板，自动生成凭证，高效精准</view>
            </view>
          </view>
        </view>
        
        <!-- 新T+UFO报表 -->
        <view class="highlight-item gray">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.ufoReport}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">新T+UFO报表</view>
              <view class="highlight-desc">支持标准财务报表，预制与自定义财务报表</view>
            </view>
          </view>
        </view>
        
        <!-- 智能期末结转 -->
        <view class="highlight-item blue">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.endPeriod}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">智能期末结转</view>
              <view class="highlight-desc">一键自动结转，期末结转完成一键生成</view>
            </view>
          </view>
        </view>
        
        <!-- 一站式凭证管理工作台 -->
        <view class="highlight-item gray">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.voucherManagement}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">凭证管理工作台</view>
              <view class="highlight-desc">集中处理模式和批量处理模式，一站式解决方案</view>
            </view>
          </view>
        </view>
        
        <!-- 跨年查询 -->
        <view class="highlight-item blue">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.crossYearQuery}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">跨年查询</view>
              <view class="highlight-desc">一个窗口连续多年账，自定义会计期间</view>
            </view>
          </view>
        </view>
        
        <!-- 多核算、多币种 -->
        <view class="highlight-item gray">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.multiAccounting}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">多核算、多币种</view>
              <view class="highlight-desc">支持多辅助核算，满足企业精细化需求</view>
            </view>
          </view>
        </view>
        
        <!-- 采购管理 -->
        <view class="highlight-item blue">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.purchaseManagement}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">采购管理</view>
              <view class="highlight-desc">灵活处理各种采购业务，自动化采购成本结算</view>
            </view>
          </view>
        </view>
        
        <!-- 销售管理 -->
        <view class="highlight-item gray">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.salesManagement}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">销售管理</view>
              <view class="highlight-desc">支持订单、销售、退换货全流程管理</view>
            </view>
          </view>
        </view>
        
        <!-- 库存核算 -->
        <view class="highlight-item blue">
          <view class="highlight-content">
            <view class="highlight-icon-wrapper">
              <text class="iconfont {{highlightIcons.inventoryAccounting}}"></text>
            </view>
            <view class="highlight-info">
              <view class="highlight-title">库存核算</view>
              <view class="highlight-desc">支持设置库存安全线，精确监督与定位库存</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 软件特色 -->
    <view class="features-section">
      <view class="section-header">
        <view class="section-title">核心优势</view>
      </view>
      
      <view class="feature-list">
        <view class="feature-row">
          <view class="feature-item">
            <view class="feature-icon"><text class="iconfont icon-dianzixinxi"></text></view>
            <view class="feature-title">随时随地</view>
            <view class="feature-desc">手机电脑都能用</view>
          </view>
          <view class="feature-item">
            <view class="feature-icon"><text class="iconfont icon-kuaisugaoxiao"></text></view>
            <view class="feature-title">高效便捷</view>
            <view class="feature-desc">操作简单易上手</view>
          </view>
        </view>
        <view class="feature-row">
          <view class="feature-item">
            <view class="feature-icon"><text class="iconfont icon-shujufenxi"></text></view>
            <view class="feature-title">数据同步</view>
            <view class="feature-desc">一次录入多处使用</view>
          </view>
          <view class="feature-item">
            <view class="feature-icon"><text class="iconfont icon-yinzhangrenzheng"></text></view>
            <view class="feature-title">安全可靠</view>
            <view class="feature-desc">专业团队保障</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 服务承诺 -->
    <view class="service-section">
      <view class="section-header">
        <view class="section-title">服务承诺</view>
      </view>
      
      <view class="service-promise">
        <view class="promise-row">
          <view class="promise-item">
            <view class="promise-icon"><text class="iconfont icon-xuanzhong"></text></view>
            <view class="promise-text">免费体验</view>
          </view>
          <view class="promise-item">
            <view class="promise-icon"><text class="iconfont icon-xuanzhong"></text></view>
            <view class="promise-text">包教包会</view>
          </view>
        </view>
        <view class="promise-row">
          <view class="promise-item">
            <view class="promise-icon"><text class="iconfont icon-xuanzhong"></text></view>
            <view class="promise-text">快速开通</view>
          </view>
          <view class="promise-item">
            <view class="promise-icon"><text class="iconfont icon-xuanzhong"></text></view>
            <view class="promise-text">终身服务</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 了解更多 -->
    <view class="more-section">
      <view class="more-title">咨询客服想要了解更多适合您的应用</view>
    </view>
    
    <!-- 底部留白 -->
    <view style="height: 120rpx;"></view>
  </view>

  <!-- 使用底部导航栏组件 -->
  <bottom-nav currentTab="hot" theme="blue" bindbottomnav="onBottomNavEvent"></bottom-nav>

  <!-- 添加悬浮咨询按钮 -->
  <float-consult positionKey="pdtonline"></float-consult>
</view> 