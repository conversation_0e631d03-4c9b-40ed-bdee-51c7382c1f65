<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <span>员工登录</span>
        </div>
      </template>
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" @keyup.enter="handleLogin">
        <el-form-item prop="loginId">
          <el-input 
            v-model="loginForm.loginId" 
            placeholder="请输入员工工号或用户名"
            clearable
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password"
            placeholder="请输入密码"
            show-password
            clearable
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleLogin" 
            :loading="loading"
            style="width: 100%;"
          >
            登 录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';
import { useAuth } from '@/store/auth';
import { useRouter } from 'vue-router';

const router = useRouter();
const { login } = useAuth();
const loginFormRef = ref(null); // 创建对表单的引用

// 将登录表单的数据模型改为响应式引用
const loginForm = ref({
  loginId: 'admin', // 将 employee_number 改为 loginId
  password: '',
});

// 定义验证规则
const rules = ref({
  loginId: [ // 将 employee_number 改为 loginId
    { required: true, message: '请输入员工工号或用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
});

const loading = ref(false);

// 登录处理函数
const handleLogin = () => {
  if (!loginFormRef.value) return;

  loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        // 调用store中的login方法
        await login(loginForm.value);
        // store中已处理跳转，但也可以在这里显式跳转
        router.push('/');
      } catch (error) {
        // 错误提示已在 request_extra.js 中统一处理
        console.error('登录组件捕获到错误:', error);
      } finally {
        loading.value = false;
      }
    } else {
      console.log('表单验证失败!');
      return false;
    }
  });
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.login-card {
  width: 400px;
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}
</style> 