# 企业权限控制修复说明

## 问题描述

根据您提供的截图，A用户在小程序中切换企业时，能够看到所有企业列表，而不是只看到自己绑定的企业。这是一个权限控制问题。

## 问题根源

1. **后端企业列表API缺乏权限过滤**：`/api/enterprises` 接口没有根据当前登录用户自动过滤企业列表
2. **小程序端直接获取所有企业**：小程序调用 `apiService.get('/enterprises')` 时没有传递用户过滤参数
3. **权限控制逻辑缺失**：系统没有根据用户类型（普通用户/员工/管理员）进行相应的权限控制

## 修复方案

### 1. 后端权限控制修复

修改了 `backend/src/controllers/enterprise.controller.js` 中的 `getAllEnterprises` 方法：

```javascript
// 权限控制：根据用户类型过滤企业
if (req.user) {
  if (req.user.type === 'user') {
    // 普通用户只能看到自己绑定的企业
    where.user_id = req.user.id;
  } else if (req.user.type === 'employee' && req.user.role !== 'admin') {
    // 非管理员员工只能看到自己负责的企业
    where.employee_id = req.user.id;
  }
  // 管理员可以看到所有企业，不添加额外过滤条件
}
```

### 2. JWT中间件增强

修改了 `backend/src/middleware/authJwt.js`，确保用户类型信息正确传递：

```javascript
// 将解码后的用户信息（通常是ID、role和type）附加到请求对象上
req.user = { 
  id: decoded.id, 
  role: decoded.role, 
  type: decoded.type // 添加用户类型信息
};
```

### 3. 小程序API方法完善

在 `minishop/utils/api.js` 中添加了企业列表获取方法：

```javascript
/**
 * 获取企业列表
 * @returns {Promise} 企业列表
 */
function getEnterprisesList() {
  return get('/enterprises');
}
```

## 权限控制逻辑

修复后的权限控制逻辑如下：

1. **普通用户（type: 'user'）**：只能看到 `user_id` 等于自己ID的企业
2. **普通员工（type: 'employee', role: 'employee'）**：只能看到 `employee_id` 等于自己ID的企业  
3. **管理员（type: 'employee', role: 'admin'）**：可以看到所有企业

## 测试验证

修复后，当A用户登录小程序时：

1. 系统会根据A用户的身份类型自动过滤企业列表
2. 如果A用户是普通用户，只会返回绑定给该用户的企业
3. 企业切换选择器中只会显示A用户有权限访问的企业

## 注意事项

1. **向后兼容**：管理员查询时仍可通过 `userId` 参数查看指定用户的企业
2. **安全性**：普通用户无法通过修改请求参数来绕过权限控制
3. **性能**：权限过滤在数据库层面进行，不会影响查询性能

## 相关文件

- `backend/src/controllers/enterprise.controller.js` - 企业控制器
- `backend/src/middleware/authJwt.js` - JWT认证中间件  
- `minishop/utils/api.js` - 小程序API工具类
- `minishop/pages/me/me.js` - 小程序个人中心页面

修复完成后，A用户在切换企业时将只能看到自己绑定的企业，解决了权限泄露问题。
