/**
 * 统一导航服务
 * 集中管理所有页面跳转逻辑
 */

// 产品页面路径配置
const PRODUCT_PAGES = {
  hkj: '/pages/pdhkj/pdhkj',   // 好会计
  ydz: '/pages/pdydz/pdydz',     // 易代账
  tonline: '/pages/pdtonline/pdtonline', // T+Online
  hyc: '/pages/pdhyc/pdhyc',     // 好云仓
  hsy: '/pages/pdhsy/pdhsy',      // 好生意
  tzsypjb: '/pages/pdtzsypjb/pdtzsypjb' // T+Cloud专属云普及版
};

// 版本详情页面路径配置
const VERSION_PAGES = {
  hkj: '/pages/versionhkj/versionhkj',   // 好会计版本
  ydz: '/pages/versionydz/versionydz',   // 易代账版本
  hyc: '/pages/versionhyc/versionhyc',   // 好云仓版本
  hsy: '/pages/versionhsy/versionhsy',    // 好生意版本
  tzsypjb: '/pages/versionhsy/versionhsy'  // T+Cloud专属云普及版暂用好生意版本页
};

// Tab页面索引
const TAB_INDICES = {
  hkj: 0,
  ydz: 1,
  tonline: 2,
  hyc: 3,
  hsy: 4,
  tzsypjb: 5
};

/**
 * 跳转到首页
 */
function navigateToHome() {
  wx.switchTab({
    url: '/pages/index/index'
  });
}

/**
 * 跳转到产品页面
 * @param {string} product - 产品标识: hkj, ydz, tonline, hyc, hsy
 * @param {string} [animationType='slide-in-right'] - 动画类型
 * @param {boolean} [useRedirect=true] - 是否使用redirectTo而不是navigateTo
 */
function navigateToProduct(product, animationType = 'slide-in-right', useRedirect = true) {
  if (!PRODUCT_PAGES[product]) {
    console.error(`未定义的产品页面: ${product}`);
    return;
  }
  
  // 获取当前页面堆栈信息
  const pages = getCurrentPages();
  
  // 如果页面栈接近上限(10页)或要求使用redirectTo，则使用redirectTo方式
  if (useRedirect || pages.length >= 8) {
    wx.redirectTo({
      url: PRODUCT_PAGES[product],
      animationType: animationType, 
      animationDuration: 350
    });
  } else {
    wx.navigateTo({
      url: PRODUCT_PAGES[product],
      animationType: animationType, 
      animationDuration: 350
    });
  }
}

/**
 * 在产品页面间横向切换
 * @param {string} currentProduct - 当前产品标识 
 * @param {string} direction - 切换方向: 'left' 或 'right'
 */
function switchProductPage(currentProduct, direction) {
  const currentIdx = TAB_INDICES[currentProduct];
  if (currentIdx === undefined) return;
  
  let targetIdx;
  let animationType;
  
  if (direction === 'left') {
    // 左滑，切换到下一个页面
    targetIdx = (currentIdx + 1) % Object.keys(TAB_INDICES).length;
    animationType = 'slide-in-right';
  } else if (direction === 'right') {
    // 右滑，切换到上一个页面
    targetIdx = (currentIdx - 1 + Object.keys(TAB_INDICES).length) % Object.keys(TAB_INDICES).length;
    animationType = 'slide-in-left';
  } else {
    return;
  }
  
  // 找到目标产品的键
  const targetProduct = Object.keys(TAB_INDICES).find(key => TAB_INDICES[key] === targetIdx);
  if (targetProduct) {
    navigateToProduct(targetProduct, animationType, true); // 使用redirectTo方式
  }
}

/**
 * 处理导航栏点击事件
 * @param {Object} e - 事件对象，包含index和id
 */
function handleNavBarTabChange(e) {
  const { index, id } = e.detail;
  
  if (!id || !PRODUCT_PAGES[id]) {
    console.error(`未定义的产品页面ID: ${id}`);
    return;
  }
  
  // 使用redirectTo方式跳转，避免页面栈累积
  wx.redirectTo({
    url: PRODUCT_PAGES[id],
    animationType: 'slide-in-right',
    animationDuration: 350
  });
}

/**
 * 跳转到产品版本详情页面
 * @param {string} product - 产品标识: hkj, ydz, hyc, hsy, tonline, tzsypjb
 */
function navigateToVersionPage(product) {
  // 对T+Online和T+专属云普及版进行特殊处理，跳转到首页
  if (product === 'tonline' || product === 'tzsypjb') {
    console.log(`产品[${product}]版本页面未上线，跳转到首页。`);
    wx.switchTab({
      url: '/pages/index/index'
    });
    return;
  }

  if (!VERSION_PAGES[product]) {
    console.error(`未定义的版本页面: ${product}`);
    // 可以选择跳转到错误页或首页作为降级方案
    wx.switchTab({
      url: '/pages/index/index'
    });
    return;
  }
  
  // 获取当前页面堆栈信息
  const pages = getCurrentPages();
  
  // 如果页面栈接近上限(10页)，则使用redirectTo方式
  if (pages.length >= 8) {
    wx.redirectTo({
      url: VERSION_PAGES[product]
    });
  } else {
    wx.navigateTo({
      url: VERSION_PAGES[product]
    });
  }
}

/**
 * 处理底部导航栏事件
 * @param {string} tab - 选项卡标识: activity, hot, me
 */
function handleBottomNav(tab) {
  switch (tab) {
    case 'activity':
      wx.redirectTo({
        url: '/pages/index/index'
      });
      break;
    case 'hot':
      wx.redirectTo({
        url: '/pages/pdhkj/pdhkj'
      });
      break;
    case 'me':
      wx.redirectTo({
        url: '/pages/me/me'
      });
      break;
    default:
      console.error(`未处理的底部导航标签: ${tab}`);
  }
}

/**
 * 拨打咨询电话
 * @param {string} phoneNumber - 电话号码，默认客服电话
 */
function makePhoneCall(phoneNumber = '131-0068-5010') {
  wx.makePhoneCall({
    phoneNumber: phoneNumber,
    success: function() {
      console.log("拨打电话成功");
    },
    fail: function() {
      console.log("拨打电话失败");
    }
  });
}

/**
 * 跳转到试用/咨询页面
 * @param {string} product - 产品标识
 * @param {string} type - 类型: 'trial'(试用) 或 'consult'(咨询)
 */
function navigateToProductService(product, type) {
  let url = '';
  
  if (type === 'trial') {
    url = `/pages/trial/trial?product=${product}`;
  } else if (type === 'consult') {
    url = `/pages/consult/consult?product=${product}`;
  } else {
    console.error(`未定义的服务类型: ${type}`);
    return;
  }
  
  // 获取当前页面堆栈信息
  const pages = getCurrentPages();
  
  // 如果页面栈接近上限(10页)，则使用redirectTo方式
  if (pages.length >= 8) {
    wx.redirectTo({
      url: url
    });
  } else {
    wx.navigateTo({
      url: url
    });
  }
}

/**
 * 处理促销活动
 * @param {string} promoId - 促销活动ID
 */
function joinPromotion(promoId) {
  // 获取当前页面堆栈信息
  const pages = getCurrentPages();
  
  const url = `/pages/promotion/join?id=${promoId}`;
  
  // 如果页面栈接近上限(10页)，则使用redirectTo方式
  if (pages.length >= 8) {
    wx.redirectTo({
      url: url
    });
  } else {
    wx.navigateTo({
      url: url
    });
  }
}

module.exports = {
  navigateToHome,
  navigateToProduct,
  switchProductPage,
  navigateToVersionPage,
  handleBottomNav,
  makePhoneCall,
  navigateToProductService,
  joinPromotion,
  PRODUCT_PAGES,
  TAB_INDICES,
  handleNavBarTabChange
}; 