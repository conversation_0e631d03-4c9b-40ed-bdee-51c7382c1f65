<script setup>
import { ref, computed, nextTick } from 'vue';
import CrudPage from '@/components/CrudPage.vue';
import UserFormHeader from '../components/UserFormHeader.vue';
import { getUsers, createUser, updateUser, deleteUser, getNextUserId, getNextPartnerId } from '@/api/user.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDateTime } from '@/utils/format.js';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 1. 定义表格的列
const columns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'user_id', label: '用户ID', width: 160 },
  { prop: 'name', label: '姓名', width: 120, isSlot: true },
  { prop: 'nickname', label: '昵称', width: 120 },
  { prop: 'mobile', label: '手机号', width: 150 },
  { prop: 'email', label: '邮箱', width: 180, showOverflowTooltip: true },
  { prop: 'is_partner', label: '合伙人', width: 100, isSlot: true },
  { prop: 'partner_id', label: '合伙人ID', width: 150 },
  { prop: 'createdAt', label: '创建时间', width: 180, isSlot: true },
  { prop: 'updatedAt', label: '修改时间', width: 180, isSlot: true },
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
]);

// 2. 定义API函数
const api = {
  list: getUsers,
  create: createUser,
  update: updateUser,
  delete: deleteUser,
};

// 3. 状态管理
const dialogVisible = ref(false);
const isEditMode = ref(false); // true: 编辑, false: 新增/复制
const crudPageRef = ref(null);
const formHeaderRef = ref(null);
const selectedItems = ref([]);

// 3.2 表单数据模型及特殊逻辑
const getInitialForm = () => ({
  user_id: '',
  name: '',
  nickname: '',
  mobile: '',
  email: '',
  password: '',
  confirmPassword: '',
  is_partner: false,
  partner_id: null,
  commission_base: null,
  commission_extra: null,
  remark: ''
});

const form = ref(getInitialForm());

// 4. 计算属性
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);
const isViewDisabled = computed(() => selectedItems.value.length !== 1);

// 5. 处理合伙人状态变化
const handlePartnerChange = async (isPartner) => {
  if (isPartner && !form.value.partner_id) {
    try {
      const res = await getNextPartnerId();
      form.value.partner_id = res.next_id;
      // 自动生成ID后，清除可能的验证错误
      nextTick(() => {
        formHeaderRef.value?.formRef?.clearValidate('partner_id');
      });
    } catch (error) {
      ElMessage.error('获取新合伙人ID失败，请手动输入一个唯一的ID。');
    }
  }
};

// 6. 方法
const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const handleOpenDialog = async (isEdit, isCopy, data) => {
  isEditMode.value = isEdit;
  
  if (isEdit) {
    form.value = { ...data };
  } else if (isCopy) {
    const copiedData = { ...data };
    delete copiedData.id;
    delete copiedData.createdAt;
    delete copiedData.updatedAt;
    delete copiedData.password; // 复制时不复制密码
    
    try {
      const res = await getNextUserId();
      copiedData.user_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新用户ID失败，请输入一个唯一的ID。');
    }
    
    form.value = copiedData;
    isEditMode.value = false;
  } else {
    form.value = getInitialForm();
    try {
      const res = await getNextUserId();
      form.value.user_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新用户ID失败，请输入一个唯一的ID。');
    }
  }
  dialogVisible.value = true;
};

const handleDelete = async (ids) => {
  await ElMessageBox.confirm(`确定删除选中的 ${ids.length} 位用户吗？`, '警告', { type: 'warning' });
  try {
    await Promise.all(ids.map(id => api.delete(id)));
    ElMessage.success('删除成功');
    crudPageRef.value?.loadData();
    selectedItems.value = [];
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

const handleBatchDelete = () => {
  if (isDeleteDisabled.value) return;
  const idsToDelete = selectedItems.value.map(item => item.id);
  handleDelete(idsToDelete);
};

// 查看用户详情
const handleView = () => {
  if (isViewDisabled.value) return;
  const user = selectedItems.value[0];
  router.push({ name: 'user-detail', params: { id: user.id } });
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const isValid = await formHeaderRef.value?.validate();
    if (!isValid) return;

    const dataToSend = { ...form.value };
    if (!dataToSend.password) delete dataToSend.password;
    // 移除确认密码字段，不发送到后端
    delete dataToSend.confirmPassword;

    if (isEditMode.value) {
      await api.update(form.value.id, dataToSend);
      ElMessage.success('更新成功');
    } else {
      if (!form.value.password) {
        ElMessage.error('新增用户时必须设置密码');
        return;
      }
      await api.create(dataToSend);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
    ElMessage.error((isEditMode.value ? '更新' : '创建') + '失败: ' + (error.response?.data?.message || error.message));
  }
};
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="用户"
    :columns="columns"
    :api-list="api.list"
    :api-create="api.create"
    :api-update="api.update"
    :api-delete="api.delete"
    :hide-row-actions="true"
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 自定义顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="handleOpenDialog(false, false, null)">新增用户</el-button>
      <el-button type="success" @click="handleView" :disabled="isViewDisabled">查看详情</el-button>
      <el-tooltip content="请选择一位用户进行修改" :disabled="!isEditDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;">
          <el-button type="default" :disabled="isEditDisabled" @click="handleOpenDialog(true, false, selectedItems[0])">修改</el-button>
        </div>
      </el-tooltip>
      <el-tooltip content="请选择一位用户进行复制" :disabled="!isCopyDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;">
          <el-button type="info" :disabled="isCopyDisabled" @click="handleOpenDialog(false, true, selectedItems[0])">复制</el-button>
        </div>
      </el-tooltip>
      <el-tooltip content="请选择用户进行删除" :disabled="!isDeleteDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;">
          <el-button type="danger" :disabled="isDeleteDisabled" @click="handleBatchDelete">删除</el-button>
        </div>
      </el-tooltip>
    </template>

    <!-- 2. 自定义列渲染 -->
    <template #col-name="{ row }">
      <el-link type="primary" @click="router.push({ name: 'user-detail', params: { id: row.id } })">{{ row.name }}</el-link>
    </template>
    <template #col-is_partner="{ row }">
      <el-tag :type="row.is_partner ? 'success' : 'info'" size="small">
        {{ row.is_partner ? '是' : '否' }}
      </el-tag>
    </template>
    <template #col-createdAt="{ row }">{{ formatDateTime(row.createdAt) }}</template>
    <template #col-updatedAt="{ row }">{{ formatDateTime(row.updatedAt) }}</template>

    <!-- 3. 自定义弹窗实现 -->
    <template #dialog>
      <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑用户' : '新增用户'" width="800px" :close-on-click-modal="false">
        <UserFormHeader
          :formData="form"
          :readonly="false"
          :isEditMode="isEditMode"
          @partner-change="handlePartnerChange"
          ref="formHeaderRef"
        />
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>
</template>

<style scoped>
.page-container {
  padding: 20px;
}
.action-bar {
  margin-bottom: 20px;
}
</style>
