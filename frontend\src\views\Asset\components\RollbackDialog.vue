<template>
  <el-dialog
    v-model="visible"
    title="确认回滚操作"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="rollback-content" v-if="rollbackRecord">
      <el-alert
        title="回滚操作说明"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>您即将回滚到以下变更记录的状态：</p>
          <ul class="rollback-info">
            <li><strong>变更ID:</strong> {{ rollbackRecord.asset_change_id }}</li>
            <li><strong>变更时间:</strong> {{ formatDateTime(rollbackRecord.created_at) }}</li>
            <li><strong>制单人:</strong> {{ getCreatorName(rollbackRecord.created_by) }}</li>
          </ul>
        </template>
      </el-alert>
      
      <p class="warning-text">
        <strong>注意:</strong> 回滚操作将会恢复资产到此变更前的状态，并删除此变更之后的所有变更记录。此操作不可撤销！
      </p>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="danger" 
          :loading="loading"
          @click="handleConfirm"
        >
          确定回滚
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  rollbackRecord: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取创建者姓名
const getCreatorName = (createdBy) => {
  // 这里可以根据实际需求处理用户名显示逻辑
  return createdBy || '未知'
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.rollback-content {
  margin-bottom: 20px;
}

.rollback-info {
  margin: 10px 0;
  padding-left: 20px;
}

.rollback-info li {
  margin: 5px 0;
}

.warning-text {
  margin-top: 15px;
  color: #e6a23c;
  font-size: 14px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
