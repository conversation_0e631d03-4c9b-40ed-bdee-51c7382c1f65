/**
 * API服务工具类
 * 封装与后端API的通信方法
 * 统一处理请求、响应、错误等
 */

// 后端API基础地址 - 根据实际部署情况修改
// 生产环境：使用HTTPS域名
const BASE_URL = 'https://service.bogoo.net/api';  // 你的新域名
// 开发环境：使用服务器IP（需要在开发者工具中关闭域名校验）
// const BASE_URL = 'http://*************:3002/api';

// 请求超时时间（毫秒）
const REQUEST_TIMEOUT = 10000;

/**
 * 统一的HTTP请求方法
 * @param {string} url - 请求地址
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求结果
 */
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const token = wx.getStorageSync('access_token');
    
    // 默认请求配置
    const defaultOptions = {
      url: `${BASE_URL}${url}`,
      method: 'GET',
      timeout: REQUEST_TIMEOUT,
      header: {
        'Content-Type': 'application/json',
        // 如果有token，添加到请求头
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      },
      success: (res) => {
        console.log(`API请求成功 [${options.method || 'GET'}] ${url}:`, res);
        
        // 检查HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 区分是登录失败还是token过期
          if (url.includes('/auth/password-login') || url.includes('/auth/phone-login')) {
            // 登录接口的401错误，返回详细错误信息而不是reject
            resolve(res.data);
          } else {
            // 其他接口的401错误，说明token过期
            wx.removeStorageSync('access_token');
            wx.removeStorageSync('user_info');
            wx.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
              duration: 2000
            });
            reject(new Error('登录已过期'));
          }
        } else if (res.statusCode === 400 && (url.includes('/auth/password-login') || url.includes('/auth/phone-login'))) {
          // 登录接口的400错误（输入验证失败），返回详细错误信息
          resolve(res.data);
        } else {
          // 根据状态码提供更友好的错误信息
          let errorMessage = res.data?.message || '请求失败';

          switch (res.statusCode) {
            case 400:
              errorMessage = res.data?.message || '请求参数错误';
              break;
            case 403:
              errorMessage = '没有权限访问该资源';
              break;
            case 404:
              errorMessage = '请求的资源不存在';
              break;
            case 500:
              errorMessage = '服务器内部错误，请稍后重试';
              break;
            case 502:
            case 503:
            case 504:
              errorMessage = '服务暂时不可用，请稍后重试';
              break;
            default:
              errorMessage = res.data?.message || `请求失败，状态码：${res.statusCode}`;
          }

          reject(new Error(errorMessage));
        }
      },
      fail: (err) => {
        console.error(`API请求失败 [${options.method || 'GET'}] ${url}:`, err);
        
        // 网络错误处理
        if (err.errMsg && err.errMsg.includes('timeout')) {
          reject(new Error('请求超时，请检查网络连接'));
        } else if (err.errMsg && err.errMsg.includes('fail')) {
          reject(new Error('网络连接失败，请检查网络设置'));
        } else {
          reject(new Error(err.errMsg || '网络请求失败'));
        }
      }
    };
    
    // 合并配置
    const finalOptions = { ...defaultOptions, ...options };
    finalOptions.url = `${BASE_URL}${url}`;
    
    // 发起请求
    wx.request(finalOptions);
  });
}

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
function get(url, params = {}) {
  // 构建查询字符串
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const fullUrl = queryString ? `${url}?${queryString}` : url;
  
  return request(fullUrl, { method: 'GET' });
}

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @returns {Promise} 请求结果
 */
function post(url, data = {}) {
  return request(url, {
    method: 'POST',
    data: data
  });
}

/**
 * PUT请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @returns {Promise} 请求结果
 */
function put(url, data = {}) {
  return request(url, {
    method: 'PUT',
    data: data
  });
}

/**
 * DELETE请求
 * @param {string} url - 请求地址
 * @returns {Promise} 请求结果
 */
function del(url) {
  return request(url, { method: 'DELETE' });
}

// ==================== 用户认证相关API ====================





/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
function getCurrentUser() {
  return get('/auth/me');
}

/**
 * 刷新token
 * @param {string} refreshToken - 刷新token
 * @returns {Promise} 新的token信息
 */
function refreshToken(refreshToken) {
  return post('/auth/refresh', { refresh_token: refreshToken });
}

// ==================== 资产相关API ====================

/**
 * 获取用户的资产列表
 * @param {Object} params - 查询参数
 * @param {string} params.q - 搜索关键词
 * @param {number} params.enterpriseId - 企业ID
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 资产列表
 */
function getAssetsList(params = {}) {
  return get('/assets', params);
}

/**
 * 获取资产详情
 * @param {number} assetId - 资产ID
 * @returns {Promise} 资产详情
 */
function getAssetDetail(assetId) {
  return get(`/assets/${assetId}`);
}

/**
 * 获取资产变更记录
 * @param {number} assetId - 资产ID
 * @param {Object} params - 查询参数
 * @returns {Promise} 变更记录列表
 */
function getAssetChanges(assetId, params = {}) {
  return get(`/assets/${assetId}/changes`, params);
}

// ==================== 订单相关API ====================

/**
 * 获取用户订单列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 订单列表
 */
function getOrdersList(params = {}) {
  return get('/orders', params);
}

/**
 * 获取订单详情
 * @param {number} orderId - 订单ID
 * @returns {Promise} 订单详情
 */
function getOrderDetail(orderId) {
  return get(`/orders/${orderId}`);
}

// ==================== 产品相关API ====================

/**
 * 获取产品列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 产品列表
 */
function getProductsList(params = {}) {
  return get('/products', params);
}

/**
 * 获取产品详情
 * @param {number} productId - 产品ID
 * @returns {Promise} 产品详情
 */
function getProductDetail(productId) {
  return get(`/products/${productId}`);
}

// ==================== 企业相关API ====================

/**
 * 获取企业列表
 * @returns {Promise} 企业列表
 */
function getEnterprisesList() {
  return get('/enterprises');
}

// ==================== 工具方法 ====================

/**
 * 保存用户登录信息到本地存储
 * @param {Object} loginData - 登录返回的数据
 */
function saveLoginInfo(loginData) {
  // 支持两种token字段名：token 和 access_token
  const token = loginData.token || loginData.access_token;
  if (token) {
    wx.setStorageSync('access_token', token);
  }
  if (loginData.refresh_token) {
    wx.setStorageSync('refresh_token', loginData.refresh_token);
  }
  if (loginData.user) {
    wx.setStorageSync('user_info', loginData.user);
  }
}

/**
 * 清除用户登录信息
 */
function clearLoginInfo() {
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('refresh_token');
  wx.removeStorageSync('user_info');
}

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('access_token');
  return !!token;
}

/**
 * 获取本地存储的用户信息
 * @returns {Object|null} 用户信息
 */
function getLocalUserInfo() {
  return wx.getStorageSync('user_info') || null;
}

module.exports = {
  // HTTP方法
  request,
  get,
  post,
  put,
  del,

  // 用户认证
  getCurrentUser,
  refreshToken,

  // 资产管理
  getAssetsList,
  getAssetDetail,
  getAssetChanges,

  // 订单管理
  getOrdersList,
  getOrderDetail,

  // 产品管理
  getProductsList,
  getProductDetail,

  // 企业管理
  getEnterprisesList,

  // 工具方法
  saveLoginInfo,
  clearLoginInfo,
  isLoggedIn,
  getLocalUserInfo,

  // 常量
  BASE_URL
};
