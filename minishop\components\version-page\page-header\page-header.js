// components/version-page/page-header/page-header.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 产品名称
    productName: {
      type: String,
      value: ''
    },
    // 促销信息
    promotionInfo: {
      type: Object,
      value: {}
    },
    // 活动时间范围
    activityTimeRange: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    countdownDays: 0,
    countdownHours: 0,
    countdownMinutes: 0,
    countdownSeconds: 0,
    countdownTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      wx.navigateBack({
        delta: 1,
        fail: function() {
          // 如果返回失败（没有上一页），则跳转到首页
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }
      });
    },

    /**
     * 启动倒计时
     */
    startCountdown() {
      if (!this.properties.promotionInfo.endTime) return;
      
      const updateCountdown = () => {
        let endTimeStr = this.properties.promotionInfo.endTime;
        if (!endTimeStr) {
          this.setData({
            countdownDays: 0,
            countdownHours: 0,
            countdownMinutes: 0,
            countdownSeconds: 0
          });
          return;
        }
        
        // 统一格式：YYYY/MM/DD HH:mm:ss
        endTimeStr = endTimeStr.replace(/-/g, '/').replace('T', ' ');
        if (/^\d{4}\/\d{2}\/\d{2}$/.test(endTimeStr)) {
          endTimeStr += ' 23:59:59';
        }
        
        // 兼容部分安卓/微信开发工具
        let endDate;
        if (/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/.test(endTimeStr)) {
          const parts = endTimeStr.match(/(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
          endDate = new Date(
            parseInt(parts[1]),
            parseInt(parts[2]) - 1,
            parseInt(parts[3]),
            parseInt(parts[4]),
            parseInt(parts[5]),
            parseInt(parts[6])
          );
        } else {
          endDate = new Date(endTimeStr);
        }
        
        const now = new Date();
        const distance = endDate.getTime() - now.getTime();
        
        if (isNaN(distance) || distance <= 0) {
          this.setData({
            countdownDays: 0,
            countdownHours: 0,
            countdownMinutes: 0,
            countdownSeconds: 0
          });
          
          if (this.data.countdownTimer) {
            clearInterval(this.data.countdownTimer);
            this.setData({ countdownTimer: null });
          }
          return;
        }
        
        // 计算天、时、分、秒
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        this.setData({
          countdownDays: days,
          countdownHours: hours,
          countdownMinutes: minutes,
          countdownSeconds: seconds
        });
      };
      
      // 立即执行一次
      updateCountdown();
      
      // 每秒更新一次
      const timer = setInterval(updateCountdown, 1000);
      this.setData({ countdownTimer: timer });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      this.startCountdown();
    },
    
    detached() {
      // 组件实例被从页面节点树移除时执行
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面被展示时执行
      this.startCountdown();
    },
    
    hide() {
      // 页面被隐藏时执行
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
      }
    }
  }
});
