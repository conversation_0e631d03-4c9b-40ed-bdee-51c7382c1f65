/**
 * 618促销活动小程序
 * @file 小程序入口文件
 */
App({
  onLaunch: function () {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    // 隐私协议同意状态（合规要求）
    agreedProtocol: false,
    // 促销信息
    promotionInfo: {
      title: '618嗨购季',
      startTime: '2025/07/07',
      endTime: '2025/07/31 23:59:59',
      slogan: '全年最佳入手期，不容错过的超值优惠！',
      rules: [
        {period: 1, discount: 0.9}, // 1年85折不送月份
        {period: 2, discount: 0.85, gift: 2}, // 2年85折不送月份
        {period: 3, discount: 0.8, gift: 3} // 3年8折送3个月
      ],
      // gifts: [
      //   {threshold: 1000, name: '小米充电宝/小米热水壶/小米音箱'},
      //   {threshold: 2000, name: '小米电饭煲/小米电动牙刷/小米电风扇'},
      //   {threshold: 4000, name: '小米除螨仪/小米加湿器/小米电磁炉'}
      // ]
    }
  }
}) 