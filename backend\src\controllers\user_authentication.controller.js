const db = require('../models');
const UserAuthentication = db.UserAuthentication;

// 获取单个用户的认证信息
exports.getAuthenticationByUserId = async (req, res) => {
  try {
    const { userId } = req.params;
    const authentication = await UserAuthentication.findOne({
      where: { user_id: userId }
    });

    if (authentication) {
      res.status(200).json(authentication);
    } else {
      res.status(404).json({ message: '未找到该用户的认证信息' });
    }
  } catch (error) {
    res.status(500).json({ success: false, message: '获取认证信息失败', error: error.message });
  }
};

// 创建或更新用户的认证信息 (Upsert)
exports.upsertAuthentication = async (req, res) => {
  try {
    const { userId } = req.params;
    const data = req.body;

    // 找到或创建记录
    const [authentication, created] = await UserAuthentication.findOrCreate({
      where: { user_id: userId },
      defaults: { ...data, user_id: userId }
    });

    // 如果不是新创建的，就更新它
    if (!created) {
      await authentication.update(data);
    }

    res.status(200).json({ success: true, data: authentication, created: created });
  } catch (error) {
    console.error('保存或更新认证信息时发生严重错误:', error);
    res.status(500).json({ success: false, message: '保存认证信息失败', error: error.message });
  }
}; 