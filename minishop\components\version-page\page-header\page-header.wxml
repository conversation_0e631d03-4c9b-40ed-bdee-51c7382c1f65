<!--components/version-page/page-header/page-header.wxml-->
<!-- 返回按钮 -->
<view class="back-button" bindtap="goBack">
  <view class="back-icon"></view>
</view>

<!-- 顶部活动信息 -->
<view class="promotion-header theme-618">
  <view class="promotion-content">
    <view class="promotion-title">{{productName}} 618年中大促</view>
    <view class="promotion-subtitle">全年最低价，就现在！</view>

    <!-- 添加活动时间显示 -->
    <view class="activity-time">活动时间：{{activityTimeRange}}</view>

    <!-- 倒计时 -->
    <view class="countdown-box">
      <text class="countdown-text">距离结束还剩：</text>
      <view class="countdown-time">
        <text class="countdown-unit">{{countdownDays}}</text>
        <text class="countdown-text">天</text>
        <text class="countdown-unit">{{countdownHours < 10 ? '0' + countdownHours : countdownHours}}</text>
        <text class="countdown-text">时</text>
        <text class="countdown-unit">{{countdownMinutes < 10 ? '0' + countdownMinutes : countdownMinutes}}</text>
        <text class="countdown-text">分</text>
        <text class="countdown-unit">{{countdownSeconds < 10 ? '0' + countdownSeconds : countdownSeconds}}</text>
        <text class="countdown-text">秒</text>
      </view>
    </view>
  </view>

  <!-- 618装饰元素 -->
  <view class="promo-decoration">618</view>
</view>
