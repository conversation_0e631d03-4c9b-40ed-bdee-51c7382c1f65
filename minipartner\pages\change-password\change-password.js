/**
 * 合伙人小程序修改密码页面
 * 允许合伙人修改登录密码
 * 修改成功后需要重新登录
 */

const app = getApp();
const api = require('../../utils/api');

// 合伙人修改密码页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    oldPassword: '',        // 原密码
    newPassword: '',        // 新密码
    confirmPassword: '',    // 确认新密码
    submitting: false       // 是否正在提交请求
  },

  /**
   * 页面加载时检查登录状态
   */
  onLoad() {
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态和合伙人权限
   */
  checkLoginStatus() {
    return api.checkPartnerAuth();
  },

  /**
   * 处理输入框输入
   */
  handleInput: function(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [field]: e.detail.value
    });
  },

  /**
   * 提交密码修改
   */
  handleSubmit: function() {
    const { oldPassword, newPassword, confirmPassword } = this.data;
    
    // 表单验证
    if (!oldPassword) {
      return this.showError('请输入原密码');
    }
    
    if (!newPassword) {
      return this.showError('请输入新密码');
    }
    
    if (newPassword.length < 6) {
      return this.showError('新密码不能少于6位');
    }
    
    if (newPassword !== confirmPassword) {
      return this.showError('两次输入的密码不一致');
    }
    
    this.setData({ submitting: true });
    wx.showLoading({ title: '提交中...' });

    // 调用修改密码API
    api.changePassword({
      oldPassword,
      newPassword
    }).then(res => {
      console.log('修改密码响应:', res);

      if (res && res.success !== false) {
        wx.hideLoading();
        wx.showModal({
          title: '修改成功',
          content: '密码已修改成功，请重新登录',
          showCancel: false,
          success: () => {
            // 清除登录信息并返回登录页
            api.clearLoginInfo();
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      } else {
        throw new Error(res.message || '修改密码失败');
      }
    }).catch(error => {
      console.error('修改密码失败:', error);
      wx.hideLoading();
      this.showError(error.message || '修改密码失败');
    }).finally(() => {
      this.setData({ submitting: false });
    });
  },

  /**
   * 显示错误信息
   */
  showError: function(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
});