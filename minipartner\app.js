/**
 * 小程序入口文件
 */
App({
  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,        // 用户信息
    token: null,            // 登录令牌
    isPartner: false,     // 是否是合伙人
    isLoggedIn: false,    // 是否已登录
    systemInfo: null,      // 系统信息
    needAuth: true,        // 是否需要认证（新增）
    isFirstLaunch: true    // 是否是首次启动
  },

  /**
   * 小程序启动时
   */
  onLaunch: function() {
    // 获取系统信息
    this.getSystemInfo();
    
    // 添加全局错误捕获
    wx.onError((err) => {
      console.error('全局捕获到错误:', err);
    });
    
    // 检查登录状态
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.isLoggedIn = true;
      this.globalData.userInfo = userInfo;
      this.globalData.token = token;
    } else {
      this.globalData.isLoggedIn = false;
      // 不在这里进行跳转，而是标记未登录状态
    }
    
    // 使用全局数据记录首次加载状态
    this.globalData.isFirstLaunch = true;
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      // 设置自定义导航栏高度
      this.globalData.navBarHeight = 
        systemInfo.statusBarHeight + 
        (systemInfo.platform === 'android' ? 48 : 44);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: async function() {
    try {
      // 获取存储的token
      const token = wx.getStorageSync('token');
      if (token) {
        this.globalData.token = token;
        this.globalData.isLoggedIn = true;
        // TODO: 验证token有效性
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  },

  /**
   * 登录方法
   */
  login: async function() {
    try {
      // 显示加载提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取微信登录凭证
      const { code } = await wx.login();
      
      // TODO: 调用后端登录接口
      console.log('登录凭证:', code);

      // 模拟登录成功
      this.globalData.isLoggedIn = true;
      this.globalData.userInfo = {
        nickName: '测试用户',
        avatarUrl: '/images/default-avatar.png'
      };

    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 退出登录
   */
  logout: function() {
    // 清除登录状态
    this.globalData.token = null;
    this.globalData.isLoggedIn = false;
    this.globalData.userInfo = null;
    this.globalData.isPartner = false;
    // 清除存储
    wx.clearStorageSync();
  },

  /**
   * 检查登录状态并处理重定向
   * 在需要登录才能访问的页面的onLoad或onShow中调用
   */
  checkLoginRedirect: function() {
    // 不需要重复检查已登录状态
    if (this.globalData.isLoggedIn) {
      return true;
    }
    
    // 首次加载时不进行重定向
    if (this.globalData.isFirstLaunch) {
      this.globalData.isFirstLaunch = false;
      return false;
    }
    
    // 已经在onLaunch中处理了初始跳转，这里只处理后续的页面访问
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      
      // 不需要登录验证的白名单页面
      const whiteList = ['pages/login/login', 'pages/register/register'];
      const currentPageInWhiteList = whiteList.some(page => currentRoute.includes(page));
      
      if (!currentPageInWhiteList) {
        console.log('访问被保护页面，重定向到登录页面');
        wx.redirectTo({
          url: '/pages/login/login'
        });
        return false;
      }
    }
    
    return true;
  }
}); 