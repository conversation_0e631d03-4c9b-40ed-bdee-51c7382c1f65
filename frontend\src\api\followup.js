import service from '@/utils/request_extra.js';

const API_PATH = '/followups';

// 根据企业ID获取所有跟进记录
export const getFollowupsByEnterpriseId = (enterpriseId) => {
  return service.get(`${API_PATH}/enterprise/${enterpriseId}`);
};

// 创建一个新的跟进记录 (现在使用 FormData)
export const createFollowup = (formData) => {
  return service.post(API_PATH, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 更新一个指定ID的跟进记录
export const updateFollowup = (id, followupData) => {
  return service.put(`${API_PATH}/${id}`, followupData);
};

// 删除一个指定ID的跟进记录
export const deleteFollowup = (id) => {
  return service.delete(`${API_PATH}/${id}`);
}; 