const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 产品订单表体模型 (order_product_item)
 * 存储产品订单的详细信息，严格按照图片要求设计
 */
const OrderProductItem = sequelize.define('OrderProductItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键'
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联order_head.id'
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联product.id'
  },
  user_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '使用人数'
  },
  account_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '账套数'
  },
  duration_months: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '购买时长(月)'
  },
  selected_features: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '所选功能ID列表'
  },
  standard_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '产品标准价'
  },
  discount_rate: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: true,
    defaultValue: 1.0000,
    comment: '折扣率'
  },
  other_discount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '其他优惠'
  },
  actual_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '实付价格'
  },
  activity_other: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '其他活动说明'
  }
}, {
  tableName: 'order_product_item',
  timestamps: false, // 产品订单明细不需要时间戳
  underscored: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  hooks: {
    // 在插入前自动计算实付价格
    beforeCreate: (item) => {
      item.actual_price = item.standard_price * item.discount_rate - item.other_discount;
    },
    // 在更新前自动计算实付价格
    beforeUpdate: (item) => {
      item.actual_price = item.standard_price * item.discount_rate - item.other_discount;
    }
  }
});

module.exports = OrderProductItem;