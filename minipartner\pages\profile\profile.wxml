<view class="profile-container">
  <!-- 背景装饰元素 -->
  <view class="tech-background">
    <view class="tech-circle circle-1"></view>
    <view class="tech-circle circle-2"></view>
    <view class="tech-line line-1"></view>
    <view class="tech-line line-2"></view>
    <view class="tech-line line-3"></view>
  </view>

  <!-- 顶部区域 - 用户信息 -->
  <view class="profile-header">
    <view class="header-content">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" />
        <view class="user-details">
          <text class="nickname">{{userInfo.nickname || userInfo.name || '未设置昵称'}}</text>
          <view class="partner-code-container">
            <text class="partner-code">专属码：{{userInfo.partner_id || '--'}}</text>
            <view class="copy-btn" bindtap="copyPartnerCode">复制</view>
          </view>
        </view>
      </view>
      <view class="auth-button" bindtap="goToAuthInfo">
        <text>认证信息</text>
        <text class="icon-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 核心内容区域 -->
  <view class="cards-section">
    <!-- 我的收益卡片 - 突出显示 -->
    <view class="earnings-card">
      <view class="earnings-glow"></view>
      <view class="earnings-title">
        <text class="section-icon">💰</text>
        <text>我的收益</text>
      </view>
      
      <view class="earnings-data">
        <view class="earnings-item">
          <view class="earnings-value-container">
            <text class="earnings-currency">¥</text>
            <text class="earnings-value">{{pending_earnings}}</text>
          </view>
          <text class="earnings-label">待发放收益</text>
        </view>

        <view class="earnings-divider"></view>

        <view class="earnings-item">
          <view class="earnings-value-container">
            <text class="earnings-currency">¥</text>
            <text class="earnings-value">{{paid_earnings}}</text>
          </view>
          <text class="earnings-label">已发放收益</text>
        </view>

        <view class="earnings-divider"></view>

        <view class="earnings-item">
          <view class="earnings-value-container">
            <text class="earnings-currency">¥</text>
            <text class="earnings-value">{{total_earnings}}</text>
          </view>
          <text class="earnings-label">累计总收益</text>
        </view>
      </view>
      
      <button class="detail-button" bindtap="goToEarningsDetail">
        <text>查看详情</text>
        <text class="arrow-icon">→</text>
      </button>
    </view>

    <!-- 佣金比例显示 -->
    <view class="commission-card">
      <view class="commission-title">
        <text class="section-icon">📊</text>
        <text>佣金比例</text>
      </view>

      <view class="commission-details">
        <view class="commission-item">
          <view class="commission-info">
            <text class="commission-label">基础佣金</text>
            <text class="commission-value base">{{commission_base * 100}}%</text>
          </view>
        </view>

        <view class="commission-divider">+</view>

        <view class="commission-item">
          <view class="commission-info">
            <text class="commission-label">额外佣金</text>
            <text class="commission-value extra">{{commission_extra * 100}}%</text>
          </view>
        </view>

        <view class="commission-divider">=</view>

        <view class="commission-item total">
          <view class="commission-info">
            <text class="commission-label">总佣金</text>
            <text class="commission-value total">{{commission_total}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 结算规则卡片 -->
    <view class="rules-card">
      <view class="card-title">
        <text class="section-icon">📋</text>
        <text>结算规则</text>
      </view>
      
      <view class="rules-list">
        <view class="rule-item">
          <view class="rule-dot"></view>
          <view class="rule-text">佣金结算时间：每月10日统一结算上月佣金并完成打款</view>
        </view>
        
        <view class="rule-item">
          <view class="rule-dot"></view>
          <view class="rule-text">个税代扣说明：平台将根据国家相关法律法规，代扣代缴个人所得税</view>
        </view>
        
        <view class="rule-item">
          <view class="rule-dot"></view>
          <view class="rule-text">结算方式：佣金将直接打款至您绑定的银行卡账户</view>
        </view>
        
        <view class="rule-item">
          <view class="rule-dot"></view>
          <view class="rule-text">特殊说明：如遇法定节假日，付款时间可能会相应顺延</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能按钮区域 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-button" bindtap="goToAuthInfo">
        <text class="button-icon">🏦</text>
        <text class="button-text">认证信息</text>
      </button>

      <button class="action-button" bindtap="goToChangePassword">
        <text class="button-icon">🔐</text>
        <text class="button-text">修改密码</text>
      </button>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-section">
    <button class="logout-button" bindtap="handleLogout">退出登录</button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view> 
