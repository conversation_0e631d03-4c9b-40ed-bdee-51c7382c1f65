/**
 * 企业、用户、资产三者联动逻辑的 Composable
 * 
 * 业务规则：
 * 1. 填了企业ID → 过滤出关联的用户ID、资产ID
 * 2. 填了资产ID → 自动带出企业ID，过滤出关联的用户ID
 * 3. 填了用户ID → 过滤出关联的企业ID、资产ID，如果用户没有关联企业和资产，则企业ID、资产ID可填可不填
 * 4. 约束条件：企业ID、用户ID不可同时为空
 * 5. 显示要求：带出的是名字，不是ID
 * 6. 注意：填写订单信息时，企业、资产、用户3个字段的填写没有先后顺序
 */

import { ref, computed } from 'vue'
import { getEnterprises } from '@/api/enterprise'
import { getUsers } from '@/api/user'
import { getAssets } from '@/api/asset'

export function useEntityLinkage() {
  // 响应式数据
  const enterprises = ref([])
  const users = ref([])
  const assets = ref([])
  
  // 加载状态
  const enterpriseLoading = ref(false)
  const userLoading = ref(false)
  const assetLoading = ref(false)
  
  // 当前选中的值
  const selectedEnterprise = ref(null)
  const selectedUser = ref(null)
  const selectedAsset = ref(null)
  
  // 计算属性：验证企业和用户不能同时为空
  const isValid = computed(() => {
    return selectedEnterprise.value || selectedUser.value
  })
  
  // 加载所有企业
  const loadEnterprises = async () => {
    enterpriseLoading.value = true
    try {
      const response = await getEnterprises()
      enterprises.value = response || []
    } catch (error) {
      console.error('加载企业失败:', error)
      enterprises.value = []
    } finally {
      enterpriseLoading.value = false
    }
  }
  
  // 加载所有用户
  const loadUsers = async () => {
    userLoading.value = true
    try {
      const response = await getUsers()
      users.value = response || []
    } catch (error) {
      console.error('加载用户失败:', error)
      users.value = []
    } finally {
      userLoading.value = false
    }
  }
  
  // 加载资产（可按企业过滤）
  const loadAssets = async (enterpriseId = null) => {
    assetLoading.value = true
    try {
      const params = enterpriseId ? { enterprise_id: enterpriseId } : {}
      const response = await getAssets(params)
      assets.value = response || []
    } catch (error) {
      console.error('加载资产失败:', error)
      assets.value = []
    } finally {
      assetLoading.value = false
    }
  }
  
  // 搜索企业
  const searchEnterprises = async (query) => {
    if (!query) {
      await loadEnterprises()
      return
    }
    
    enterpriseLoading.value = true
    try {
      const response = await getEnterprises({ search: query })
      enterprises.value = response || []
    } catch (error) {
      console.error('搜索企业失败:', error)
    } finally {
      enterpriseLoading.value = false
    }
  }
  
  // 搜索用户
  const searchUsers = async (query) => {
    if (!query) {
      await loadUsers()
      return
    }
    
    userLoading.value = true
    try {
      const response = await getUsers({ search: query })
      users.value = response || []
    } catch (error) {
      console.error('搜索用户失败:', error)
    } finally {
      userLoading.value = false
    }
  }
  
  // 处理企业变化
  const handleEnterpriseChange = async (enterpriseId) => {
    selectedEnterprise.value = enterpriseId
    
    if (enterpriseId) {
      // 加载该企业的资产
      await loadAssets(enterpriseId)
      
      // 查找企业关联的用户并自动选择
      const enterprise = enterprises.value.find(ent => ent.id === enterpriseId)
      if (enterprise && enterprise.user_id) {
        selectedUser.value = enterprise.user_id
        
        // 确保用户在选项中
        if (!users.value.find(user => user.id === enterprise.user_id)) {
          await loadUsers()
        }
      }
    } else {
      // 清空企业时，清空资产选择
      selectedAsset.value = null
      assets.value = []
      
      // 重新加载所有用户
      await loadUsers()
    }
  }
  
  // 处理用户变化
  const handleUserChange = async (userId) => {
    selectedUser.value = userId
    
    if (userId) {
      // 查找该用户关联的企业
      await loadEnterprises()
      const userEnterprises = enterprises.value.filter(ent => ent.user_id === userId)
      
      if (userEnterprises.length > 0) {
        // 过滤企业列表
        enterprises.value = userEnterprises
        
        // 如果只有一个企业，自动选择
        if (userEnterprises.length === 1) {
          selectedEnterprise.value = userEnterprises[0].id
          await loadAssets(userEnterprises[0].id)
        } else {
          // 多个企业，加载所有相关资产
          const allAssets = []
          for (const enterprise of userEnterprises) {
            const assetResponse = await getAssets({ enterprise_id: enterprise.id })
            allAssets.push(...(assetResponse || []))
          }
          assets.value = allAssets
        }
      } else {
        // 用户没有关联企业，显示所有企业和资产
        await Promise.all([loadEnterprises(), loadAssets()])
      }
    } else {
      // 清空用户时，重新加载所有企业和资产
      await Promise.all([loadEnterprises(), loadAssets()])
    }
  }
  
  // 处理资产变化
  const handleAssetChange = async (assetId) => {
    selectedAsset.value = assetId
    
    if (assetId) {
      // 当选择资产时，自动带出企业信息
      const selectedAssetObj = assets.value.find(asset => asset.id === assetId)
      if (selectedAssetObj && selectedAssetObj.enterprise_id) {
        selectedEnterprise.value = selectedAssetObj.enterprise_id
        
        // 确保企业在选项中
        if (!enterprises.value.find(ent => ent.id === selectedAssetObj.enterprise_id)) {
          await loadEnterprises()
        }
        
        // 过滤出与该企业关联的用户
        const enterprise = enterprises.value.find(ent => ent.id === selectedAssetObj.enterprise_id)
        if (enterprise && enterprise.user_id) {
          selectedUser.value = enterprise.user_id
          
          // 确保用户在选项中
          if (!users.value.find(user => user.id === enterprise.user_id)) {
            await loadUsers()
          }
        }
      }
    }
  }
  
  // 初始化数据
  const initialize = async () => {
    await Promise.all([
      loadEnterprises(),
      loadUsers(),
      loadAssets()
    ])
  }
  
  // 重置所有选择
  const reset = () => {
    selectedEnterprise.value = null
    selectedUser.value = null
    selectedAsset.value = null
  }
  
  // 获取选中项的显示名称
  const getSelectedNames = () => {
    const enterprise = enterprises.value.find(ent => ent.id === selectedEnterprise.value)
    const user = users.value.find(u => u.id === selectedUser.value)
    const asset = assets.value.find(a => a.id === selectedAsset.value)
    
    return {
      enterpriseName: enterprise?.name || '',
      userName: user?.name || '',
      assetName: asset?.asset_id || ''
    }
  }
  
  return {
    // 响应式数据
    enterprises,
    users,
    assets,
    
    // 加载状态
    enterpriseLoading,
    userLoading,
    assetLoading,
    
    // 选中值
    selectedEnterprise,
    selectedUser,
    selectedAsset,
    
    // 计算属性
    isValid,
    
    // 方法
    loadEnterprises,
    loadUsers,
    loadAssets,
    searchEnterprises,
    searchUsers,
    handleEnterpriseChange,
    handleUserChange,
    handleAssetChange,
    initialize,
    reset,
    getSelectedNames
  }
}
