import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { approveOrder, rejectOrder } from '@/api/order';

/**
 * 订单审核相关的 composable
 */
export function useOrderAudit() {
  const auditing = ref(false);

  /**
   * 审核订单（通过）
   * @param {string} orderId - 订单ID
   * @param {Function} onSuccess - 成功回调
   */
  const auditOrder = async (orderId, onSuccess) => {
    try {
      await ElMessageBox.confirm(
        '确定要审核通过此订单吗？',
        '确认审核',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      auditing.value = true;
      await approveOrder(orderId);
      
      ElMessage.success('审核通过成功');
      
      // 执行成功回调
      if (onSuccess) {
        onSuccess();
      }
      
    } catch (error) {
      if (error !== 'cancel') {
        console.error('审核失败:', error);
        ElMessage.error('审核失败');
      }
    } finally {
      auditing.value = false;
    }
  };

  /**
   * 弃审订单
   * @param {string} orderId - 订单ID
   * @param {Function} onSuccess - 成功回调
   */
  const revertAudit = async (orderId, onSuccess) => {
    try {
      await ElMessageBox.confirm(
        '确定要弃审此订单吗？弃审后订单将退回到审核列表。',
        '确认弃审',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      auditing.value = true;
      // 弃审不需要填写原因，传空字符串
      await rejectOrder(orderId, '');

      ElMessage.success('弃审成功');

      // 执行成功回调
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      if (error !== 'cancel') {
        console.error('弃审失败:', error);
        ElMessage.error('弃审失败');
      }
    } finally {
      auditing.value = false;
    }
  };

  return {
    auditing,
    auditOrder,
    revertAudit
  };
}
