<view class="container">
  <!-- 页面头部组件 -->
  <page-header
    product-name="好生意"
    promotion-info="{{promotionInfo}}"
    activity-time-range="{{activityTimeRange}}" />

  <!-- 标签页选项卡 -->
  <view class="tabs">
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 0 ? 'active' : ''}}"
        data-index="0"
        bindtap="switchTab">
        套餐详情
      </view>
    </view>
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 1 ? 'active' : ''}}"
        data-index="1"
        bindtap="switchTab">
        活动规则
      </view>
    </view>
    <view class="tab-container">
      <view
        class="tab-item {{activeTab === 2 ? 'active' : ''}}"
        data-index="2"
        bindtap="switchTab">
        常见问题
      </view>
    </view>
  </view>

  <!-- 套餐详情 -->
  <view class="tab-content" wx:if="{{activeTab === 0}}">
    <!-- 好生意信息整合框 -->
    <view class="ydz-integrated-card">
      <!-- 限时促销标签 -->
      <view class="promo-tag">限时特惠</view>
      
      <view class="ydz-header">
        <view class="ydz-title">好生意</view>
        <view class="ydz-desc">
          <view>针对不同企业规模、行业、业务场景</view>
          <view>好生意提供各种版本满足您的需求</view>
        </view>
      </view>

      <!-- 产品特点 -->
      <view class="ydz-features">
        <view class="ydz-feature-item" wx:for="{{hkjFeatures}}" wx:key="index">
          <view class="feature-icon"><text class="iconfont icon-xuanzhong"></text></view>

          <text>{{item}}</text>
        </view>
      </view>

      <!-- 账套数选择 -->
      <view class="ydz-selector-section">
        <view class="ydz-selector-title">请选择账套数：</view>
        <view class="ydz-selector">
          <view class="ydz-option {{selectedPackage === '普及版' ? 'selected' : ''}}" 
                bindtap="selectPackage" data-package="普及版">
                <text>普及版</text>
                <text class="hot-tag">HOT</text>
          </view>

          <view class="ydz-option {{selectedPackage === '标准版' ? 'selected' : ''}}" 
                bindtap="selectPackage" data-package="标准版">标准版</view>

          
          <view class="ydz-option {{selectedPackage === '精益版' ? 'selected' : ''}}" 
                bindtap="selectPackage" data-package="精益版">精益版</view>
          
        </view>
      </view>

      <!-- 时长选择 -->
      <view class="ydz-selector-section">
        <view class="ydz-selector-title">请选择时长：</view>
        <view class="ydz-duration-selector">
          <view wx:for="{{promotionInfo.rules}}" wx:key="index" 
                class="ydz-duration-option {{selectedDuration === item.period ? 'selected' : ''}}" 
                bindtap="selectDuration" data-duration="{{item.period}}">
            <text>{{item.period}}年</text>
            <text wx:if="{{item.period === 3}}" class="hot-tag">HOT</text>
            <text class="discount-tag">{{item.discountText}}折</text>
          </view>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="ydz-price-section">
        <view class="ydz-price-row">
          <text class="price-label">原价:</text>
          <text class="original-price">¥{{originalPrice}}</text>
        </view>
        <view class="ydz-price-row">
          <text class="price-label">特惠价:</text>
          <text class="discount-price">{{discountPrice}}</text>
          <view class="discount-badge">
            <text wx:if="{{selectedDiscount}}">{{selectedDuration}}年{{selectedDiscountText}}折</text>
          </view>
        </view>
        <view class="ydz-price-row">
          <text class="price-label">每天低至:</text>
          <text class="daily-price">{{dailyPrice}}</text>
        </view>
        <!-- 价格注释信息 -->
        <view class="ydz-price-note-container">
          <view class="ydz-price-note">
            <text class="note-icon">*</text>
            <text>可增购 账套数量 和 用户数量</text>
          </view>
        </view>
      </view>

      <!-- 赠送礼品 -->
      <view class="ydz-gift-section">
        <view class="ydz-gift-title">
          <text class="gift-icon">🎁</text>
          <text>满额赠送</text>
        </view>
        <view class="ydz-gift-info">
          <view class="ydz-gift-product" wx:if="{{giftProduct}}">{{giftProduct}}</view>
          <view wx:else class="no-gift">暂未满足赠品条件</view>
        </view>
        
        <!-- 满减指示器 -->
        <view class="gift-progress">
          <view class="gift-progress-bar">
            <view class="progress-inner" style="width: {{progressPercent}}"></view>
          </view>
          <view class="gift-progress-marks">
            <text wx:for="{{promotionInfo.gifts}}" wx:key="index" 
                  class="{{discountPrice >= item.threshold ? 'reached' : ''}}">满{{item.threshold}}</text>
          </view>
        </view>
      </view>

      <!-- 立即购买按钮 -->
      <view class="buttons-group">
        <view class="detail-button" bindtap="showProductDetail">产品详情</view>
        <button class="ydz-buy-button" open-type="contact">
          购买咨询
          <view class="button-shine"></view>
        </button>
      </view>
      
      <!-- 活动说明 -->
      <view class="activity-notes">
        <text>活动时间：{{activityTimeRange}}</text>
        <text>*活动解释权归贝克信息所有</text>
      </view>
    </view>
  </view>

  <!-- 活动规则组件 -->
  <activity-rules
    wx:if="{{activeTab === 1}}"
    promotion-info="{{promotionInfo}}"
    activity-time-range="{{activityTimeRange}}" />

  <!-- 常见问题组件 -->
  <common-faq wx:if="{{activeTab === 2}}" />
</view>