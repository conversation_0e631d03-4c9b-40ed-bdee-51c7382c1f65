import service from '@/utils/request_extra.js';

const API_PATH = '/users';

/**
 * [新增] 获取下一个可用的用户ID
 */
export const getNextUserId = () => {
  return service.get(`${API_PATH}/next-user-id`);
};

/**
 * [新增] 获取下一个可用的合伙人ID
 */
export const getNextPartnerId = () => {
  return service.get(`${API_PATH}/next-partner-id`);
};

/**
 * 获取所有用户列表 (支持模糊搜索)
 * @param {object} params - 查询参数, 例如 { q: 'keyword' }
 */
export const getUsers = (params) => {
  return service.get(API_PATH, { params });
};

/**
 * 根据ID获取单个用户的详细信息
 */
export const getUserById = (id) => {
  return service.get(`${API_PATH}/${id}`);
};

/**
 * 创建一个新用户
 * @param {object} userData - 新用户的数据
 */
export const createUser = (userData) => {
  return service.post(API_PATH, userData);
};

/**
 * 更新一个用户
 * @param {string} id - 用户ID
 * @param {object} userData - 要更新的用户信息
 */
export const updateUser = (id, userData) => {
  return service.put(`${API_PATH}/${id}`, userData);
};

/**
 * 删除一个用户
 * @param {string} id - 用户ID
 */
export const deleteUser = (id) => {
  return service.delete(`${API_PATH}/${id}`);
}; 