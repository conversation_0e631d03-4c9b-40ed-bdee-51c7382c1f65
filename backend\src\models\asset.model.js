// /backend/src/models/asset.model.js
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 Asset (资产) 模型/表
 * [重要] 此模型定义已根据最新的 customer_management.sql 文件进行了完全同步。
 */
const Asset = sequelize.define(
  'Asset',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '自增主键',
    },
    asset_id: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: '资产业务ID',
    },
    enterprise_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联enterprise.id',
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '关联user.id',
    },
    status: {
      type: DataTypes.ENUM('过期', '在线'),
      allowNull: false,
      defaultValue: '在线',
      comment: '资产状态',
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联product.id',
    },
    user_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '使用人数',
    },
    account_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '账套数',
    },
    duration_months: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '购买时长（月）',
    },
    selected_features: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '功能ID列表',
    },
    purchase_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: '购买日期',
    },
    product_expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: '产品到期日',
    },
    sps_expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'SPS到期日（原service_expiry_date）',
    },
    after_sales_expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: '售后服务到期日',
    },
    product_standard_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '产品标准价',
    },
    sps_annual_fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'SPS年费（即应用服务费）',
    },
    after_sales_service_fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '售后服务费用',
    },
    implementation_fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '实施费用',
    },
    activation_code: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '激活码',
    },
    activation_phone: {
      type: DataTypes.STRING(15),
      allowNull: true,
      comment: '激活手机号',
    },
    activation_password: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '激活密码（明文存储）',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注',
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '制单人（关联employee.id）',
    },
  },
  {
    tableName: 'asset',
    timestamps: true, // 自动管理 createdAt 和 updatedAt 字段
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
  }
);

module.exports = Asset; 