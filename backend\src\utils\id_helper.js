const { Op } = require('sequelize');
const db = require('../models'); // 引入所有模型，方便调用

/**
 * 生成一个指定长度的、由纯数字组成的随机字符串。
 * @param {number} length - 要生成的字符串的长度。
 * @returns {string} - 生成的随机数字字符串。
 */
function generateRandomDigits(length) {
    const chars = '0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 生成一个指定长度的、由大写字母和数字组成的随机字符串。
 * @param {number} length - 要生成的字符串的长度。
 * @returns {string} - 生成的随机字符串。
 */
function generateRandomUppercaseString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * [重构] 核心函数：生成一个唯一的、按顺序递增的ID。
 * @param {object} model - Sequelize模型，例如 db.Enterprise。
 * @param {string} field - 要检查唯一性的字段名，例如 'enterprise_id'。
 * @param {string} [prefix=''] - ID的前缀，例如 'P' 或 'SW'。
 * @param {number} [length=6] - 数字部分的长度，不足会自动补零。
 * @returns {Promise<string>} 一个唯一的、顺序的ID，例如 '000001' 或 'P000001'。
 */
async function generateSequentialId(model, field, prefix = '', length = 6) {
    const whereCondition = {
        [field]: {
            // 对于有前缀的，用LIKE；对于纯数字的，用REGEXP确保只匹配纯数字，过滤掉脏数据
            [prefix ? Op.like : Op.regexp]: prefix ? `${prefix}%` : '^[0-9]+$'
        }
    };

    // 为了解决 '9' > '10' 的字符串排序问题，我们按ID的长度和值双重排序
    const orderCondition = [
        [model.sequelize.fn('LENGTH', model.sequelize.col(field)), 'DESC'],
        [field, 'DESC']
    ];

    const latestRecord = await model.findOne({
        where: whereCondition,
        order: orderCondition,
        attributes: [field],
        raw: true
    });

    let newSequence = 1;
    if (latestRecord) {
        // 从字段值中提取数字部分
        const numericPart = latestRecord[field].replace(prefix, '');
        if (numericPart && !isNaN(parseInt(numericPart, 10))) {
            newSequence = parseInt(numericPart, 10) + 1;
        }
    }

    const paddedSequence = newSequence.toString().padStart(length, '0');
    return `${prefix}${paddedSequence}`;
}

/**
 * 生成一个唯一的8位纯数字随机用户ID。
 * @returns {Promise<string>} 一个唯一的用户ID。
 */
async function generateUserId() {
    let userId;
    let userExists = true;
    do {
        userId = generateRandomDigits(8);
        const existingUser = await db.User.findOne({ where: { user_id: userId } });
        if (!existingUser) {
            userExists = false;
        }
    } while (userExists);
    return userId;
}

/**
 * 生成一个唯一的合伙人ID (PN + 6位随机数字)。
 * @returns {Promise<string>} 一个唯一的合伙人ID。
 */
async function generatePartnerId() {
    let partnerId;
    let partnerExists = true;
    do {
        partnerId = `PN${generateRandomDigits(6)}`;
        const existingPartner = await db.User.findOne({ where: { partner_id: partnerId } });
        if (!existingPartner) {
            partnerExists = false;
        }
    } while (partnerExists);
    return partnerId;
}

/**
 * [重构] 生成一个唯一的、从'000001'开始的有序企业ID。
 * @returns {Promise<string>} 一个唯一的企业ID。
 */
async function generateEnterpriseId() {
    return generateSequentialId(db.Enterprise, 'enterprise_id', '', 6);
}

/**
 * [新增] 生成一个唯一的员工工号 (6位顺序数字)。
 * @returns {Promise<string>} 一个唯一的员工工号。
 */
async function generateEmployeeNumber() {
    return generateSequentialId(db.Employee, 'employee_number', '', 6);
}

/**
 * [新增] 生成一个唯一的产品ID (6位顺序数字)。
 * @returns {Promise<string>} 一个唯一的产品ID。
 */
async function generateProductId() {
    return generateSequentialId(db.Product, 'product_id', '', 6);
}

/**
 * [新增] 生成一个唯一的产品功能ID (6位顺序数字)。
 * @returns {Promise<string>} 一个唯一的产品功能ID。
 */
async function generateFeatureId() {
    return generateSequentialId(db.ProductFeature, 'feature_id', '', 6);
}

/**
 * [重构] 生成一个唯一的、从'000001'开始的有序资产ID。
 * @returns {Promise<string>} 一个唯一的资产ID。
 */
async function generateAssetId() {
    return generateSequentialId(db.Asset, 'asset_id', '', 6);
}

/**
 * [新增] 生成一个唯一的、从'000001'开始的有序资产变更ID。
 * @returns {Promise<string>} 一个唯一的资产变更ID。
 */
async function generateAssetChangeId() {
    return generateSequentialId(db.AssetChangeLog, 'asset_change_id', '', 6);
}

/**
 * 生成一个唯一的订单ID
 * @param {string} [prefix='PO'] - 订单前缀：'PO'=产品订单，'SO'=服务订单
 * @returns {Promise<string>} 一个唯一的订单ID，格式：PO + YYYYMMDD + 3位随机码 或 SO + YYYYMMDD + 3位随机码
 */
async function generateOrderId(prefix = 'PO') {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const datePrefix = `${prefix}${year}${month}${day}`;

    let orderId;
    let orderExists = true;
    do {
        orderId = `${datePrefix}${generateRandomUppercaseString(3)}`;
        // 使用新的 OrderHead 模型检查唯一性
        const existingOrder = await db.OrderHead.findOne({ where: { order_id: orderId } });
        if (!existingOrder) {
            orderExists = false;
        }
    } while (orderExists);
    return orderId;
}

module.exports = {
    generateUserId,
    generatePartnerId,
    generateEnterpriseId,
    generateEmployeeNumber,
    generateProductId,
    generateFeatureId,
    generateOrderId,
    generateAssetId,
    generateAssetChangeId
}; 