<view class="float-consult">
  <!-- 毛玻璃遮罩层 -->
  <view class="backdrop {{isExpanded ? 'active' : ''}}" bindtap="onBackdropClick"></view>
  
  <!-- 菜单项容器 -->
  <view class="menu-container {{isExpanded ? 'expanded' : ''}}" style="{{menuStyle}}">
    <!-- 菜单项 -->
    <view class="menu-items">
      <button 
        class="menu-item {{isExpanded ? 'visible' : ''}} {{activeMenuId === 1 ? 'active' : ''}}"
        data-id="1"
        open-type="contact"
        plain="true"
      >
        <view class="menu-label {{activeMenuId === 1 ? 'visible' : ''}}">
          <text class="menu-text">微信咨询</text>
        </view>
        <view class="menu-icon" style="background: #07C160;">
          <text class="iconfont icon-weixin"></text>
        </view>
      </button>
      
      <view 
        class="menu-item {{isExpanded ? 'visible' : ''}} {{activeMenuId === 2 ? 'active' : ''}}"
        data-action="makePhoneCall"
        data-id="2"
        bindtap="handleAction"
      >
        <view class="menu-label {{activeMenuId === 2 ? 'visible' : ''}}">
          <text class="menu-text">电话咨询</text>
        </view>
        <view class="menu-icon" style="background: #3B94FF;">
          <text class="iconfont icon-dianhua"></text>
        </view>
      </view>
      
      <view 
        class="menu-item {{isExpanded ? 'visible' : ''}} {{activeMenuId === 3 ? 'active' : ''}}"
        data-action="waitCallback"
        data-id="3"
        bindtap="handleAction"
      >
        <view class="menu-label {{activeMenuId === 3 ? 'visible' : ''}}">
          <text class="menu-text">等待回电</text>
        </view>
        <view class="menu-icon" style="background: #FF6A00;">
          <text class="iconfont icon-icon_pingyutianxie"></text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 主按钮 -->
  <view 
    class="main-button {{isExpanded ? 'active' : ''}}" 
    style="{{buttonStyle}}"
    bindtap="toggleMenu"
  >
    <view class="icon-container">
      <text class="iconfont {{isExpanded ? 'icon-guanbi1' : 'icon-shouhoufuwu2'}}"></text>
    </view>
  </view>
  
  <!-- 底部弹出的回电表单 -->
  <view class="callback-form-container {{showCallbackForm ? 'show' : ''}}">
    <view class="callback-form-mask" bindtap="closeCallbackForm"></view>
    <view class="callback-form">
      <view class="callback-form-header">
        <text class="callback-form-title">留下联系方式</text>
        <view class="callback-form-close" bindtap="closeCallbackForm">
          <text class="iconfont icon-guanbi1"></text>
        </view>
      </view>
      <view class="callback-form-content">
        <view class="form-item">
          <text class="form-label">称呼</text>
          <input class="form-input" type="text" placeholder="请输入您的称呼" bindinput="onNameInput" value="{{name}}" />
        </view>
        <view class="form-item">
          <text class="form-label"><text class="required">*</text> 手机号码</text>
          <input class="form-input" type="number" placeholder="请输入您的手机号码" bindinput="onPhoneInput" value="{{phoneNumber}}" maxlength="11" />
        </view>
        <view class="form-item">
          <text class="form-label">备注信息</text>
          <textarea class="form-textarea" placeholder="请输入您的留言内容" bindinput="onRemarkInput" value="{{remark}}" maxlength="100"></textarea>
          <view class="textarea-counter">{{remark.length || 0}}/100</view>
        </view>
      </view>
      <view class="callback-form-footer">
        <button class="submit-btn" bindtap="submitCallbackForm">提 交</button>
        <view class="form-tips">提交后我们将尽快与您联系</view>
      </view>
    </view>
  </view>
</view> 