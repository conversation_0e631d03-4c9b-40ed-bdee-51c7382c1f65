<template>
  <div class="asset-activation-info">
    <!-- 激活信息组件 - 处理激活码、激活手机号、激活密码 -->
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px" :disabled="readonly">
      
      <div class="form-section">
        <h4 class="section-title">激活信息</h4>
        <div class="activation-grid">
          <!-- 激活码 -->
          <el-form-item label="激活码" prop="activation_code" class="field-activation-code">
            <el-input
              v-model="formData.activation_code"
              placeholder="请输入激活码"
              :readonly="readonly"
              show-password
              clearable
            />
          </el-form-item>

          <!-- 激活手机号 -->
          <el-form-item label="激活手机号" prop="activation_phone" class="field-activation-phone">
            <el-input
              v-model="formData.activation_phone"
              placeholder="请输入激活手机号"
              :readonly="readonly"
              clearable
              maxlength="11"
              show-word-limit
            />
          </el-form-item>

          <!-- 激活密码 -->
          <el-form-item label="激活密码" prop="activation_password" class="field-activation-password">
            <el-input
              v-model="formData.activation_password"
              placeholder="请输入激活密码"
              :readonly="readonly"
              show-password
              clearable
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Props定义
const props = defineProps({
  // 表单数据对象
  formData: {
    type: Object,
    required: true
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits(['data-change'])

// 表单引用
const formRef = ref(null)

// 表单验证规则
const formRules = {
  activation_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
}

// 表单验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单验证
const resetValidation = () => {
  formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetValidation
})
</script>

<style scoped>
.asset-activation-info {
  padding: 20px;
}

.form-section {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 激活信息Grid布局 */
.activation-grid {
  display: grid;
  gap: 20px 24px; /* 增加间距避免挤压 */
  grid-template-columns: 1fr 1fr 1fr; /* 3列布局 */
  align-items: start;
}

.el-form-item {
  margin-bottom: 0; /* Grid已处理间距 */
  overflow: hidden; /* 防止内容溢出 */
}

.el-form-item__content {
  overflow: hidden; /* 防止内容溢出 */
}

/* 输入框宽度优化 */
.activation-grid .el-input {
  width: 100%;
  max-width: 100%; /* 防止超出容器 */
}

/* 只读模式样式调整 */
.el-form--disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .activation-grid {
    grid-template-columns: 1fr 1fr; /* 中等屏幕使用2列 */
    gap: 16px 20px;
  }
}

@media (max-width: 768px) {
  .activation-grid {
    grid-template-columns: 1fr; /* 小屏幕使用单列 */
    gap: 16px;
  }

  .asset-activation-info {
    padding: 16px;
  }

  .form-section {
    padding: 16px;
  }
}
</style>
