/**
 * 页面头部组件样式
 */
/* 引入iconfont图标库 */
@import '/static/fonts/iconfont.wxss';

/* 全局变量 */
:host {
  --primary-color: #FF3333;
  --secondary-color: #FF8C00;
  --light-color: #FFFFFF;
  --bg-color: #F6F6F6;
  --text-color: #1a1a1a;
  --text-secondary: #383838;
  --border-radius: 12rpx;
}

/* 返回按钮样式 */
.back-button {
  position: fixed;
  top: 90rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #FF3333;
  border-left: 4rpx solid #FF3333;
  transform: rotate(-45deg);
  margin-left: 8rpx;
}

/* 添加发光效果 */
.back-button::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.back-button:active::after {
  opacity: 1;
}

/* 顶部活动信息 */
.promotion-header {
  width: 100%;
  padding: 190rpx 0 40rpx;
  border-radius: 0 0 30rpx 30rpx;
  margin: 0 0 20rpx 0;
  background: linear-gradient(135deg, #FF3333, #FF6A6A);
  color: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(255, 51, 51, 0.3);
}

/* 内容区域 */
.promotion-content {
  padding: 0 30rpx;
  position: relative;
  z-index: 2;
}

/* 装饰元素 */
.promotion-header::before,
.promotion-header::after {
  content: '';
  position: absolute;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.promotion-header::before {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
}

.promotion-header::after {
  width: 150rpx;
  height: 150rpx;
  bottom: -70rpx;
  left: 30rpx;
}

.promotion-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
}

.promotion-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
}

.activity-time {
  font-size: 28rpx;
  margin-bottom: 16rpx;
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(0, 0, 0, 0.1);
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.countdown-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16rpx;
  border-radius: 16rpx;
  margin-top: 10rpx;
}

.countdown-box .countdown-text {
  font-size: 30rpx;
}

.countdown-time {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.countdown-unit {
  background-color: var(--light-color);
  color: var(--primary-color);
  padding: 8rpx 18rpx;
  border-radius: 8rpx;
  margin: 0 8rpx;
  font-weight: bold;
  min-width: 60rpx;
  text-align: center;
  font-size: 34rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.countdown-unit::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
}

/* 618装饰元素 */
.promo-decoration {
  position: absolute;
  bottom: -15rpx;
  right: 20rpx;
  font-size: 120rpx;
  font-weight: 900;
  opacity: 0.1;
  color: #fff;
  transform: rotate(-5deg);
  z-index: 1;
  font-style: italic;
}
