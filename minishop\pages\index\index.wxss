/* 618活动页面样式 */
/* 引入iconfont图标库 */
@import '/static/fonts/iconfont.wxss';

page {
  background: linear-gradient(135deg, #FF2B22, #FF6347);
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: 100%;
  --primary-color: #FF2B22;
  --secondary-color: #FF5A00;
  --highlight-color: #FF0000;
  --gold-color: #FFD700;
  --text-color: #333333;
  --light-text-color: #666666;
  --card-bg-color: #FFFFFF;
  --border-radius: 12rpx;
  --shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  color: #fff;
}

.activity-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #FF3333 0%, #FF5555 50%, #FF7777 100%);
  position: relative;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  overflow: hidden;
}


/* 顶部图片区域 - 新样式 */
.banner-image-container {
  width: 100%;
  height: 850rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}

.banner-full-image {
  width: 750rpx;
  height: 850rpx;
}

/* 服务 - 立体样式 */
.service-buttons-container {
  display: flex;
  justify-content: space-between;
  margin: 30rpx;
  margin-top: -70rpx;
  background: linear-gradient(180deg, #ffffff, #fff0f0);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 15rpx 30rpx rgba(255, 0, 0, 0.25);
  position: relative;
  z-index: 20;
  border: 2rpx solid rgba(255, 51, 51, 0.3);
  overflow: hidden;
  animation: container-glow 2s infinite alternate;
}

@keyframes container-glow {
  0% { box-shadow: 0 15rpx 30rpx rgba(255, 0, 0, 0.25); }
  100% { box-shadow: 0 15rpx 40rpx rgba(255, 0, 0, 0.4); }
}

.service-buttons-container::before {
  content: '';
  position: absolute;
  top: -80rpx;
  right: -80rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 51, 51, 0.1) 0%, rgba(255, 51, 51, 0) 70%);
  border-radius: 50%;
  z-index: 1;
}

.service-buttons-container::after {
  content: '爆款';
  position: absolute;
  bottom: -10rpx;
  right: 20rpx;
  font-size: 100rpx;
  font-weight: bold;
  color: rgba(255, 51, 51, 0.06);
  z-index: 1;
  transform: rotate(-5deg);
}

.service-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
  padding: 20rpx 0;
  position: relative;
  transition: all 0.3s ease;
  z-index: 2;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16rpx;
  margin: 0 8rpx;
  box-shadow: 0 6rpx 12rpx rgba(255, 0, 0, 0.1);
}

.service-btn:active {
  transform: scale(0.95);
}

.service-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FF3333, #FF6666);
  border-radius: 6rpx 6rpx 0 0;
}

.service-btn .iconfont {
  font-size: 70rpx;
  color: #FF3333;
  margin-bottom: 15rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(255, 0, 0, 0.4));
  transform: perspective(800rpx) rotateY(0deg);
  transition: all 0.5s ease;
  animation: float 3s infinite ease-in-out;
  font-weight: 500;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

.service-btn:hover .iconfont {
  transform: perspective(800rpx) rotateY(15deg);
}

.service-btn text {
  font-size: 28rpx;
  color: #FF3333;
  font-weight: bold;
  white-space: nowrap;
  position: relative;
  text-shadow: 0 1rpx 2rpx rgba(255, 0, 0, 0.1);
}

.service-btn text::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -6rpx;
  width: 0;
  height: 2rpx;
  background: #FF3333;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.service-btn:hover text::after {
  width: 80%;
}

.service-badge {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  background: linear-gradient(135deg, #FF3333, #FF6666);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  transform: rotate(5deg);
  animation: pulse-badge 2s infinite;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  z-index: 3;
}

@keyframes pulse-badge {
  0% { transform: rotate(5deg) scale(1); }
  50% { transform: rotate(5deg) scale(1.1); }
  100% { transform: rotate(5deg) scale(1); }
}

/* 活动礼品区域 - 新样式 */
.gifts-section {
  position: relative;
  margin: 30rpx;
  background: linear-gradient(135deg, #FFFFFF, #FFF0F0);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 15rpx 30rpx rgba(255, 0, 0, 0.25);
  border: none;
  overflow: hidden;
}

.gifts-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ff0000' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E"); */
  z-index: 0;
}

.gifts-section::after {
  content: '限时赠礼';
  position: absolute;
  top: 12rpx;
  right: -45rpx;
  background: linear-gradient(90deg, #FF3333, #FF6666);
  color: white;
  font-size: 22rpx;
  font-weight: bold;
  padding: 10rpx 60rpx;
  transform: rotate(45deg);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF0000;
  margin-bottom: 40rpx;
  text-align: left;
  position: relative;
  display: inline-block;
  padding-left: 20rpx;
  border-left: 8rpx solid #FF3333;
  text-shadow: 0 2rpx 4rpx rgba(255, 0, 0, 0.2);
  z-index: 2;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 20rpx;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FF3333, transparent);
  border-radius: 3rpx;
}

/* 顶部大卡片 */
.gift-large-card {
  background: linear-gradient(to bottom, #ffffff, #fff5f5);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 10rpx 20rpx rgba(255, 51, 51, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 102, 102, 0.3);
  max-height: 300rpx;
}

.gift-large-card:hover {
  transform: translateY(-5rpx);
  box-shadow: 0 20rpx 40rpx rgba(255, 51, 51, 0.3);
}

.gift-large-card::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 235, 235, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
  opacity: 0.6;
}

.gift-condition-tag {
  position: absolute;
  top: 12rpx;
  left: 15rpx;
  background: linear-gradient(90deg, #ff3333, #ff6666);
  color: white;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  font-weight: bold;
  box-shadow: 0 5rpx 10rpx rgba(255, 0, 0, 0.2);
  z-index: 2;
}

.gift-condition-tag .highlight {
  font-size: 30rpx;
  font-weight: bold;
  margin: 0 4rpx;
  color: #ffff00;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.large-card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  margin-top: 25rpx;
  padding: 0 10rpx;
}

.gift-image-container {
  flex: 0 0 auto;
  margin-right: 20rpx;
  margin-bottom: 0;
  margin-top: 20rpx;
}

.gift-image-large {
  width: 180rpx;
  height: 180rpx;
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.2));
  
}


.gift-info {
  flex: 1;
  align-items: flex-start;
}

.gift-text-large {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  text-align: left;
  margin-bottom: 10rpx;
  position: relative;
  padding: 0;
}

.gift-text-large::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 30%;
  right: 30%;
  height: 3rpx;
  background: linear-gradient(90deg, transparent, #ff6666, transparent);
}

/* 小卡片新样式 */
.gift-row-cards {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.gift-small-card {
  flex: 1;
  background: linear-gradient(to bottom, #ffffff, #fff8f8);
  border-radius: 20rpx;
  padding: 20rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 10rpx 20rpx rgba(255, 51, 51, 0.15);
  border: 1px solid rgba(255, 102, 102, 0.2);
}

.gift-small-card:hover {
  transform: translateY(-5rpx) scale(1.02);
  box-shadow: 0 15rpx 30rpx rgba(255, 51, 51, 0.25);
}

.gift-small-card .gift-condition-tag {
  top: 12rpx;
  left: 12rpx;
  font-size: 20rpx;
  padding: 4rpx 14rpx;
  z-index: 4;
}

.gift-image-medium {
  width: 180rpx;
  height: 180rpx;
  filter: drop-shadow(0 6rpx 12rpx rgba(0, 0, 0, 0.2));
  margin: 30rpx 0 0rpx;
}

.gift-text-medium {
  font-size: 26rpx;
  color: #333333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10rpx;
}

.gift-note {
  font-size: 22rpx;
  color: white;
  background: linear-gradient(90deg, #ff9900, #ff6600);
  padding: 4rpx 15rpx;
  border-radius: 15rpx;
  display: inline-block;
  font-weight: bold;
  margin-top: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.send-tag {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff3333, #ff6666);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 12rpx rgba(255, 0, 0, 0.3);
  animation: pulse-tag 2s infinite alternate;
  z-index: 3;
  border: 2rpx dashed rgba(255, 255, 255, 0.8);
}

@keyframes pulse-tag {
  0% { transform: scale(1); }
  100% { transform: scale(1.1) rotate(10deg); }
}



/* 送标签 - 小卡片 */
.send-tag-small {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 45rpx;
  height: 45rpx;
  background: linear-gradient(135deg, #ff3333, #ff6666);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(255, 0, 0, 0.3);
  animation: pulse-tag 2s infinite alternate;
  z-index: 3;
  border: 1rpx dashed rgba(255, 255, 255, 0.8);
}

/* 倒计时区域 */
.countdown-section {
  margin: 30rpx;
  background: linear-gradient(135deg, #FF0000, #FF3333);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
}

.countdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.countdown-title {
  font-size: 34rpx;
  font-weight: bold;
}

.countdown-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.countdown-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15rpx;
}

.countdown-label {
  font-size: 30rpx;
  margin-right: 25rpx;
  font-weight: 500;
}

.countdown-blocks {
  display: flex;
  align-items: center;
}

.countdown-block {
  background: white;
  border-radius: 10rpx;
  width: 75rpx;
  height: 75rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.countdown-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF3333;
  line-height: 1;
}

.countdown-unit {
  font-size: 22rpx;
  color: #666;
}

.countdown-separator {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin: 0 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 爆款推荐标题 */
.section-header {
  margin: 50rpx 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.section-header-title {
  font-size: 38rpx;
  font-weight: bold;
  color: white;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.section-header-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 20%;
  right: 20%;
  height: 6rpx;
  background: #FFFF00;
  border-radius: 3rpx;
}

.header-tag {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(90deg, #FFCC00, #FF9900);
  color: #800000;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 爆款推荐区域 */
.hot-products-section {
  padding: 0 30rpx;
}

.hot-label {
  display: inline-block;
  font-size: 22rpx;
  background: linear-gradient(90deg, #FF3333, #FF6666);
  color: white;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 15rpx;
  vertical-align: middle;
}

.product-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.product-header {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  margin-top: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.product-content {
  display: flex;
}

.product-info {
  flex: 2;
}

.product-name {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

/* 价格容器 */
.price-container {
  display: flex;
  flex-direction: column;
  margin: 20rpx 0;
}

/* 折扣标签 */
.discount-tag {
  background-color: #ff3636;
  color: #fff;
  font-weight: bold;
  font-size: 30rpx;
  padding: 6rpx 14rpx;
  border-radius: 30rpx;
  display: inline-block;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 0, 0, 0.3);
  transform: scale(1.05);
  white-space: nowrap;
  text-align: center;
  width: fit-content;
  min-width: auto;
  max-width: 150rpx;
  line-height: 1.2;
}

/* 折扣价 */
.discount-price {
  display: flex;
  flex-direction: column;
  margin-bottom: 8rpx;
}

.discount-price text:first-child {
  font-size: 24rpx;
  color: #ff3636;
  margin-bottom: 4rpx;
}

.discount-price .price.highlight {
  color: #ff3636;
  font-size: 36rpx;
  font-weight: bold;
}

/* 原价 */
.original-price {
  display: flex;
  flex-direction: column;
  margin-top: 8rpx;
}

.original-price text:first-child {
  font-size: 24rpx;
  color: #ff6b49;
  margin-bottom: 2rpx;
}

.original-price .price {
  color: #ff9149;
  font-size: 26rpx;
  font-weight: bold;
}

.price {
  font-size: 26rpx;
  color: #333;
}

.price.highlight {
  color: #FF3333;
  font-weight: bold;
  font-size: 32rpx;
}

.buy-button ,.detail-button {
  background: linear-gradient(90deg, #FFCC00, #FF9900);
  color: #3b3b3b;
  font-size: 30rpx;
  text-align: center;
  padding: 12rpx 0rpx;
  border-radius: 30rpx;
  width: 60%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 15rpx;
}

/* 针对button的额外样式重置 */
button.buy-button {
  line-height: normal !important;
  border: none !important;
  font-weight: normal !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding: 12rpx 0rpx !important;
  min-height: 0 !important;
  display: inline-block !important;
  width: 60% !important;
  box-sizing: border-box !important;
  height: auto !important;
}

button.buy-button::after {
  border: none !important;
  outline: none !important;
}

.detail-button {
  background: linear-gradient(90deg, #FF3333, #FF6666);
  color: white;
}

.product-image {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
}

.product-image image {
  width: 310rpx;
  height: 210rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.discount-circle {
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  background: #FF3333;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: rotate(15deg);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.discount-circle text {
  color: white;
  font-size: 20rpx;
  line-height: 1.2;
}


/* 底部联系区域 */
.contact-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20rpx 30rpx 40rpx;
}

.contact-btn {
  background: linear-gradient(90deg, #FFCC00, #FF9900);
  color: #800000;
  font-size: 32rpx;
  font-weight: bold;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  border: none;
  width: 80%;
}

.copyright {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 删除分享按钮样式 */
.share-btn {
  display: none; /* 隐藏分享按钮 */
}

.share-icon {
  display: none;
}

/* 折扣优惠区域 */
.discount-info-section {
  margin: 30rpx;
  background: linear-gradient(135deg, #FF6B00, #FF3E00);
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.25);
  position: relative;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
}

.discount-info-section::before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
}

.discount-info-section::after {
  content: '';
  position: absolute;
  bottom: -80rpx;
  left: -80rpx;
  width: 250rpx;
  height: 250rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
}

.discount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2;
}

.discount-title {
  font-size: 34rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  position: relative;
  padding-left: 20rpx;
}

.discount-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: #FFFF00;
  border-radius: 4rpx;
}

.premium-tag {
  background: rgba(255, 255, 255, 0.9);
  color: #FF3333;
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
}

.premium-tag::after {
  content: '';
  position: absolute;
  width: 120%;
  height: 120%;
  border: 2rpx dashed rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  top: -10%;
  left: -10%;
  animation: rotate 15s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.discount-cards {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 30rpx;
  position: relative;
  z-index: 2;
}

.discount-card {
  width: 31%;
  height: 180rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  overflow: hidden;
  justify-content: center;
}

.discount-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #FFCC00, #FF3333);
}

.discount-card:nth-child(2)::before {
  background: linear-gradient(90deg, #FF6600, #FF0066);
}

.discount-card:nth-child(3)::before {
  background: linear-gradient(90deg, #FF0033, #990000);
}

.discount-card:hover {
  transform: translateY(-5rpx) scale(1.02);
  box-shadow: 0 12rpx 20rpx rgba(0, 0, 0, 0.2);
}

.discount-period {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  position: relative;
}

.discount-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.discount-number {
  font-size: 52rpx;
  font-weight: bold;
  color: #FF3333;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(255, 0, 0, 0.2);
}

.discount-unit {
  font-size: 28rpx;
  color: #FF3333;
  margin-left: 4rpx;
}

.discount-save {
  font-size: 24rpx;
  color: white;
  background: #FF6600;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.best-value-tag {
  position: absolute;
  top: -8rpx;
  right: -32rpx;
  background: #FF0000;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 20rpx 30rpx 6rpx 30rpx;
  transform: rotate(45deg);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.discount-note {
  text-align: center;
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 20rpx;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
  background: rgba(0, 0, 0, 0.15);
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

/* 礼品标签 */
.gift-tag {
  background: linear-gradient(90deg, #FF9900, #FF6600);
  color: white;
  font-size: 20rpx;
  display: inline-block;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  margin-top: 5rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 促销装饰元素 */
.promotion-decor {
  position: absolute;
  bottom: -25rpx;
  right: 20rpx;
  font-size: 120rpx;
  font-weight: bold;
  color: rgba(255, 51, 51, 0.08);
  z-index: 1;
  transform: rotate(-5deg);
  letter-spacing: -5rpx;
  text-shadow: 3rpx 3rpx 0 rgba(255, 51, 51, 0.1);
}

