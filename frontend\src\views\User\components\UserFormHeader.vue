<template>
  <el-card shadow="never" class="header-card">
    <template #header>
      <span class="card-header">用户基本信息</span>
    </template>

    <el-form 
      :model="formData" 
      label-width="120px" 
      :disabled="readonly"
      ref="formRef"
      :rules="rules"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="用户ID" prop="user_id">
            <el-input 
              v-model="formData.user_id" 
              placeholder="请输入用户ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="name">
            <el-input 
              v-model="formData.name" 
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="昵称" prop="nickname">
            <el-input 
              v-model="formData.nickname" 
              placeholder="请输入昵称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="手机号" prop="mobile">
            <el-input 
              v-model="formData.mobile" 
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="邮箱" prop="email">
            <el-input 
              v-model="formData.email" 
              placeholder="请输入邮箱"
              type="email"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否合伙人" prop="is_partner">
            <el-switch 
              v-model="formData.is_partner"
              @change="handlePartnerChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 合伙人信息 -->
      <div v-if="formData.is_partner" class="partner-section">
        <el-divider content-position="left">合伙人信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="合伙人ID" prop="partner_id">
              <el-input
                v-model="formData.partner_id"
                placeholder="请输入合伙人ID"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 空列，保持布局 -->
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="基础佣金" prop="commission_base">
              <el-input-number
                v-model="formData.commission_base"
                :precision="2"
                :step="0.01"
                :min="0"
                placeholder="请输入基础佣金"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额外佣金" prop="commission_extra">
              <el-input-number
                v-model="formData.commission_extra"
                :precision="2"
                :step="0.01"
                :min="0"
                placeholder="请输入额外佣金"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 密码修改（仅编辑模式显示） -->
      <div v-if="!readonly" class="password-section">
        <el-divider content-position="left">密码设置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input 
                v-model="formData.password" 
                type="password"
                :placeholder="isEditMode ? '留空则不修改密码' : '请输入密码'"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input 
              v-model="formData.remark" 
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 管理认证按钮 -->
    <div class="auth-button-container" style="margin-top: 16px; text-align: left;">
      <el-button
        type="primary"
        size="default"
        @click="handleManageAuth"
      >
        管理认证相关信息
      </el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['partner-change', 'manage-auth'])

// 表单引用
const formRef = ref(null)

// 表单验证规则
const rules = computed(() => {
  const baseRules = {
    user_id: [
      { required: true, message: '请输入用户ID', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    mobile: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    email: [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
  }

  // 如果是合伙人，添加合伙人相关验证
  if (props.formData.is_partner) {
    baseRules.partner_id = [
      { required: true, message: '合伙人ID不能为空', trigger: 'blur' }
    ]
  }

  // 如果不是只读模式，添加密码验证
  if (!props.readonly) {
    if (!props.isEditMode) {
      // 新增模式，密码必填
      baseRules.password = [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少6位', trigger: 'blur' }
      ]
    } else {
      // 编辑模式，密码可选
      baseRules.password = [
        { min: 6, message: '密码长度至少6位', trigger: 'blur' }
      ]
    }

    // 确认密码验证
    baseRules.confirmPassword = [
      {
        validator: (_rule, value, callback) => {
          const password = props.formData.password || ''
          const confirmPwd = props.formData.confirmPassword || ''

          if (password && confirmPwd !== password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change']
      }
    ]
  }

  return baseRules
})



// 处理合伙人状态变化
const handlePartnerChange = (isPartner) => {
  if (!isPartner) {
    // 取消合伙人状态时，清空相关字段
    props.formData.partner_id = null
    props.formData.commission_base = null
    props.formData.commission_extra = null
  }
  emit('partner-change', isPartner)
}

// 处理管理认证按钮点击
const handleManageAuth = () => {
  emit('manage-auth')
}

// 表单验证方法
const validate = async () => {
  try {
    await formRef.value?.validate()
    
    // 额外验证确认密码
    if (!props.readonly && props.formData.password) {
      const password = props.formData.password || ''
      const confirmPwd = props.formData.confirmPassword || ''

      if (confirmPwd !== password) {
        throw new Error('两次输入的密码不一致')
      }
    }
    
    return true
  } catch (error) {
    if (typeof error === 'string') {
      ElMessage.error(error)
    }
    return false
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  formRef
})
</script>

<style scoped>
.header-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.partner-section {
  margin-top: 20px;
}

.password-section {
  margin-top: 20px;
}

.partner-section :deep(.el-divider__text) {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

.password-section :deep(.el-divider__text) {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
}
</style>
